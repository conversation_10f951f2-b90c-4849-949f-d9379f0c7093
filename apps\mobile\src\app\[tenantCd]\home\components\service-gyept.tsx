import { ButtonNone } from "@/components/button";
import { MenuItem } from "@/consts/menu-items";
import { convertNewlineToJSX } from "@toss/react";
import { Button } from "@workspace/ui/components/button";
import Image from "next/image";

export const GyeptServiceItem = ({
  title,
  imageUrl,
  description,
  buttonText,
  onPress,
  onButtonPress,
}: MenuItem) => {
  return (
    <ButtonNone asChild onClick={onPress}>
      <div className="flex h-[152px] flex-col items-center justify-between rounded-[10px] bg-white p-[10px]">
        <Image
          src={imageUrl}
          width={40}
          height={40}
          alt={title}
          className="mb-[8px] h-[40px] w-[40px] justify-center"
        />
        <div className="flex items-center justify-between text-[16px] font-semibold">
          {convertNewlineToJSX(title)}
        </div>
        <div className="line-clamp-3 flex-1 place-items-start text-ellipsis text-[12px] text-[#636F7A]">
          {description}
        </div>
        {buttonText && (
          <Button
            className="bg-secondary-local text-secondary-local-foreground w-full items-center justify-center transition-transform active:scale-95"
            variant="secondary"
            onClick={(e) => {
              e.stopPropagation();
              onButtonPress ? onButtonPress() : onPress ? onPress() : null;
            }}
          >
            {buttonText}
          </Button>
        )}
      </div>
    </ButtonNone>
  );
};

export const GyeptServiceList = ({ items }: { items: MenuItem[] }) => {
  return (
    <div className="grid grid-cols-2 gap-[12px]">
      {items.map((item, index) => (
        <GyeptServiceItem
          key={index}
          title={item.title}
          imageUrl={item.imageUrl}
          description={item.description}
          buttonText={item.buttonText}
          onPress={item.onPress}
          onButtonPress={item.onButtonPress}
        />
      ))}
    </div>
  );
};
