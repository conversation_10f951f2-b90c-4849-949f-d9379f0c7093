"use client";

import { useQueryPointBalance } from "@/actions/point/point-query";
import { useShowModalAlert } from "@workspace/ui/hooks/modal";
import { useEffect } from "react";
import { PointTitle, PointTitleCoin } from "../../components/common";
import { PointBalances } from "../../components/point-balance";

export const WalletExportContent = ({ tenantCd }: { tenantCd: string }) => {
  const { data, isError } = useQueryPointBalance();
  const showModalAlert = useShowModalAlert();

  useEffect(() => {
    if (!isError) return;
    showModalAlert({
      title: "오류",
      children: (
        <p className="text-[14px]">
          보유 포인트 정보 확인 중 오류가 발생하였습니다.
          <br />
          잠시 후 다시 시도해주세요.
        </p>
      ),
      options: { onClose: () => window.close() },
    });
    if (!isError) return;
  }, [isError, showModalAlert]);

  const totalBalance = data?.reduce((prev, item) => prev + item.balance, 0);
  return (
    <div>
      <PointTitle
        title="전환가능 포인트"
        titlePrefix={<PointTitleCoin />}
        balance={totalBalance}
      />
      <PointBalances tenantCd={tenantCd} data={data} />
    </div>
  );
};
