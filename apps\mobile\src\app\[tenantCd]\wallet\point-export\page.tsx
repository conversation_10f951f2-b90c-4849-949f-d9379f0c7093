import { MainContainer } from "@/components/main-container";
import { Metadata } from "next";
import { WalletExportContent } from "./components/export-content";
import { WalletExportMenu } from "./components/export-menus";

export const metadata: Metadata = {
  title: "포인트 내보내기",
};

interface WalletExportPageProps {
  params: Promise<{ tenantCd: string }>;
}

export default async function WalletPointExportPage({
  params,
}: Readonly<WalletExportPageProps>) {
  const { tenantCd } = await params;

  return (
    <MainContainer>
      <WalletExportContent tenantCd={tenantCd} />
      <WalletExportMenu tenantCd={tenantCd} />
    </MainContainer>
  );
}
