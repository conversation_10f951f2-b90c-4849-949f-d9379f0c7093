import { Menu, MenuItem } from "@/consts/menu-items";
import { Button } from "@workspace/ui/components/button";
import Image from "next/image";

export const ServiceItem = ({
  title,
  imageUrl,
  description,
  buttonText,
  onPress,
  onButtonPress,
}: MenuItem) => {
  return (
    <Button variant="none" asChild onClick={onPress}>
      <div className="shadow-gray flex flex-col justify-between rounded-[10px] bg-white px-[16px] py-[12px] transition-transform active:scale-[0.97]">
        <div className="flex items-center justify-between">
          <div>
            <div className="mb-[4px] text-[16px] font-semibold">{title}</div>
            <div className="line-clamp-3 flex-1 place-items-start text-ellipsis text-[12px] text-[#636F7A]">
              {description}
            </div>
          </div>
          <Image
            src={imageUrl}
            width={40}
            height={40}
            alt={title}
            className="ml-[10px]"
          />
        </div>
        {buttonText && (
          <div className="mt-[8px] flex justify-end">
            <Button
              className="h-[26px] w-[130px] items-center justify-center rounded-[6px] p-[5px] transition-transform active:scale-95"
              variant="secondary"
              onClick={(e) => {
                e.stopPropagation();
                onButtonPress ? onButtonPress() : onPress ? onPress() : null;
              }}
            >
              <span className="text-[#2DA5FF]">{buttonText}</span>
            </Button>
          </div>
        )}
      </div>
    </Button>
  );
};

export const ServiceList = ({ data }: { data: Menu }) => {
  const { title, items } = data;

  return (
    <div>
      {title ? (
        <div className="mb-[8px] mt-[20px] text-[18px] font-semibold">
          {title}
        </div>
      ) : null}
      <div className="flex flex-col gap-[12px]">
        {items.map((item, index) => (
          <ServiceItem
            key={index}
            title={item.title}
            imageUrl={item.imageUrl}
            description={item.description}
            buttonText={item.buttonText}
            onPress={item.onPress}
            onButtonPress={item.onButtonPress}
          />
        ))}
      </div>
    </div>
  );
};
