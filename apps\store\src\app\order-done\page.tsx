import { SummaryItem } from "@/components/common/summary";
import { MainContainer } from "@/components/main-container";
import { Button } from "@workspace/ui/components/button";
import Link from "next/link";
import { OrderStep, OrderTitle } from "../order/[id]/order-info";

export default function OrderDonePage() {
  // TODO: 실제 주문 정보로 교체

  return (
    <MainContainer className="py-[20px]" gradient={false}>
      <OrderStep step="done" />
      <div className="my-[20px] flex flex-col gap-[10px]">
        <OrderTitle>주문정보확인</OrderTitle>
        <OrderTitle>주문정보</OrderTitle>
        <SummaryItem title="주문명">
          CITYCUBE 모바일 상품권 교환 서비스
        </SummaryItem>
        <SummaryItem title="상품명">---</SummaryItem>
        <SummaryItem title="판매가">{1234} P</SummaryItem>
        <SummaryItem title="유효기간">
          ??일/유효기간 만료 후 연장 및 환불 불가
        </SummaryItem>

        <OrderTitle>수신자 정보</OrderTitle>
        <SummaryItem title="수신자명">홍길동</SummaryItem>
        <SummaryItem title="수신번호">010-1234-5678</SummaryItem>

        <OrderTitle>결제정보</OrderTitle>
        <SummaryItem title="상품금액">
          <span className="font-bold">{1234}</span> P
        </SummaryItem>
        <SummaryItem title="포인트 결제금액">
          <span className="font-bold">{1234}</span> P
        </SummaryItem>
        <SummaryItem title="수량">1</SummaryItem>
      </div>
      <div className="my-[20px] h-[4px] bg-gray-200" />

      <div className="flex flex-col gap-[4px] rounded-sm bg-[#f5f5f5] p-[20px]">
        <div>
          해당 상품이 정상적으로 결제되었습니다. 이용해주셔서 감사합니다.
        </div>
        <div className="text-[#256bff]">
          구매하신 상품은 기프티쇼 비즈 이용약관 및 구매 약정 동의에 따라 발송
          완료 후 기간 연장 및 환불이 불가합니다.
        </div>
      </div>
      <div className="my-[20px]">
        <Link href="/" replace>
          <Button className="h-[52px] w-full rounded-[10px] text-[16px] font-semibold shadow-lg transition-transform active:scale-[0.97]">
            확인
          </Button>
        </Link>
      </div>
    </MainContainer>
  );
}
