import { boolean, integer, pgTable, varchar } from "drizzle-orm/pg-core";
import { timestampString } from "../types";

export const petOwners = pgTable("tb_pets_owner", {
  uid: integer("uid").primaryKey().generatedByDefaultAsIdentity(),
  userDid: varchar("user_did", { length: 255 }).notNull(),
  ownCode: varchar("own_code", { length: 64 }).notNull(),
  isValid: varchar("val_yn", { length: 1, enum: ["Y", "N"] })
    .default("Y")
    .notNull(),
  regDate: timestampString("reg_dt").defaultNow().notNull(),
  updDate: timestampString("upd_dt")
    .defaultNow()
    .notNull()
    .$onUpdate(() => new Date().toISOString()),
});

export const petInfos = pgTable("tb_pets_info", {
  uid: integer("uid").primaryKey().generatedByDefaultAsIdentity(),
  userDid: varchar("user_did", { length: 255 }).notNull(),
  qrpIdx: integer("qrp_idx").notNull(),
  ownName: varchar("own_name", { length: 64 }).notNull(),
  petRegNo: varchar("pet_reg_no", { length: 15 }).notNull(),
  petName: varchar("pet_name", { length: 64 }).notNull(),
  petBirth: varchar("pet_birth", { length: 64 }).notNull(),
  petWeight: varchar("pet_weight", { length: 64 }).notNull(),
  petType: varchar("pet_type", { length: 1, enum: ["D", "C"] }).notNull(),
  petBreed: varchar("pet_breed", { length: 64 }).notNull(),
  petBreedCode: varchar("pet_breed_code", { length: 64 }).notNull(),
  petBreedId: varchar("pet_breed_id", { length: 64 }).notNull(),
  petIsFierce: boolean("pet_is_fierce").notNull(),
  petHairColorCode: varchar("pet_hair_color_code", { length: 64 }).notNull(),
  petGender: varchar("pet_gender", { length: 64 }).notNull(),
  petNeuterYn: varchar("pet_neuter_yn", { length: 64 }).notNull(),
  petVaccinateYn: varchar("pet_vaccinate_yn", { length: 64 }).notNull(),
  petRabiesVirusYn: varchar("pet_rabies_virus_yn", { length: 64 }).notNull(),
  petSizeType: varchar("pet_size_type", {
    length: 1,
    enum: ["S", "M", "L"],
  }).notNull(),
  petOffice: varchar("pet_office", { length: 64 }).notNull(),
  relationshipOwn: integer("relationship_own").notNull(),
  isValid: varchar("val_yn", { length: 1, enum: ["Y", "N"] })
    .default("Y")
    .notNull(),
  regDate: timestampString("reg_dt").defaultNow().notNull(),
  updDate: timestampString("upd_dt")
    .defaultNow()
    .notNull()
    .$onUpdate(() => new Date().toISOString()),
});

export const petQrEntries = pgTable("tb_pets_qr_entry", {
  uid: integer("uid").primaryKey().generatedByDefaultAsIdentity(),
  userDid: varchar("user_did", { length: 255 }).notNull(),
  qrpIdx: integer("qrp_idx").notNull(),
  ownName: varchar("own_name", { length: 64 }).notNull(),
  petName: varchar("pet_name", { length: 64 }).notNull(),
  accompanyingCount: integer("accompanying_count").notNull(),
  petSizeType: varchar("pet_size_type", {
    length: 1,
    enum: ["S", "M", "L"],
  }).notNull(),
  qrText: varchar("qr_text", { length: 128 }).notNull(),
  isValid: varchar("val_yn", { length: 1, enum: ["Y", "N"] })
    .default("Y")
    .notNull(),
  regDate: timestampString("reg_dt").defaultNow().notNull(),
  updDate: timestampString("upd_dt")
    .defaultNow()
    .notNull()
    .$onUpdate(() => new Date().toISOString()),
});
