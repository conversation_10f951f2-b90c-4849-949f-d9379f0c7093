services:
  setup:
    container_name: setup
    image: ghcr.io/data-alliance/cw-web-apps/setup:latest
    platform: linux/amd64
    extra_hosts:
      - "host.docker.internal:host-gateway"
    env_file:
      - .env.production
  admin:
    container_name: admin
    image: ghcr.io/data-alliance/cw-web-apps/admin:latest
    platform: linux/amd64
    restart: unless-stopped
    extra_hosts:
      - "host.docker.internal:host-gateway"
    ports:
      - 40000:3000
    env_file:
      - .env.production
    depends_on:
      setup:
        condition: service_completed_successfully
  mobile:
    container_name: mobile
    image: ghcr.io/data-alliance/cw-web-apps/mobile:latest
    platform: linux/amd64
    restart: unless-stopped
    extra_hosts:
      - "host.docker.internal:host-gateway"
    ports:
      - 40010:3000
    env_file:
      - .env.production
    depends_on:
      setup:
        condition: service_completed_successfully
  living-lab:
    container_name: living-lab
    image: ghcr.io/data-alliance/cw-web-apps/living-lab:latest
    platform: linux/amd64
    restart: unless-stopped
    extra_hosts:
      - "host.docker.internal:host-gateway"
    ports:
      - 40020:3000
    env_file:
      - .env.production
    depends_on:
      setup:
        condition: service_completed_successfully
  store:
    container_name: store
    image: ghcr.io/data-alliance/cw-web-apps/store:latest
    platform: linux/amd64
    restart: unless-stopped
    extra_hosts:
      - "host.docker.internal:host-gateway"
    ports:
      - 40030:3000
    env_file:
      - .env.production
    depends_on:
      setup:
        condition: service_completed_successfully
