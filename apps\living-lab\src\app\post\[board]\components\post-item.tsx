"use client";

import { PostSuggestItem } from "@/components/post-suggest/post-suggest-item";
import { useGlobalStore } from "@/lib/store/store-provider";
import { Post, PostFile } from "@workspace/db/schema/living-lab/post";
import { getImageURL } from "@workspace/utils/files";
import { useShallow } from "zustand/shallow";

export const PostItem = ({
  item,
  className,
}: {
  item: Post & { files: PostFile[] };
  className?: string;
}) => {
  const baseURL = useGlobalStore(useShallow((state) => state.baseURL));
  const imageURL =
    item.files.length > 0 ? getImageURL(baseURL, item.files[0].uri) : null;

  return (
    <PostSuggestItem
      href={`/post/${item.board}/${item.uid}`}
      category={item.category}
      title={item.title}
      imageURL={imageURL}
      hitCount={item.hitCount}
      regDate={item.regDate}
      stats={null}
      className={className}
    />
  );
};
