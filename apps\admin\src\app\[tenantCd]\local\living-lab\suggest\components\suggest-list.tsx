"use client";

import { Pagination } from "@/components/pagination";
import { parseTenantPath } from "@/lib/tenant";
import { Suggest } from "@workspace/db/crud/living-lab/suggest";
import { usePathname, useRouter, useSearchParams } from "next/navigation";
import { SuggestTable } from "./suggest-table";

export const SuggestList = ({
  data,
  count,
  pageNumber,
  pageSize,
}: {
  data: Suggest[];
  count: number;
  pageNumber: number;
  pageSize: number;
}) => {
  const router = useRouter();
  const pathname = usePathname();
  const [tenantId] = parseTenantPath(pathname);
  const searchParams = useSearchParams();

  const handlePageChange = (page: number) => {
    const params = new URLSearchParams(searchParams);
    params.set("page", page.toString());
    router.push(pathname + "?" + params.toString());
  };
  return (
    <>
      <Pagination
        count={count}
        page={pageNumber}
        pageSize={pageSize}
        onPageChange={handlePageChange}
        className="mb-[4px]"
      />
      <SuggestTable tenantId={tenantId} data={data} />
    </>
  );
};
