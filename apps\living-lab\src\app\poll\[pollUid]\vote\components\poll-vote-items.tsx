"use client";

import { VoteType } from "@/app/poll/store";
import { usePollStore } from "@/app/poll/store-provider";
import { ImageFiles } from "@/app/suggest/components/common";
import { BottomButton } from "@/components/common/bottom-button";
import { useGlobalStore } from "@/lib/store/store-provider";
import { PollFile, PollItem } from "@workspace/db/crud/living-lab/poll";
import { Checkbox } from "@workspace/ui/components/checkbox";
import { Label } from "@workspace/ui/components/label";
import {
  RadioGroup,
  RadioGroupItem,
} from "@workspace/ui/components/radio-group";
import { useEffect } from "react";
import { useShallow } from "zustand/shallow";

type PollItemDto = PollItem & { files: PollFile[] };

const PollItemComponent = ({
  type,
  item,
  checked,
  onClick,
}: {
  type: VoteType;
  item: PollItemDto;
  checked: boolean;
  onClick: (uid: number, checked: boolean) => void;
}) => {
  const baseURL = useGlobalStore(useShallow((state) => state.baseURL));

  return (
    <div className="py-[20px]">
      <div className="mb-[14px] flex items-center space-x-2">
        {type === "multiple" ? (
          <Checkbox
            value={item.uid.toString()}
            id={item.uid.toString()}
            checked={checked}
            onClick={() => onClick(item.uid, !checked)}
          />
        ) : (
          <RadioGroupItem
            value={item.uid.toString()}
            id={item.uid.toString()}
            onClick={() => onClick(item.uid, !checked)}
          />
        )}
        <Label
          htmlFor={item.uid.toString()}
          className="text-[14px] font-medium"
        >
          {item.name}
        </Label>
      </div>
      <div className="ml-[24px] text-[16px]">
        <ImageFiles baseURL={baseURL} files={item.files} />
        {item.content}
      </div>
    </div>
  );
};

export const PollItemsComponent = ({
  pollItems,
  type,
}: {
  pollItems: PollItemDto[];
  type: VoteType;
}) => {
  const [selectedItem, setVoteType, setSelectedItem] = usePollStore(
    useShallow((state) => [
      state.selectedItem,
      state.setVoteType,
      state.setSelectedItem,
    ]),
  );
  useEffect(() => {
    console.log("type", type);
    setVoteType(type);
  }, [type]);

  return (
    <RadioGroup>
      <div className="flex flex-col divide-y-[1px] border-[#F5F5F5]">
        {pollItems.map((item, index) => (
          <PollItemComponent
            key={index}
            item={item}
            type={type}
            checked={selectedItem.has(item.uid)}
            onClick={setSelectedItem}
          />
        ))}
      </div>
    </RadioGroup>
  );
};

export const PollVoteButton = ({ pollUid }: { pollUid: number }) => {
  const [selectedItem, setStep, setPollUid] = usePollStore(
    useShallow((state) => [
      state.selectedItem,
      state.setStep,
      state.setPollUid,
    ]),
  );

  return (
    <BottomButton
      disabled={selectedItem.size === 0}
      onClick={() => {
        setPollUid(pollUid);
        setStep("sign");
      }}
    >
      투표하기
    </BottomButton>
  );
};
