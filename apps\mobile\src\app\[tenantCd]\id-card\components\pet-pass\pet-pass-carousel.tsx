import {
  Carousel,
  CarouselApi,
  CarouselContent,
  CarouselItem,
} from "@workspace/ui/components/carousel";
import { cn } from "@workspace/ui/lib/utils";
import { EmblaCarouselType, EmblaEventType } from "embla-carousel";
import { ReactNode, useCallback, useEffect, useRef, useState } from "react";
import styles from "./pet-pass-carousel.module.css";

const TWEEN_FACTOR_BASE = 0.2;
const numberWithinRange = (number: number, min: number, max: number): number =>
  Math.min(Math.max(number, min), max);

export const PetPassCarousel = ({
  className,
  onSelectSnap,
  children,
}: {
  className?: string;
  onSelectSnap?: (index: number, maxCount: number) => void;
  children: ReactNode | ReactNode[];
}) => {
  const [api, setApi] = useState<CarouselApi>();

  const tweenFactor = useRef(0);
  const tweenNodes = useRef<HTMLElement[]>([]);

  const setTweenNodes = useCallback((emblaApi: EmblaCarouselType): void => {
    tweenNodes.current = emblaApi.slideNodes().map((slideNode) => {
      //console.log(slideNode);
      //return slideNode.querySelector(".embla__slide__number") as HTMLElement;
      return slideNode as HTMLElement;
    });
  }, []);

  const setTweenFactor = useCallback((emblaApi: EmblaCarouselType) => {
    tweenFactor.current = TWEEN_FACTOR_BASE * emblaApi.scrollSnapList().length;
  }, []);

  const setSnapState = useCallback((emblaApi: EmblaCarouselType) => {
    onSelectSnap?.(
      emblaApi.selectedScrollSnap(),
      emblaApi.scrollSnapList().length,
    );
  }, []);

  const tweenScale = useCallback(
    (emblaApi: EmblaCarouselType, eventName?: EmblaEventType) => {
      const engine = emblaApi.internalEngine();
      const scrollProgress = emblaApi.scrollProgress();
      const slidesInView = emblaApi.slidesInView();
      const isScrollEvent = eventName === "scroll";

      emblaApi.scrollSnapList().forEach((scrollSnap, snapIndex) => {
        let diffToTarget = scrollSnap - scrollProgress;
        const slidesInSnap = engine.slideRegistry[snapIndex];

        slidesInSnap.forEach((slideIndex) => {
          if (isScrollEvent && !slidesInView.includes(slideIndex)) return;

          if (engine.options.loop) {
            engine.slideLooper.loopPoints.forEach((loopItem) => {
              const target = loopItem.target();

              if (slideIndex === loopItem.index && target !== 0) {
                const sign = Math.sign(target);

                if (sign === -1) {
                  diffToTarget = scrollSnap - (1 + scrollProgress);
                }
                if (sign === 1) {
                  diffToTarget = scrollSnap + (1 - scrollProgress);
                }
              }
            });
          }

          const tweenValue = 1 - Math.abs(diffToTarget * tweenFactor.current);
          const scale = numberWithinRange(tweenValue, 0, 1).toString();
          const tweenNode = tweenNodes.current[slideIndex];
          tweenNode.style.transform = `scale(${scale})`;
          tweenNode.style.opacity = scale;
          tweenNode.style.transformOrigin =
            diffToTarget < 0 ? "center right" : "center left";
        });
      });
    },
    [],
  );

  useEffect(() => {
    if (!api) return;
    setTweenNodes(api);
    setTweenFactor(api);
    setSnapState(api);
    tweenScale(api);
    api
      .on("reInit", setTweenNodes)
      .on("reInit", setTweenFactor)
      .on("reInit", setSnapState)
      .on("reInit", tweenScale)
      .on("scroll", tweenScale)
      .on("select", setSnapState)
      .on("slideFocus", tweenScale);
  }, [api, tweenScale]);

  return (
    <Carousel className={cn(styles.petPassCarousel, "w-full")} setApi={setApi}>
      <CarouselContent
        className={cn(styles.petPassCarousel__Container, className)}
      >
        {Array.isArray(children) ? (
          children.map((node, index) => (
            <CarouselItem
              key={index}
              className={cn(styles.petPassCarousel__Item, "")}
            >
              <div className="m-[5px]">{node}</div>
            </CarouselItem>
          ))
        ) : (
          <CarouselItem className={cn(styles.petPassCarousel__Item, "")}>
            <div className="m-[5px]">{children}</div>
          </CarouselItem>
        )}
      </CarouselContent>
    </Carousel>
  );
};
