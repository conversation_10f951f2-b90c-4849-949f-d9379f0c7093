"use client";

import { <PERSON><PERSON><PERSON>one, PrimaryButton } from "@/components/button";
import { zodResolver } from "@hookform/resolvers/zod";
import { Checkbox } from "@workspace/ui/components/checkbox";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
} from "@workspace/ui/components/form";
import { Input } from "@workspace/ui/components/input";
import {
  She<PERSON>,
  SheetContent,
  SheetTitle,
} from "@workspace/ui/components/sheet";
import { Skeleton } from "@workspace/ui/components/skeleton";
import { cn } from "@workspace/ui/lib/utils";
import Image from "next/image";
import { useCallback, useState } from "react";
import { useForm } from "react-hook-form";
import { useWindowSize } from "usehooks-ts";
import { z } from "zod";
import {
  useMutationQrEntry,
  useQueryPets,
  useQueryQrEntries,
} from "../../actions/pet-query";
import { PetPassPetDto } from "../../actions/pet-types";
import { PetQrEntrySheet } from "./pet-pass-qr-entry-sheet";

const formSchema = z.object({
  accompanyingCount: z.string().min(1, {
    message: "동반 인원수는 1명 이상이어야 합니다.",
  }),
  qrpIds: z.array(z.number()).min(1, {
    message: "동반 반려동물은 1마리 이상이어야 합니다.",
  }),
});

const PetItem = ({
  data,
  checked,
  onCheckedChange,
}: {
  data: PetPassPetDto;
  checked: boolean;
  onCheckedChange: (checked: boolean) => void;
}) => {
  return (
    <div
      className={cn(
        "mt-[10px] flex items-center rounded-[6px] border-[1px] p-[20px]",
        "transition-transform active:scale-[0.98]",
        checked ? "border-primary" : "border-[#e1e4eb]",
      )}
      onClick={() => onCheckedChange(!checked)}
    >
      <div className="flex flex-row items-center">
        <Checkbox checked={checked} onCheckedChange={onCheckedChange} />
        <div className="ml-[12px]">
          <div className="text-[18px] font-semibold text-[#000]">
            {data.petName}
          </div>
          <div className="text-[14px] text-[#808C98]">
            {data.petBreed}
            <span className="ml-[10px]">{data.petRegNo}</span>
          </div>
        </div>
      </div>
    </div>
  );
};

const PetPassQrEntryRegist = ({
  petData,
  onSubmit,
}: {
  petData: PetPassPetDto[];
  onSubmit: (data: z.infer<typeof formSchema>) => void;
}) => {
  const size = useWindowSize();
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    mode: "onChange",
    defaultValues: {
      accompanyingCount: "1",
      qrpIds: [],
    },
  });

  return (
    <SheetContent side="bottom" className="rounded-t-[16px]">
      <Form {...form}>
        <SheetTitle className="text-[20px] font-medium">
          QR 발급 정보 등록
        </SheetTitle>
        <div
          className="hide-scrollbar px-[2px] py-[20px]"
          style={{
            maxHeight: size.height ? size.height - 150 : undefined,
            overflowY: "auto",
          }}
        >
          <div className="text-[14px] text-[#373E44]">
            반려동물 출입 정보를 등록해주세요.
          </div>
          <div className="mb my-[10px] text-[16px] font-semibold text-[#222222]">
            동반 인원수
          </div>
          <div>
            <FormField
              control={form.control}
              name="accompanyingCount"
              render={({ field }) => (
                <FormItem>
                  <FormControl>
                    <Input
                      {...field}
                      onChange={(e) => {
                        const value = e.target.value;
                        if (value === "" || /^\d+$/.test(value)) {
                          field.onChange(value);
                        }
                      }}
                      onBeforeInput={(
                        e: React.FormEvent<HTMLInputElement> & {
                          nativeEvent: InputEvent;
                        },
                      ) => {
                        const data = e.nativeEvent.data;
                        if (data === null) return;
                        if (!/^\d$/.test(data)) e.preventDefault();
                      }}
                      placeholder="동반 인원수를 입력해주세요"
                      type="text"
                      inputMode="numeric"
                      pattern="\d*"
                      step="1"
                    />
                  </FormControl>
                </FormItem>
              )}
            />
          </div>
          <div className="mb my-[10px] text-[16px] font-semibold text-[#222222]">
            동반 반려동물 선택
          </div>
          <div>
            {petData?.map((item, index) => (
              <FormField
                key={index}
                control={form.control}
                name="qrpIds"
                render={({ field }) => (
                  <FormItem>
                    <FormControl>
                      <PetItem
                        data={item}
                        checked={field.value.includes(item.qrpIdx)}
                        onCheckedChange={(checked) => {
                          console.log(field.value, item.qrpIdx);
                          if (checked) {
                            field.onChange([...field.value, item.qrpIdx]);
                          } else {
                            field.onChange(
                              field.value.filter((id) => id !== item.qrpIdx),
                            );
                          }
                        }}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
            ))}
          </div>
          <div className="pt-[20px]">
            <PrimaryButton
              disabled={form.formState.isSubmitting || !form.formState.isValid}
              onClick={form.handleSubmit(onSubmit)}
            >
              확인
            </PrimaryButton>
          </div>
        </div>
      </Form>
    </SheetContent>
  );
};

export const PetQrEntry = () => {
  const { data: qrData, isLoading: isLoadingQr } = useQueryQrEntries();
  const { data, isLoading: isLoadingPets } = useQueryPets();
  const { mutateAsync } = useMutationQrEntry();

  const [isModalVisible, setModalVisible] = useState(false);
  const [showModify, setShowModify] = useState(false);

  const isLoading = isLoadingQr || isLoadingPets;
  const petData = data?.data;

  const handleModify = useCallback(() => {
    setShowModify(true);
  }, []);

  const handleEntrySubmit = useCallback(
    async (data: z.infer<typeof formSchema>) => {
      await mutateAsync({
        qrpIds: data.qrpIds,
        accompanyingCount: Number(data.accompanyingCount),
      });
      setShowModify(false);
    },
    [mutateAsync],
  );

  const handleOpenChange = useCallback((open: boolean) => {
    setModalVisible(open);
    if (!open) {
      setShowModify(false);
    }
  }, []);

  if (isLoading) return <Skeleton className="h-[100px] w-[100px]" />;

  return (
    <Sheet modal={true} open={isModalVisible} onOpenChange={handleOpenChange}>
      <ButtonNone variant="none" onClick={() => setModalVisible(true)}>
        <Image
          src={"/assets/id-card/qr-code.png"}
          width={100}
          height={100}
          alt="QR 코드"
        />
      </ButtonNone>
      {!showModify && qrData?.data && qrData.data.length > 0 ? (
        <PetQrEntrySheet
          data={qrData.data}
          onModify={handleModify}
          onClose={() => handleOpenChange(false)}
        />
      ) : (
        petData && (
          <PetPassQrEntryRegist
            petData={petData}
            onSubmit={handleEntrySubmit}
          />
        )
      )}
    </Sheet>
  );
};
