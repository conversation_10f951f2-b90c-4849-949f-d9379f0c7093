import { NextRequest, NextResponse } from "next/server";
import { homeHeaders, ImageInfo } from "./types";

type Action = {
  imageUrl: string;
  actionUrl: string;
};

type Metadata = {
  metadataVersion: number;
  requiredAppVersion: string;
  tenantCd: string;
  homeHeader: ImageInfo;
};

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ tenantCd: string }> },
) {
  // TODO: DB에서 읽어오도록 변경
  const { tenantCd } = await params;
  const metaResult: Metadata = {
    metadataVersion: 1,
    requiredAppVersion: "1.0.0",
    tenantCd,
    homeHeader: homeHeaders[tenantCd] || homeHeaders["main"],
  };

  return NextResponse.json(metaResult);
}
