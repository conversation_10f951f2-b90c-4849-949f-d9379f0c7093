import Link from "next/link";

export const Breadcrumb = ({
  categoryText,
  searchTag,
  title,
}: {
  categoryText?: string;
  searchTag?: string;
  title: string | undefined;
}) => {
  const keyword = searchTag ? `?keyword=${searchTag}` : "";
  return (
    <div className="my-[10px] flex items-center justify-between">
      <h2 className="text-base font-bold text-[#292929]">
        <Link
          className="cursor-pointer transition-colors hover:text-blue-600"
          href="/"
          replace
        >
          전체
        </Link>
        {categoryText && categoryText.length > 0 && (
          <>
            {" > "}
            <Link
              className="cursor-pointer transition-colors hover:text-blue-600"
              href={`/products/${categoryText}${keyword}`}
            >
              {categoryText}
            </Link>
          </>
        )}
        {title && " > " + title}
        {searchTag && (
          <span>
            {" / "}
            {searchTag}
          </span>
        )}
      </h2>
    </div>
  );
};
