{"name": "living-lab", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --port 3011", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@hookform/resolvers": "catalog:", "@tanstack/react-query": "^5.59.19", "@tanstack/react-query-devtools": "^5.59.20", "@toss/react": "^1.8.1", "@toss/utils": "^1.6.1", "@webview-bridge/web": "^1.7.7", "@workspace/db": "workspace:*", "@workspace/ui": "workspace:*", "@workspace/utils": "workspace:*", "class-variance-authority": "^0.7.0", "dayjs": "^1.11.13", "lucide-react": "^0.454.0", "next": "catalog:next", "react": "catalog:react19", "react-dom": "catalog:react19", "react-hook-form": "catalog:", "server-only": "^0.0.1", "signature_pad": "^5.0.4", "zod": "catalog:", "zustand": "^5.0.1"}, "devDependencies": {"@svgr/webpack": "^8.1.0", "@tanstack/eslint-plugin-query": "^5.59.7", "@types/node": "^20", "@types/react": "catalog:react19", "@types/react-dom": "catalog:react19", "@workspace/eslint-config": "workspace:*", "@workspace/typescript-config": "workspace:*", "autoprefixer": "^10.4.20", "eslint": "catalog:", "postcss": "catalog:", "tailwindcss": "catalog:", "typescript": "catalog:"}, "browserslist": "defaults"}