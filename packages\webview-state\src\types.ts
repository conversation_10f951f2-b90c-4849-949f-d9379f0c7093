import { type Bridge } from "@webview-bridge/types";

export type NavigateUrl =
  | `cw.app://${string}`
  | `cw.mobile://${string}`
  | `cw.living-lab://${string}`
  | `cw.store://${string}`
  | `http://${string}`
  | `https://${string}`;

export type NavigateParams = Record<
  string,
  string | number | undefined | null | (string | number)[]
>;

/**
 * 네비게이션 옵션
 * @property webViewId - webViewId가 표시중인 webViewId와 동일한 경우 표시중인 웹뷰를 replace한다.
 */
export type NavigateOptions = {
  title?: string;
  hideHeader: boolean;
  disableSwipeBack: boolean;
  delegateBackButton?: boolean;
  requiredNonce?: boolean;
  webViewId?: string;
};

export type NavigateRequest = {
  url: NavigateUrl;
  title?: string;
  params?: NavigateParams;
  options?: NavigateOptions;
};

export type NavigateData = NavigateRequest & {
  dismissTo?: boolean;
};

export type AppInsetsType = {
  top: number;
  bottom: number;
  left: number;
  right: number;
};

export type UserData = {
  name: string;
  birth: string;
  address: string | null;
};

export type UserApprovalRequestData = {
  type: "payment";
  payload: string;
};

export type UserApprovalResponseData = {
  type: "payment";
  payload: string;
  signature?: string;
};

export interface WebViewState extends Bridge {
  appPlatform: string;
  appVersion: string;
  appDist: number;

  userData: UserData | null;

  insets: AppInsetsType | null;
  paddingBottom: number | null;
  todaySteps: number | null | undefined;

  webViewTitle: Record<string, string>;

  navigateData: NavigateData | null;

  setInsets: (insets: AppInsetsType) => Promise<void>;
  setPaddingBottom: (bottom: number) => Promise<void>;

  revokeVc: () => Promise<void>;
  refreshUserData: () => Promise<UserData>;

  requestApproval: (
    data: UserApprovalRequestData,
  ) => Promise<UserApprovalResponseData>;

  setTodaySteps: (count: number | null) => Promise<void>;
  setWebViewTitle: (url: string, title: string) => Promise<void>;
  navigate: (request: NavigateRequest) => Promise<void>;
  dismissTo: (request: NavigateRequest, reload?: boolean) => Promise<void>;
  navigateReset: () => Promise<void>;
}
