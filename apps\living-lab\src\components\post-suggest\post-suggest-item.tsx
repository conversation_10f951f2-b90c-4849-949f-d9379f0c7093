import { formatAsDate } from "@/lib/datetime";
import Link from "next/link";
import { ReactNode } from "react";

export const PostSuggestItem = ({
  href,
  category,
  title,
  imageURL,
  hitCount,
  regDate,
  stats,
  className,
}: {
  href: string;
  category: string;
  title: string;
  imageURL: string | null;
  hitCount: number;
  regDate: string;
  stats: ReactNode;
  className?: string;
}) => {
  return (
    <div className={className}>
      <Link href={href}>
        <div className="flex justify-between">
          <div>
            <div className="mb-[6px] text-[12px] font-medium text-[#636F7A]">
              {category}
            </div>
            <div className="mb-[16px] text-[16px] font-medium">{title}</div>
            <div className="text-[12px]">
              <span className="text-gray-500">작성일</span>{" "}
              {formatAsDate(regDate)}
            </div>
          </div>
          {imageURL && (
            <div className="flex h-[65px] w-[65px] items-center justify-center bg-gray-200">
              {
                // eslint-disable-next-line @next/next/no-img-element
                <img
                  src={imageURL}
                  alt="이미지"
                  className="h-[65px] w-[65px] rounded-lg object-cover"
                />
              }
            </div>
          )}
        </div>
        <div className="mt-4 flex justify-between">
          {stats ? stats : <div />}
          <div className="text-[10px]">
            <span className="mr-[3px] text-[#808C98]">조회수</span> {hitCount}
          </div>
        </div>
      </Link>
    </div>
  );
};
