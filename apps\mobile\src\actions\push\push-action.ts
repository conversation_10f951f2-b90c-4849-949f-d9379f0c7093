"use server";

import { platformPushApi } from "../platform-api";

export type IndividualMessage = {
  agentMsgId: string;
  messageBody: string;
  messageTitle: string;
  readCheck: boolean;
  readDate: string | null;
  regDate: string;
};

export type IndividualMessageHistory = {
  currentPage: number;
  pageScale: number;
  totalCount: number;
  individualMessageList: IndividualMessage[];
};

export const fetchPushHistory = async (page: number, scale: number = 10) => {
  const { data } = await platformPushApi.post<IndividualMessageHistory>(
    "/api/service/message/individual/search",
    {
      projectId: "da-citywallet",
      page,
      scale,
    },
  );
  return data;
};

export const markAllMessagesAsRead = async () => {
  const { data } = await platformPushApi.post("/api/service/message/readAll");
  return data;
};
