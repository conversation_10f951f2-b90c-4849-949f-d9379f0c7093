"use client";

import { format } from "date-fns";
import { ko } from "date-fns/locale";
import { CalendarIcon } from "lucide-react";
import { useState } from "react";
import { Matcher } from "react-day-picker";
import { ControllerRenderProps, FieldPath, FieldValues } from "react-hook-form";
import { cn } from "../../lib/utils";
import { Button } from "../button";
import { Calendar } from "../calendar";
import { FormControl } from "../form";
import { Popover, PopoverContent, PopoverTrigger } from "../popover";

export const DatePickerForm = <TFieldValues extends FieldValues>({
  field,
  placeholder,
  disabled,
  className,
}: {
  field: ControllerRenderProps<TFieldValues, FieldPath<TFieldValues>>;
  placeholder?: string;
  disabled?: Matcher | Matcher[] | undefined;
  className?: string;
}) => {
  const [open, setOpen] = useState(false);
  const onSelect = (date: Date | undefined) => {
    field.onChange(date);
    setOpen(false);
  };
  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <FormControl>
          <Button
            variant={"outline"}
            className={cn(
              "pl-3 text-left font-normal",
              className,
              !field.value && "text-muted-foreground",
            )}
          >
            {field.value ? (
              format(field.value, "yyyy.MM.dd")
            ) : (
              <span>{placeholder}</span>
            )}
            <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
          </Button>
        </FormControl>
      </PopoverTrigger>
      <PopoverContent className="w-auto p-0" align="start">
        <Calendar
          mode="single"
          selected={field.value}
          onSelect={onSelect}
          disabled={disabled}
          initialFocus
          locale={ko}
          formatters={{ formatCaption: (date) => format(date, "yyyy년 MM월") }}
          defaultMonth={field.value}
        />
      </PopoverContent>
    </Popover>
  );
};

export const DatePicker = ({
  date,
  onChange,
  placeholder = "날짜를 선택해주세요",
  fromDate,
  toDate,
  className,
}: {
  date: Date | undefined;
  onChange: (date: Date | undefined) => void;
  fromDate?: Date;
  toDate?: Date;
  placeholder?: string;
  className?: string;
}) => {
  const [open, setOpen] = useState(false);
  const onSelect = (selectedDate: Date | undefined) => {
    onChange(selectedDate);
    setOpen(false);
  };

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          data-empty={!date}
          className={cn(
            "data-[empty=true]:text-muted-foreground min-w-[140px] justify-start text-left font-normal",
            className,
          )}
        >
          <CalendarIcon />
          {date ? format(date, "yyyy.MM.dd") : <span>{placeholder}</span>}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-auto p-0">
        <Calendar
          mode="single"
          selected={date}
          onSelect={onSelect}
          locale={ko}
          formatters={{ formatCaption: (date) => format(date, "yyyy년 MM월") }}
          fromDate={fromDate}
          toDate={toDate}
          defaultMonth={date}
          initialFocus
        />
      </PopoverContent>
    </Popover>
  );
};
