"use client";

import { useQueryPedometerRecordsMonthly } from "@/actions/pedometer/pedometer-query";
import { commaizeNumber } from "@toss/utils";
import { Skeleton } from "@workspace/ui/components/skeleton";
import { getMonthName } from "@workspace/utils/datetime";
import { format, parse, subMonths } from "date-fns";
import { useState } from "react";
import { PedometerCalendar } from "./calendar";
import { PedometerChart } from "./pedometer-chart";

export const Summary = ({
  avgStepCount,
}: {
  avgStepCount: number | undefined;
}) => {
  return (
    <div className="flex-col p-[10px] text-[24px] font-medium text-[#636F7A]">
      이번 달 평균 걸음수는
      <br />
      <span className="inline-block font-bold text-[#000000]">
        {avgStepCount !== undefined ? (
          commaizeNumber(avgStepCount)
        ) : (
          <Skeleton className="inline-block h-[20px] w-[60px]" />
        )}{" "}
        보
      </span>
      입니다.
    </div>
  );
};

export const PedometerMonthly = () => {
  const endDt = new Date();
  const startDt = subMonths(endDt, 6);
  const [yearMonth, setYearMonth] = useState<Date>(endDt);
  const currentYearMonth = format(endDt, "yyyy-MM");

  const { data } = useQueryPedometerRecordsMonthly(startDt, endDt);

  const avgStepCount = data?.find(
    (item) => item.monthDt === currentYearMonth,
  )?.avgSteps;

  return (
    <div className="flex h-full flex-1 flex-col gap-[12px]">
      <Summary avgStepCount={avgStepCount} />
      <PedometerCalendar
        yearMonth={yearMonth}
        currentDate={endDt}
        onChangeYearMonth={setYearMonth}
      />
      <PedometerTrendsChart data={data} />
    </div>
  );
};

const PedometerTrendsChart = ({
  data,
}: {
  data: { monthDt: string; /*yyyy-MM*/ totalSteps: number }[] | undefined;
}) => {
  const chartData: { key: string; steps: number }[] = data
    ? data.map((item) => {
        return {
          key: getMonthName(parse(item.monthDt, "yyyy-MM", new Date())),
          steps: item.totalSteps,
        };
      })
    : [];

  return (
    <section className="flex flex-1 flex-col rounded-lg bg-white p-[16px] shadow-lg">
      <div className="text-[16px] font-semibold">최근 기록 (월간)</div>
      <div className="flex-1 items-center justify-center px-[16px]">
        <PedometerChart data={chartData} />
      </div>
    </section>
  );
};
