"use server";

import {
  addOwnerToDb,
  addPetToDb,
  addQrEntryToDb,
  deletePetFromDb,
  deleteQrEntryFromDb,
  readPetsFromDb,
  readQrEntriesFromDb,
  syncPetsToDb,
} from "@workspace/db/crud/pet-pass/pet-pass";
import { encryptJWE } from "@workspace/utils/auth/jwt-utils";
import { getSession } from "@workspace/utils/auth/session";
import {
  addOwner,
  addPet,
  deletePet,
  getAPMS,
  getBreeds,
  getHairColors,
  getOwnerByUserDid,
  getPets,
  getQrEntry,
} from "./pet-api";
import {
  isPetPassSucceed,
  PetPassAPMSRequest,
  PetPassEntryRequest,
  PetPassPetDto,
  PetPassRegistOwnerRequest,
  PetPassRegistPetRequest,
} from "./pet-types";

export const fetchOwner = async ({ userDid }: { userDid: string }) => {
  const result = await getOwnerByUserDid({ userDid });
  if (isPetPassSucceed(result)) {
    await addOwnerToDb({ userDid, ...result.response });
    return { data: result.response };
  }
  return { error: result.resultMsg };
};

export const fetchRegistOwner = async (model: PetPassRegistOwnerRequest) => {
  const result = await addOwner(model);
  if (isPetPassSucceed(result)) {
    return { data: result.response };
  }
  return { error: result.resultMsg };
};

export const fetchPets = async (
  fetchFrom: "api" | "db",
): Promise<{
  data?: PetPassPetDto[];
  error?: string;
}> => {
  const session = await getSession();
  if (!session) return { error: "세션이 만료되었습니다." };
  const { userDid } = session;
  const resultDb = await readPetsFromDb(userDid);
  if (fetchFrom === "db") {
    return {
      data: resultDb.map(
        (item) =>
          ({
            ...item,
            isVerified: false,
            qrpDeletion: false,
            insDatetime: "",
          }) as PetPassPetDto,
      ),
    };
  } else {
    const result = await getPets(userDid);
    if (isPetPassSucceed(result)) {
      if (resultDb.length !== result.response.length) {
        await syncPetsToDb(userDid, result.response);
      }
      return { data: result.response };
    }
    return { error: result.resultMsg };
  }
};

export const fetchRegistPet = async (
  model: PetPassRegistPetRequest,
): Promise<{ data?: PetPassPetDto; error?: string }> => {
  const session = await getSession();
  if (!session) return { error: "세션이 만료되었습니다." };
  const { userDid } = session;
  const ownerData = await fetchOwner({ userDid });
  if (ownerData.data) {
    try {
      const result = await addPet(userDid, model);
      if (isPetPassSucceed(result)) {
        await addPetToDb({ userDid, ...result.response });
        return { data: result.response };
      } else {
        return { error: result.resultMsg };
      }
    } catch (e) {
      return { error: "반려동물 등록에 실패했습니다." };
    }
  } else {
    // 회원 가입 진행 후 등록
    const registOwner: PetPassRegistOwnerRequest = {
      ownCi: session.userCi,
      ownCode: session.userDid,
      ownName: session.userNm,
      ownPhone: session.mblTelNo,
      ownEmail: "",
      ownBirth: "",
      ownGender: "",
    };
    const ownerResult = await fetchRegistOwner(registOwner);
    if (ownerResult.data) {
      return await fetchRegistPet(model);
    } else {
      return { error: ownerResult.error };
    }
  }
};

export const fetchDeletePet = async (model: { qrpIdx: number }) => {
  const session = await getSession();
  if (!session) return { error: "세션이 만료되었습니다." };
  const result = await deletePet(session.userDid, model.qrpIdx);
  if (isPetPassSucceed(result)) {
    await deletePetFromDb(model.qrpIdx);
    await deleteQrEntryFromDb(model.qrpIdx);
    return { data: result.response };
  } else {
    return { error: result.resultMsg };
  }
};

export const fetchGetEntryPass = async (model: PetPassEntryRequest) => {
  const session = await getSession();
  if (!session) return { error: "세션이 만료되었습니다." };
  const { userDid } = session;
  const result = await getQrEntry(userDid, model);
  if (isPetPassSucceed(result)) {
    await addQrEntryToDb(
      userDid,
      result.response.map((item, index) => ({
        ...item,
        qrpIdx: model.qrpIds[index],
      })),
    );

    return { data: result.response };
  } else {
    return { error: result.resultMsg };
  }
};

export const fetchAPMS = async (model: PetPassAPMSRequest) => {
  const result = await getAPMS(model);
  if (isPetPassSucceed(result)) {
    return {
      data: await encryptJWE({ ownName: model.ownName, ...result.response }),
    };
  }
  return { error: result.resultMsg };
};

export const fetchBreeds = async () => {
  const result = await getBreeds();
  if (isPetPassSucceed(result)) {
    return { data: result.response };
  }
  return { error: result.resultMsg };
};

export const fetchHairColors = async ({ breedCode }: { breedCode: string }) => {
  const result = await getHairColors(breedCode);
  if (isPetPassSucceed(result)) {
    return { data: result.response };
  }
  return { error: result.resultMsg };
};

export const fetchQrEntriesFromDb = async () => {
  const session = await getSession();
  if (!session) return { error: "세션이 만료되었습니다." };
  try {
    const resultDb = await readQrEntriesFromDb(session.userDid);
    return { data: resultDb };
  } catch (e) {
    return { error: "조회에 실패했습니다." };
  }
};
