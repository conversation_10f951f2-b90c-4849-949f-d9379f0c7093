"use client";

import { useMutationPollVotes } from "@/app/poll/query";
import { usePollStore } from "@/app/poll/store-provider";
import { GuidePanel } from "@/app/suggest/components/common";
import { BottomButton } from "@/components/common/bottom-button";
import { <PERSON><PERSON> } from "@workspace/ui/components/button";
import { RotateCcw } from "lucide-react";
import { useRouter } from "next/navigation";
import {
  MutableRefObject,
  useCallback,
  useEffect,
  useRef,
  useState,
} from "react";
import SignaturePad from "signature_pad";
import { useShallow } from "zustand/shallow";

const PollSignPad = ({
  ref: signaturePad,
  disabled,
  isDrawing,
  setIsDrawing,
}: {
  ref: MutableRefObject<SignaturePad | null>;
  disabled: boolean;
  isDrawing: boolean;
  setIsDrawing: (isDrawing: boolean) => void;
}) => {
  const signaturePadRef = useRef(null);
  useEffect(() => {
    if (!signaturePadRef.current) return;
    const pad = new SignaturePad(signaturePadRef.current, {
      backgroundColor: "rgba(255, 255, 255,0)",
      penColor: "rgb(0, 0, 0)",
    });
    pad.addEventListener("beginStroke", () => setIsDrawing(true));
    signaturePad.current = pad;
  }, [signaturePadRef]);

  useEffect(() => {
    if (!signaturePad.current) return;
    if (disabled) {
      signaturePad.current.off();
    } else {
      signaturePad.current.on();
    }
  }, [signaturePad, disabled]);

  return (
    <div>
      <div className="px-[20px] pb-[10px] pt-[20px]">
        <div className="relative flex h-[200px] w-[320px] items-center justify-center rounded-md border-[1px] border-[#E1E4E7]">
          {!isDrawing && (
            <div className="absolute left-0 top-0 flex h-[200px] w-[320px] items-center justify-center text-[16px] text-[#E1E4E7]">
              서명
            </div>
          )}
          <canvas
            id="signature-pad"
            ref={signaturePadRef}
            className="absolute left-0 top-0"
            width={320}
            height={200}
          />
        </div>
      </div>
      <div className="mx-[20px] flex w-[320px] justify-end">
        <Button
          variant="none"
          size="none"
          className="text-[14px] font-medium"
          onClick={() => {
            setIsDrawing(false);
            signaturePad.current?.clear();
          }}
        >
          <div className="flex items-center">
            <div className="mr-[2px]">
              <RotateCcw size={15} />
            </div>
            서명 다시하기
          </div>
        </Button>
      </div>
    </div>
  );
};

const PollSubmitButton = ({
  disabled,
  onClick,
}: {
  disabled: boolean;
  onClick: () => void;
}) => {
  return (
    <BottomButton disabled={disabled} onClick={onClick}>
      투표하기
    </BottomButton>
  );
};

export const PollSignContent = () => {
  const router = useRouter();
  const [pollUid, selectedItem] = usePollStore(
    useShallow((state) => [state.pollUid, state.selectedItem]),
  );
  const signaturePad = useRef<SignaturePad>(null);
  const [isDrawing, setIsDrawing] = useState<boolean>(false);
  const { mutateAsync, isPending } = useMutationPollVotes({ pollUid });

  const handleSubmit = useCallback(async () => {
    //TODO: 사용자 인증 처리
    if (!signaturePad.current) return;
    const data = signaturePad.current.toDataURL();
    try {
      await mutateAsync({ signature: data, items: Array.from(selectedItem) });
      alert("투표가 완료되었습니다.");
      router.replace("/poll");
    } catch (e) {
      if (e instanceof Error) {
        alert(e.message);
      } else {
        console.error(e);
      }
    }
  }, [signaturePad, selectedItem, mutateAsync]);

  const isDisabled = isPending || !isDrawing;
  return (
    <>
      <GuidePanel type="pollSign" />
      <PollSignPad
        ref={signaturePad}
        isDrawing={isDrawing}
        setIsDrawing={setIsDrawing}
        disabled={isPending}
      />
      <PollSubmitButton disabled={isDisabled} onClick={handleSubmit} />
    </>
  );
};
