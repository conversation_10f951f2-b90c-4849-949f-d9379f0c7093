"use client";

import { ImageFiles } from "@/app/[tenantCd]/components/image-files";
import { isUserDid } from "@/lib/utils";
import { <PERSON><PERSON> } from "@toss/utils";
import {
  SuggestComment,
  SuggestCommentDto,
  SuggestFile,
} from "@workspace/db/crud/living-lab/suggest";
import { Button } from "@workspace/ui/components/button";
import { Card, CardContent } from "@workspace/ui/components/card";
import dayjs from "dayjs";
import { useRouter } from "next/navigation";
import { removeSuggestComment } from "../../../actions/suggest";

const SuggestCommentComponent = ({
  comment,
  onPressEdit,
  onPressDelete,
}: {
  comment: SuggestCommentDto;
  onPressEdit: (comment: SuggestComment) => void;
  onPressDelete: (comment: SuggestComment) => void;
}) => {
  return (
    <div className="py-[4px]">
      <div className="flex items-center justify-between">
        <div className="font-medium">{Masker.maskName(comment.name)}</div>
        <div className="justify-end gap-2">
          {!isUserDid(comment.userDid) && (
            <Button variant="outline" onClick={() => onPressEdit(comment)}>
              수정
            </Button>
          )}
          <Button variant="outline" onClick={() => onPressDelete(comment)}>
            삭제
          </Button>
        </div>
      </div>
      <div>
        <ImageFiles files={comment.files} baseURL={""} />
        {comment.content}
      </div>
      <div className="text-sm">
        {dayjs(comment.regDate).format("YYYY.MM.DD HH:mm:ss")}
      </div>
    </div>
  );
};

export const SuggestComments = ({
  tenantId,
  comments,
}: {
  tenantId: string;
  comments: SuggestCommentDto[];
}) => {
  const router = useRouter();
  const handleEdit = (comment: SuggestComment) => {
    // TODO
  };

  const handleDelete = (comment: SuggestComment) => {
    if (!confirm("정말 삭제하시겠습니까?")) return;
    removeSuggestComment(
      tenantId,
      comment.suggestUid,
      comment.parentUid,
      comment.uid,
    );
    router.refresh();
  };

  const commentsMap = comments.reduce(
    (acc, comment) => {
      if (comment.parentUid) {
        if (!acc[comment.parentUid]) acc[comment.parentUid] = [];
        acc[comment.parentUid].push(comment);
      } else {
        if (!acc[0]) acc[0] = [];
        acc[0].push(comment);
      }
      return acc;
    },
    {} as Record<number, SuggestCommentDto[]>,
  );

  if (!commentsMap[0]) return null;
  return (
    <Card>
      <CardContent className="p-6">
        {commentsMap[0].map((comment) => (
          <div key={comment.uid}>
            <SuggestCommentComponent
              comment={comment}
              onPressEdit={handleEdit}
              onPressDelete={handleDelete}
            />

            {commentsMap[comment.uid] ? (
              <div className="mb-[16px] ml-[32px]">
                {commentsMap[comment.uid]?.map((reply) => (
                  <SuggestCommentComponent
                    key={reply.uid}
                    comment={reply}
                    onPressEdit={handleEdit}
                    onPressDelete={handleDelete}
                  />
                ))}
              </div>
            ) : null}
          </div>
        ))}
      </CardContent>
    </Card>
  );
};
