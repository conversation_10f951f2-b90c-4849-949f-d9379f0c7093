import {
  fetchPointBalance,
  fetchPointBalanceByTenantCd,
} from "@/actions/point/pont-action";
import { MainContainer } from "@/components/main-container";
import { getSession } from "@workspace/utils/auth/session";
import { Metadata } from "next";
import Head from "next/head";
import { PointTitle } from "../components/common";
import {
  PointHistoryAllContent,
  PointHistoryLocalContent,
} from "./components/point-history-content";

export async function generateMetadata({
  params,
  searchParams,
}: WalletPointHistoryProps) {
  const { tenantCd } = await params;
  const { type = "all" } = await searchParams;
  if (isAllHistory(type)) {
    return { title: "포인트 내역" };
  }

  const pointAccount = await fetchPointBalanceByTenantCd(tenantCd);
  return {
    title: `${pointAccount?.name} 내역`,
  };
}

interface WalletPointHistoryProps {
  params: Promise<{ tenantCd: string }>;
  searchParams: Promise<{ type?: string }>;
}

export default async function WalletPointHistoryPage({
  params,
  searchParams,
}: Readonly<WalletPointHistoryProps>) {
  const { tenantCd } = await params;
  const { type = "all" } = await searchParams;

  return (
    <MainContainer containerClassName="px-[0px] pb-[0px]" paddingBottom={false}>
      {isAllHistory(type) ? (
        <PointHistoryAllContent tenantCd={tenantCd} />
      ) : (
        <PointHistoryLocalContent tenantCd={tenantCd} />
      )}
    </MainContainer>
  );
}

const isAllHistory = (type: string) => type === "all";
