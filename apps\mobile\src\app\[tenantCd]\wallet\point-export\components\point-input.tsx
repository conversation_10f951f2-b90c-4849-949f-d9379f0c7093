"use client";

import {
  ButtonNone,
  OutlineButton,
  PrimaryButton,
  SecondaryButton,
} from "@/components/button";
import { commaizeNumber } from "@toss/utils";
import { Button } from "@workspace/ui/components/button";
import { Input } from "@workspace/ui/components/input";
import { cn } from "@workspace/ui/lib/utils";
import { CircleX } from "lucide-react";
import Image from "next/image";
import { ChangeEvent, ReactNode, useState } from "react";

const AmountButton = ({
  focused,
  children,
  onClick,
}: {
  focused?: boolean;
  children: ReactNode;
  onClick: () => void;
}) => {
  return (
    <PrimaryButton
      onClick={onClick}
      className={cn(
        "h-[40px] flex-1 rounded-[8px] px-0 py-[8px] text-[16px] font-medium",
        !focused && "bg-[#ffffff] text-[#000000] hover:text-[#FFFFFF]",
      )}
    >
      {children}
    </PrimaryButton>
  );
};

export const PointInput = ({
  title,
  totalBalance,
  value,
  onChange,
}: {
  title: string;
  totalBalance: number;
  value: number;
  onChange: (value: number) => void;
}) => {
  const presetAmounts = [1000, 5000, 10000];

  const handleAmountSelect = (amount: number) => {
    if (amount <= totalBalance) {
      onChange(amount);
    }
  };

  const handleCustomAmountChange = (e: ChangeEvent<HTMLInputElement>) => {
    const rawValue = e.target.value.replace(/,/g, "");
    const value = parseInt(rawValue) || 0;
    onChange(value <= totalBalance ? value : totalBalance);
  };

  return (
    <div>
      <h1 className="py-[20px] text-[16px] font-semibold">{title}</h1>
      <div className="rounded-[10px] bg-[#ECF2FD] px-[10px] py-[20px]">
        <div className="mb-4 flex items-center justify-between">
          <span className="text-gray-700">전환가능한 포인트</span>
          <span className="font-semibold text-blue-500">
            {commaizeNumber(totalBalance)} P
          </span>
        </div>

        <div className="relative mb-4">
          <Input
            type="text"
            value={value ? commaizeNumber(value) : ""}
            onChange={handleCustomAmountChange}
            placeholder="0"
            className="h-[50px] w-full border-none bg-[#ffffff] p-[10px] pr-[50px] text-[22px] font-bold outline-none"
          />
          <ButtonNone onClick={() => onChange(0)}>
            <Image
              src={"/assets/wallet/btn-point-clear.png"}
              alt="전체 삭제"
              width={24}
              height={24}
              className="absolute right-3 top-1/2 -translate-y-1/2 transform text-[#AAAAAA]"
            />
          </ButtonNone>
        </div>

        <div className="flex gap-2">
          {presetAmounts.map((amount) => (
            <AmountButton
              key={amount}
              focused={value === amount}
              onClick={() => handleAmountSelect(amount)}
            >
              {commaizeNumber(amount)}
            </AmountButton>
          ))}
          <AmountButton
            focused={value === totalBalance}
            onClick={() => handleAmountSelect(totalBalance)}
          >
            전액
          </AmountButton>
        </div>
      </div>
    </div>
  );
};
