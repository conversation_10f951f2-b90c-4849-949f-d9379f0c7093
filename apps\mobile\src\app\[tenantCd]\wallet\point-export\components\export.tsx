"use client";

import { PrimaryButton } from "@/components/button";
import { useState } from "react";
import { CardInput } from "./card-input";
import { PointInput } from "./point-input";

export const PointExport = () => {
  const totalBalance = 0; // TODO
  const [exportPoint, setExportPoint] = useState(0);

  return (
    <div className="flex h-full flex-col">
      <div className="flex flex-1 flex-col gap-[12px]">
        <PointInput
          title="지역화폐로 보낼 포인트"
          totalBalance={totalBalance}
          value={exportPoint}
          onChange={setExportPoint}
        />
        <CardInput />
      </div>
      <div className="flex">
        <PrimaryButton disabled={true} className="h-[48px] text-[16px]">
          내보내기
        </PrimaryButton>
      </div>
    </div>
  );
};
