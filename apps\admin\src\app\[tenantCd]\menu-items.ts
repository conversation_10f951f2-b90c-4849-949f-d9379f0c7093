import { TenantPayload } from "@/lib/session";
import { isMainTenant } from "@/lib/tenant";
import { SideMenuItem } from "./components/sidebar";

export const getNavItems = (tenantInfo: TenantPayload): SideMenuItem[] => {
  const { tenantCd } = tenantInfo;
  return [
    {
      title: "Dashboard",
      icon: "menu-dashboard.svg",
      link: "/dashboard",
    },
    {
      title: "회원관리",
      icon: "menu-user.svg",
      children: [
        {
          title: "회원현황",
          link: "/users/stats",
        },
        {
          title: "회원정보관리",
          link: "/users/manage",
        },
        {
          title: "탈퇴환불관리",
          link: "#/users/refunds",
        },
      ],
    },
    {
      title: "포인트/결제관리",
      icon: "menu-point.svg",
      children: [
        {
          title: "포인트 현황",
          link: "/points/stats",
        },
        {
          title: "포인트 적립 관리(유상)",
          link: "/points/manage/money",
        },
        {
          title: "포인트 적립 관리(무상)",
          link: "/points/manage",
        },
        {
          title: "포인트 사용 관리",
          link: "/points/manage/use",
        },
        {
          title: "포인트 전환 정책 관리",
          link: "/points/policy",
        },
      ],
    },
    ...(isMainTenant(tenantCd)
      ? []
      : [
          {
            title: "혜택_로컬서비스",
            icon: "menu-local.svg",
            children: [
              {
                title: "리빙랩",
                children: [
                  {
                    title: "정책제안",
                    link: "/local/living-lab/suggest",
                  },
                  {
                    title: "주민투표",
                    link: "#/local/living-lab/vote",
                  },
                  {
                    title: "토론하기-설문조사",
                    link: "#/local/living-lab/survey",
                  },
                  {
                    title: "반영하기",
                    link: "#/local/living-lab/reflect",
                  },
                ],
              },
            ],
          },
        ]),
    {
      title: "혜택_공통서비스",
      icon: "menu-addon.svg",
      link: "#/addons",
    },
    {
      title: "알림관리",
      icon: "menu-notification.svg",
      children: [
        {
          title: "사용자 공지사항",
          link: "/board/user-notice",
        },
        ...(isMainTenant(tenantCd)
          ? [
              {
                title: "관리자 공지사항",
                link: "/board/admin-notice",
              },
            ]
          : []),
      ],
    },
    {
      title: "운영관리",
      icon: "menu-op.svg",
      children: [
        ...(isMainTenant(tenantCd)
          ? [
              {
                title: "약관 및 정책",
                link: "/board/terms",
              },
            ]
          : []),
        {
          title: "이용안내",
          link: "#",
        },
      ],
    },
    {
      title: "정산관리",
      icon: "menu-settle.svg",
      link: "#/settlements",
    },
    {
      title: "시스템관리",
      icon: "menu-system.svg",
      link: "#/system",
    },
  ];
};
