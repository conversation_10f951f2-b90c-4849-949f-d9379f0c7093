import { cn } from "@workspace/ui/lib/utils";
import { ReactNode } from "react";
import { Matcher } from "react-day-picker";
import { ControllerRenderProps, FieldPath, FieldValues } from "react-hook-form";
import { Label } from "../label";
import { RadioGroup, RadioGroupItem } from "../radio-group";

type RadioGroupItemType = {
  value: string;
  label: ReactNode;
};

export const RadioGrid = <TFieldValues extends FieldValues>({
  field,
  items,
  className,
}: {
  field: ControllerRenderProps<TFieldValues, FieldPath<TFieldValues>>;
  items: RadioGroupItemType[];
  className?: string;
}) => {
  return (
    <RadioGroup
      name={field.name}
      defaultValue={field.value}
      onValueChange={field.onChange}
      className={cn("grid gap-[8px]", className)}
    >
      {items.map((item) => (
        <div key={item.value}>
          <RadioGroupItem
            value={item.value}
            id={`${field.name}-${item.value}`}
            className="peer sr-only"
          />
          <Label
            htmlFor={`${field.name}-${item.value}`}
            className="border-muted bg-popover hover:bg-accent hover:text-accent-foreground peer-data-[state=checked]:border-primary [&:has([data-state=checked])]:border-primary peer-data-[state=checked]:text-primary peer-data-[state=checked]:bg-primary/10 flex flex-col items-center justify-between rounded-md border-[1px] p-[10px]"
          >
            {item.label}
          </Label>
        </div>
      ))}
    </RadioGroup>
  );
};
