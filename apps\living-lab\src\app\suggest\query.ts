"use client";

import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { SuggestCommentDto } from "@workspace/db/crud/living-lab/suggest";

const fetchUserVote = async (uid: number) => {
  const result = await fetch(`/api/suggest/${uid}/vote`);
  return (await result.json()) as { up: number; down: number };
};

const fetchComments = async (uid: number, commentUid: number | null) => {
  const endpoint = commentUid
    ? `/api/suggest/${uid}/comment/${commentUid}`
    : `/api/suggest/${uid}/comment`;
  const result = await fetch(endpoint);
  if (result.ok) return (await result.json()) as SuggestCommentDto[];
  return [];
};

const fetchCommentVotes = async (uid: number) => {
  const result = await fetch(`/api/suggest/${uid}/comment/votes`);
  return (await result.json()) as number[];
};

const postSuggest = async (formData: FormData) => {
  const result = await fetch("/api/suggest", {
    method: "POST",
    body: formData,
  });
  return result;
};

const updateSuggest = async (suggestUid: number, formData: FormData) => {
  const result = await fetch(`/api/suggest/${suggestUid}`, {
    method: "PUT",
    body: formData,
  });
  return result;
};

export const deleteSuggest = async (suggestUid: number) => {
  const result = await fetch(`/api/suggest/${suggestUid}`, {
    method: "DELETE",
  });
  return result;
};

const postCommentVote = async (
  suggestUid: number,
  commentUid: number,
  vote: 0 | 1,
) => {
  const result = await fetch(
    `/api/suggest/${suggestUid}/comment/${commentUid}/vote`,
    {
      method: "POST",
      body: JSON.stringify({ vote }),
    },
  );
  return result;
};

const postComment = async (suggestUid: number, formData: FormData) => {
  const result = await fetch(`/api/suggest/${suggestUid}/comment`, {
    method: "POST",
    body: formData,
  });
  return result;
};

const updateComment = async (
  suggestUid: number,
  commentUid: number,
  formData: FormData,
) => {
  const result = await fetch(
    `/api/suggest/${suggestUid}/comment/${commentUid}`,
    {
      method: "PUT",
      body: formData,
    },
  );
  return result;
};

export const useQueryUserVote = ({ uid }: { uid: number }) => {
  return useQuery({
    queryKey: ["suggest/vote", uid],
    queryFn: () => fetchUserVote(uid),
  });
};

export const useQueryComments = (
  { uid, commentUid }: { uid: number; commentUid: number | null },
  enabled: boolean = true,
) => {
  return useQuery({
    queryKey: ["suggest/comment", uid, commentUid],
    queryFn: () => fetchComments(uid, commentUid),
    enabled,
  });
};

export const useQueryCommentVotes = ({ uid }: { uid: number }) => {
  return useQuery({
    queryKey: ["suggest/vote", uid],
    queryFn: () => fetchCommentVotes(uid),
  });
};

export const useMutationCommentVotes = ({
  uid,
  parentUid,
}: {
  uid: number;
  parentUid: number | null;
}) => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationKey: ["suggest/vote", uid],
    mutationFn: ({ commentUid, vote }: { commentUid: number; vote: 0 | 1 }) =>
      postCommentVote(uid, commentUid, vote),
    onMutate: async ({ commentUid, vote }) => {
      await queryClient.cancelQueries({ queryKey: ["suggest/vote", uid] });
      queryClient.setQueryData<number[]>(["suggest/vote", uid], (prev) => {
        if (!prev) return vote ? [commentUid] : [];
        return vote === 1
          ? [...prev, commentUid]
          : prev.filter((id) => id !== commentUid);
      });

      queryClient.setQueryData<SuggestCommentDto[]>(
        ["suggest/comment", uid, parentUid],
        (prev) => {
          if (!prev) return [];
          return prev.map((comment) => {
            if (comment.uid !== commentUid) return comment;
            return {
              ...comment,
              upCount: vote === 1 ? comment.upCount! + 1 : comment.upCount! - 1,
            };
          });
        },
      );
    },
  });
};

export const useMutationSuggest = () => {
  return useMutation({
    mutationFn: async ({
      suggestUid,
      formData,
    }: {
      suggestUid?: number;
      formData: FormData;
    }) => {
      const result = !suggestUid
        ? await postSuggest(formData)
        : await updateSuggest(suggestUid, formData);
      if (!result.ok) {
        throw new Error(result.statusText);
      }
      return result;
    },
  });
};

export const useMutationComment = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async ({
      suggestUid,
      commentUid,
      formData,
    }: {
      suggestUid: number;
      parentUid: number | null;
      commentUid?: number;
      formData: FormData;
    }) => {
      const result = !commentUid
        ? await postComment(suggestUid, formData)
        : await updateComment(suggestUid, commentUid, formData);
      if (!result.ok) {
        throw new Error(result.statusText);
      }
      return result;
    },
    onSettled: (_data, _error, { suggestUid }) => {
      queryClient.invalidateQueries({
        queryKey: ["suggest/comment", suggestUid],
      });
    },
  });
};
