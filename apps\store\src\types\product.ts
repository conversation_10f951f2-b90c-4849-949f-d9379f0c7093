export type ProductListProps = {
  categoryText: string | null;
  searchTag: string | null;
  keyword: string | null;
  count: number;
};

export type ProductItem = {
  id: number;
  name: string;
  price: number;
  brand: string | null;
  imageUrl: string;
  sourceId: string;
  sourceType: string;
};

export type ProductsFilter = {
  searchTag?: string[];
  keyword?: string;
  sortOrder: string;
  pageIndex?: number;
  pageSize?: number;
};

export type ProductDetailItem = ProductItem & {
  deliver: string;
  deliverMethod: string;
};
