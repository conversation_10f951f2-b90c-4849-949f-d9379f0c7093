import { PointInfo } from "@/components/common/point-info";
import { SearchBar } from "@/components/common/search-bar";
import { MainContainer } from "@/components/main-container";
import { CategoryList } from "@/components/root/category-list";
import { Metadata } from "next";

export default async function MainPage({
  params,
}: Readonly<{
  params: Promise<{ keyword: string }>;
}>) {
  const { keyword } = await params;

  return (
    <MainContainer className="py-[20px]" gradient={false}>
      <PointInfo mileage={undefined} />
      <SearchBar initialKeyword={keyword} />
      {/* TODO: 추천 상품  */}

      <div className="my-[10px] flex items-center justify-between">
        <h2 className="text-base font-bold text-[#292929]">카테고리</h2>
      </div>
      <CategoryList />
    </MainContainer>
  );
}
