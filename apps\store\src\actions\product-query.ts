import { ProductItem } from "@/types/product";
import { keepPreviousData, useInfiniteQuery } from "@tanstack/react-query";
import { fetchProducts } from "./product-action";

const PAGE_SIZE = 20;

export const useInfiniteQueryProducts = ({
  searchTag,
  keyword,
  sortOrder,
}: {
  searchTag: string[];
  keyword: string | undefined;
  sortOrder: string;
}) =>
  useInfiniteQuery<ProductItem[]>({
    queryKey: ["productList", searchTag, keyword ?? "", sortOrder],
    queryFn: async ({ pageParam }) => {
      const pageIndex = (pageParam as number) / PAGE_SIZE;
      const result = await fetchProducts({
        searchTag,
        keyword,
        sortOrder,
        pageIndex,
        pageSize: PAGE_SIZE,
      });
      return result;
    },
    getNextPageParam: (lastPage, pages) => {
      if (lastPage.length === 0) {
        return undefined;
      }
      const totalCount = pages.reduce((acc, page) => acc + page.length, 0);
      return totalCount;
    },
    initialPageParam: 0,
    placeholderData: keepPreviousData,
  });
