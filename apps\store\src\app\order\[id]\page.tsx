import { fetchProduct } from "@/actions/product-action";
import { SummaryItem } from "@/components/common/summary";
import { MainContainer } from "@/components/main-container";
import { commaizeNumber, Masker } from "@toss/utils";
import { getSession } from "@workspace/utils/auth/session";
import { redirect } from "next/navigation";
import { OrderButton } from "./order-button";
import { OrderStep, OrderTitle } from "./order-info";

export default async function OrderPage({
  params,
  searchParams,
}: Readonly<{
  params: Promise<{ id: string }>;
  searchParams: Promise<{ nonce: string }>;
}>) {
  const { id } = await params;
  const { nonce } = await searchParams;
  const session = await getSession();

  const idNumber = Number(id);
  if (isNaN(idNumber) || idNumber <= 0) {
    return redirect("/");
  }

  const product = await fetchProduct(idNumber);
  if (!product) return redirect("/");

  return (
    <MainContainer className="py-[20px]" gradient={false}>
      <OrderStep step="confirm" />
      <div className="my-[20px] flex flex-col gap-[8px]">
        <OrderTitle>주문정보</OrderTitle>
        <SummaryItem title="상품명">{product.name}</SummaryItem>
        <SummaryItem title="최종 판매 가격">
          <span className="font-bold">{commaizeNumber(product.price)}</span> P
        </SummaryItem>
      </div>
      <div className="my-[20px] flex flex-col gap-[8px]">
        <OrderTitle>발신정보</OrderTitle>
        <SummaryItem title="발신번호">{product.deliver}</SummaryItem>
        <SummaryItem title="발신방식">{product.deliverMethod}</SummaryItem>
      </div>
      <div className="my-[20px] flex flex-col gap-[8px]">
        <OrderTitle>수신정보</OrderTitle>
        <SummaryItem title="수신자명">
          {Masker.maskName(session?.userNm ?? "")}
        </SummaryItem>
        <SummaryItem title="수신번호">
          {Masker.maskPhoneNumber(session?.mblTelNo ?? "")}
        </SummaryItem>
      </div>
      <div className="my-[20px] h-[4px] bg-gray-200" />

      <OrderButton product={product} nonce={nonce} />
    </MainContainer>
  );
}
