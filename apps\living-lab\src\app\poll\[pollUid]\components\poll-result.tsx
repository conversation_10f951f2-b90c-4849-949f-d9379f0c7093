import {
  readPollItems,
  readPollVoteCount,
  readPollVoterCount,
} from "@workspace/db/crud/living-lab/poll";

export const PollResult = async ({
  tenantId,
  pollUid,
}: {
  tenantId: string;
  pollUid: number;
}) => {
  const items = await readPollItems(tenantId, pollUid);
  const voterCount = await readPollVoterCount(tenantId, pollUid);
  const voteCount = await readPollVoteCount(tenantId, pollUid);

  return (
    <div className="border-b-[1px] border-[#F5F5F5] px-[20px]">
      {items.map((pollItem) => {
        const count =
          voteCount.find((r) => r.pollItemUid === pollItem.uid)?.count ?? 0;
        const percent = voterCount === 0 ? 0 : (count / voterCount) * 100;
        return (
          <div key={pollItem.uid} className="py-[20px]">
            <div className="mb-[10px] text-[14px]">{pollItem.name}</div>
            <div className="text-[12px]">
              <span className="mr-[14px]">
                <span className="text-primary">{percent}</span>%
              </span>
              <span className="text-primary">{count}</span>명
            </div>
            {/*draw graph*/}
            <div className="mt-[10px] h-[5px] rounded-[5px] bg-[#E1E4E7]">
              <div
                className="bg-primary h-full rounded-[5px]"
                style={{ width: `${percent}%` }}
              ></div>
            </div>
          </div>
        );
      })}
    </div>
  );
};
