import Header from "@/components/common/header";
import { EmptyList } from "@/components/common/list";
import { PollSurveyItem } from "@/components/poll-survey/poll-survey-item";
import {
  readSurveyMinYear,
  readSurveys,
  Survey,
} from "@workspace/db/crud/living-lab/survey";
import { SURVEY_STATE } from "@workspace/db/schema/living-lab/survey";
import { getSession } from "@workspace/utils/auth/session";
import dayjs from "dayjs";
import { redirect, RedirectType } from "next/navigation";
import { Suspense } from "react";
import { GuidePanel } from "../suggest/components/common";
import { SurveyFilter } from "./components/survey-filter";
import { getSurveyStateName, isSurveyClosed } from "./consts";

const SurveyItem = ({ item }: { item: Survey }) => {
  const period = [dayjs(item.startDate), dayjs(item.endDate)];
  const variant =
    item.state === SURVEY_STATE.CLOSED
      ? "closed"
      : isSurveyClosed(item.state, period)
        ? "expired"
        : "default";
  const stateName = getSurveyStateName(item.state, period);

  return (
    <PollSurveyItem
      href={`/survey/${item.uid}`}
      category={item.category}
      title={item.title}
      period={period}
      state={stateName}
      variant={variant}
    />
  );
};

const SurveyContents = async ({
  tenantCd,
  category,
  year,
  isClosed,
}: {
  tenantCd: string;
  category: string | undefined;
  year: number | undefined;
  isClosed: boolean;
}) => {
  const surveys = await readSurveys(tenantCd, category, year, isClosed);
  const currentYear = new Date().getFullYear();
  const minYear = isClosed ? await readSurveyMinYear(tenantCd) : currentYear;
  const years = Array.from(
    { length: currentYear - minYear + 1 },
    (_, i) => currentYear - i,
  );

  return (
    <>
      <SurveyFilter
        isClosed={isClosed}
        category={category}
        year={Number.isNaN(year) ? currentYear : year!}
        selectableYears={years}
        count={surveys.length}
      />
      <div className="flex flex-1 overflow-y-hidden">
        <div className="w-full overflow-y-auto bg-[#FAFBFF] py-[20px]">
          {surveys.length < 1 ? (
            <EmptyList>
              {isClosed
                ? "현재 완료된 설문이 없습니다."
                : "현재 진행 중인 주민 투표가 없습니다."}
              <div className="mt-[10px] text-[12px]">
                설문이 등록되면 참여하실 수 있습니다.
              </div>
            </EmptyList>
          ) : (
            <div className="space-[10px] flex flex-col space-y-[10px]">
              {surveys.map((item, index) => (
                <SurveyItem key={index} item={item} />
              ))}
            </div>
          )}
        </div>
      </div>
    </>
  );
};

const SurveyContentsSkeleton = () => {
  // TODO
  return null;
};

export default async function SurveyPage({
  searchParams,
}: {
  searchParams: Promise<{ closed?: string; category?: string; year?: string }>;
}) {
  const session = await getSession();
  if (!session) redirect("/", RedirectType.replace);

  const { tenantCd } = session;
  const { closed, category, year } = await searchParams;

  return (
    <main className="flex max-h-screen min-h-screen flex-col bg-[#FFFFFF]">
      <Header title="토론하기" parent="/home" />
      <GuidePanel type="survey" />
      <Suspense fallback={<SurveyContentsSkeleton />}>
        <SurveyContents
          tenantCd={tenantCd}
          isClosed={!!closed}
          category={category}
          year={year ? Number.parseInt(year) : undefined}
        />
      </Suspense>
    </main>
  );
}
