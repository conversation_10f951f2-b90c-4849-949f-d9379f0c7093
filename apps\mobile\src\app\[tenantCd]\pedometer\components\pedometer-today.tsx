"use client";

import { commaizeNumber } from "@toss/utils";
import { ChartConfig, ChartContainer } from "@workspace/ui/components/chart";
import { useBridgeStore } from "@workspace/webview-state";
import Image from "next/image";
import {
  Label,
  PolarGrid,
  PolarRadiusAxis,
  RadialBar,
  RadialBarChart,
} from "recharts";

const chartConfig: ChartConfig = {
  steps: {},
} satisfies ChartConfig;

const PedometerTodayChart = ({
  steps,
  targetSteps,
  size = 160,
}: {
  steps: number | null;
  targetSteps: number;
  size?: number;
}) => {
  const weight = 10;
  const polarRadius = [size / 2, size / 2 - weight];
  const chartData = [{ steps: steps || 0, fill: "rgba(var(--primary), 1)" }];

  return (
    <ChartContainer config={chartConfig} className="mx-auto aspect-square">
      <RadialBarChart
        data={chartData}
        startAngle={90}
        endAngle={90 - ((steps ?? 0) / targetSteps) * 360}
        innerRadius={size / 2 + weight / 2 + 2}
        outerRadius={size / 2 - weight * 2 + 4}
      >
        <PolarGrid
          gridType="circle"
          radialLines={false}
          stroke="none"
          className="first:fill-muted last:fill-background"
          polarRadius={polarRadius}
        />
        <RadialBar dataKey="steps" background cornerRadius={weight / 2} />
        <PolarRadiusAxis tick={false} tickLine={false} axisLine={false}>
          <Label
            content={({ viewBox }) => {
              if (viewBox && "cx" in viewBox && "cy" in viewBox) {
                const cx = viewBox.cx as number;
                const cy = viewBox.cy as number;
                return (
                  <foreignObject
                    x={cx - size / 2}
                    y={cy - size / 2}
                    width={size}
                    height={size}
                  >
                    <div className="flex h-full flex-col items-center justify-center gap-[6px] pb-[10px]">
                      <Image
                        src="/assets/pedometer/footsteps.png"
                        width={30}
                        height={30}
                        alt={""}
                      />
                      <div className="text-primary mt-[4px] text-[26px] font-semibold">
                        {commaizeNumber(chartData[0].steps)}
                      </div>
                      <div className="text-[14px]">걸음</div>
                    </div>
                  </foreignObject>
                );
              }
            }}
          />
        </PolarRadiusAxis>
      </RadialBarChart>
    </ChartContainer>
  );
};

const PedometerView = ({
  steps,
  goalCount,
}: {
  steps: number | null;
  goalCount: number;
}) => {
  return (
    <section className="flex flex-col rounded-lg bg-white p-[16px] shadow-lg">
      <div className="flex-1 text-center text-[16px] font-semibold">
        오늘의 걸음수
      </div>
      <div className="flex flex-col justify-center">
        <div className="mx-auto aspect-square min-h-[200px] min-w-[200px] items-center justify-center p-[16px]">
          <PedometerTodayChart
            steps={steps}
            targetSteps={goalCount}
            size={160}
          />
        </div>
        <div className="flex items-center justify-center">
          <div className="bg-muted items-center justify-center rounded-[20px] px-[16px] py-[4px]">
            <span className="text-center text-[14px] font-semibold">
              목표 :{" "}
              <span className="text-center text-[16px] font-semibold">
                {commaizeNumber(goalCount)}
              </span>{" "}
              걸음
            </span>
          </div>
        </div>
      </div>
    </section>
  );
};

export const PedometerToday = ({ goalCount }: { goalCount: number }) => {
  const todaySteps = useBridgeStore((state) => state.todaySteps);
  return <PedometerView steps={todaySteps ?? null} goalCount={goalCount} />;
};
