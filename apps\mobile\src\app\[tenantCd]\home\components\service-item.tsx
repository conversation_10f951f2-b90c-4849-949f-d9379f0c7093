import { convertNewlineToJSX } from "@toss/react";
import { Button } from "@workspace/ui/components/button";
import Image from "next/image";
import { ReactNode } from "react";

export const ServiceItem = ({
  title,
  imageUrl,
  description,
  buttonText,
}: {
  title: string;
  imageUrl: string;
  description: string | ReactNode;
  buttonText: string;
}) => {
  return (
    <div className="flex h-[152px] flex-col justify-between rounded-[6px] bg-white p-[10px]">
      <div className="mb-[8px] flex items-center justify-between">
        <span className="text-[16px] font-semibold">
          {convertNewlineToJSX(title)}
        </span>
        <Image
          src={imageUrl}
          width={40}
          height={40}
          alt={title}
          className="ml-[10px]"
        />
      </div>
      <div className="line-clamp-3 flex-1 place-items-start text-ellipsis text-[12px] text-[#636F7A]">
        {description}
      </div>
      <div>
        <Button
          className="w-full items-center justify-center transition-transform active:scale-95"
          variant="secondary"
        >
          <span className="text-[#2DA5FF]">{buttonText}</span>
        </Button>
      </div>
    </div>
  );
};
