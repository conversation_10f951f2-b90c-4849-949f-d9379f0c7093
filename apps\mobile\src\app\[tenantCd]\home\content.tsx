"use client";

import { MainContainer } from "@/components/main-container";
import {
  useGetCityLoungeItems,
  useGetLocalLoungeItems,
} from "@/consts/menu-items";
import { useBridgeStore } from "@workspace/webview-state";
import { FeatureList } from "./components/feature-list";
import { IdPointCard } from "./components/id-point-card";
import { ServiceList, ServiceListLocal } from "./components/service-list";

export const HomeContent = ({ tenantCd }: { tenantCd: string }) => {
  const [userData] = useBridgeStore((state) => [state.userData]);
  const localLoungeItems = useGetLocalLoungeItems(tenantCd);
  const cityLoungeItems = useGetCityLoungeItems(tenantCd);

  return (
    <MainContainer paddingTop paddingBottom>
      <IdPointCard
        tenantCd={tenantCd}
        userName={userData?.name ?? ""}
        className="mb-[12px]"
      />
      <ServiceListLocal tenantId={tenantCd} />
      <ServiceList />
      <FeatureList data={localLoungeItems} />
      <FeatureList data={cityLoungeItems} />
    </MainContainer>
  );
};
