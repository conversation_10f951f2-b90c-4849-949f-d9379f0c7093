import { AppContent } from "@/app/[tenantCd]/components/container";
import {
  readTermsDataById,
  readTermsGroups,
} from "@workspace/db/crud/common/terms";
import { TermsForm } from "./components/terms-form";

export default async function TermsDetailPage({
  params,
}: Readonly<{
  params: Promise<{ tenantCd: string; termsId: string }>;
}>) {
  const { tenantCd, termsId } = await params;
  const termsIdNumber = Number.parseInt(termsId, 10);
  const termsGroups = await readTermsGroups(tenantCd);
  const termsData = Number.isNaN(termsIdNumber)
    ? null
    : await readTermsDataById(tenantCd, termsIdNumber);

  return (
    <AppContent title={termsData ? "약관 수정" : "약관 등록"}>
      <TermsForm
        tenantCd={tenantCd}
        initialData={termsData}
        termsGroups={termsGroups}
      />
    </AppContent>
  );
}
