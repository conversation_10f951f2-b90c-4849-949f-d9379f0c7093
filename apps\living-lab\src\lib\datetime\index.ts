import dayjs, { Dayjs, PluginFunc } from "dayjs";
import timezone from "dayjs/plugin/timezone";
import utc from "dayjs/plugin/utc";

const formatAs: PluginFunc = (_, Dayjs) => {
  Dayjs.prototype.formatAsDate = function () {
    return this.format("YYYY.MM.DD");
  };

  Dayjs.prototype.formatAsDateTime = function () {
    return this.format("YYYY.MM.DD. HH:mm");
  };

  Dayjs.prototype.formatAsTime = function () {
    return this.format("HH:mm");
  };
};

const kst: PluginFunc = (_, Dayjs, dayjs) => {
  dayjs.kst = (date, format) => {
    return format
      ? dayjs.tz(date, format, "Asia/Seoul")
      : dayjs.tz(date, "Asia/Seoul");
  };

  Dayjs.prototype.kst = function (options) {
    return this.tz("Asia/Seoul", options?.keepLocalTime);
  };
};

dayjs.extend(timezone);
dayjs.extend(utc);
dayjs.extend(formatAs);
dayjs.extend(kst);

export const formatAsDate = (date: string) =>
  dayjs(date).kst({ keepLocalTime: true }).formatAsDate();
export const formatAsDateTime = (date: string | Dayjs) =>
  dayjs(date).kst({ keepLocalTime: true }).formatAsDateTime();
