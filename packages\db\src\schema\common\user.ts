import {
  boolean,
  char,
  pgTable,
  smallint,
  timestamp,
  varchar,
} from "drizzle-orm/pg-core";

export const userDids = pgTable("tb_user_did", {
  userDid: varchar("user_did", { length: 255 }).notNull().unique(),
  userPincode: varchar("user_pincode", { length: 255 }).notNull(),
  didStts: varchar("did_stts", { length: 10 }).notNull(),
  hsmTk: varchar("hsm_tk", { length: 255 }).notNull(),
  issueDt: timestamp("issue_dt", { mode: "string" }).notNull(),
  dscdDt: timestamp("dscd_dt", { mode: "string" }),
});

export const users = pgTable("tb_user_info", {
  userDid: varchar("user_did", { length: 255 })
    .notNull()
    .references(() => userDids.userDid, {
      onDelete: "restrict",
      onUpdate: "restrict",
    }),
  userCi: varchar("user_ci", { length: 255 }),
  userNm: varchar("user_nm", { length: 100 }).notNull(),
  userStts: varchar("user_stts", { length: 10 }).notNull(),
  telnoEnc: varchar("telno_enc", { length: 100 }).notNull(),
  mblOt: varchar("mbl_ot", { length: 10 }),
  brdt: char("brdt", { length: 8 }),
  gender: varchar("gender", { length: 4 }),
  frgnrVal: smallint("frgnr_val").notNull(),
  citizenYn: smallint("citizen_yn"),
  joinDt: timestamp("join_dt").notNull(),
  whdwlDt: timestamp("whdwl_dt"),
  lastLgnDt: timestamp("last_lgn_dt"),
  lastChgDt: timestamp("last_chg_dt"),
});

export const userVcs = pgTable("tb_user_vc", {
  id: varchar("id", { length: 255 }).notNull().primaryKey(),
  issuer: varchar("issuer", { length: 255 }),
  issuedDt: timestamp("issued_dt").notNull(),
  expireDt: timestamp("expire_dt").notNull(),
  credDef: varchar("cred_def", { length: 255 }),
  subjectId: varchar("subject_id", { length: 255 }),
  claim: varchar("claim", { length: 255 }),
  dscdVal: boolean("dscd_val"),
  dscdCn: varchar("dscd_cn", { length: 50 }),
  regDt: timestamp("reg_dt").notNull(),
  updtDt: timestamp("updt_dt"),
});
