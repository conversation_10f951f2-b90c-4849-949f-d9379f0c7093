import { getMainDb } from "@workspace/db/drizzle";
import {
  ADMIN_ACTION,
  ADMIN_ROLE,
  adminInfos,
  adminRoles,
} from "@workspace/db/schema/admin/admin";
import { saasTenants } from "@workspace/db/schema/common/saas";
import { ClientInfo } from "@workspace/utils/client-utils";
import { eq } from "drizzle-orm";
import { addAdminLogTx } from "./admin-logs";

export type AdminSignUpDto = {
  userId: string;
  userPwd: string;
  userNm: string;
  userPhone: string;
  userEmail: string;
  tenantCd: string;
  userDepr: string;
};

export const addAdmin = async (
  data: AdminSignUpDto,
  clientInfo: ClientInfo,
) => {
  const db = await getMainDb();
  await db.transaction(async (tx) => {
    const result = await tx
      .insert(adminInfos)
      .values({
        userId: data.userId,
        userPwd: data.userPwd,
        userNm: data.userNm,
        userEmail: data.userEmail,
        userPhone: data.userPhone,
        userDepr: data.userDepr,
        isValid: "N",
      })
      .returning();
    const adminUid = result[0].uid;
    await tx.insert(adminRoles).values({
      adminUid: adminUid,
      tenantCd: data.tenantCd,
      userRole: ADMIN_ROLE.UNAUTHORIZED,
    });
    addAdminLogTx(tx, {
      tenantCd: data.tenantCd,
      adminUid: adminUid,
      action: ADMIN_ACTION.AUTH_SIGN_UP,
      description: `회원가입신청: ${data.userId}`,
      clientInfo,
    });
  });
};

export const checkAdminIdExists = async (userId: string): Promise<boolean> => {
  const db = await getMainDb();
  const result = await db
    .select({ id: adminInfos.uid })
    .from(adminInfos)
    .where(eq(adminInfos.userId, userId))
    .limit(1);
  return result.length > 0;
};

export const getAdminInfo = async (userId: string) => {
  const db = await getMainDb();
  const result = await db
    .select()
    .from(adminInfos)
    .where(eq(adminInfos.userId, userId))
    .limit(1);
  return result[0] ?? null;
};

export const getAdminTenants = async (adminUid: number) => {
  const db = await getMainDb();
  const result = await db
    .select({ adminRoles: adminRoles, tenants: saasTenants })
    .from(adminRoles)
    .innerJoin(saasTenants, eq(saasTenants.tenantCd, adminRoles.tenantCd))
    .where(eq(adminRoles.adminUid, adminUid));
  return result;
};
