import { Button } from "@workspace/ui/components/button";
import { cn } from "@workspace/ui/lib/utils";
import { useEffect } from "react";

export const Pagination = ({
  count,
  page,
  pageSize,
  onPageChange,
  className,
}: {
  count: number;
  page: number;
  pageSize: number;
  onPageChange: (page: number) => void;
  className?: string;
}) => {
  const pageCount = Math.ceil(count / pageSize);
  if (page < 1) page = 1;
  else if (page > pageCount) page = pageCount;

  return (
    <div className={cn("flex items-center justify-end gap-2", className)}>
      <div>
        총 {count}건 | {page} / {pageCount}
      </div>
      <Button
        onClick={() => onPageChange(page - 1)}
        disabled={page === 1}
        variant="outline"
      >
        이전
      </Button>
      <Button
        onClick={() => onPageChange(page + 1)}
        variant="outline"
        disabled={page === pageCount}
      >
        다음
      </Button>
    </div>
  );
};
