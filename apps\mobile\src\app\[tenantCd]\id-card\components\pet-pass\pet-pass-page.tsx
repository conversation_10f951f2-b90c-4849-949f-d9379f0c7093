"use client";

import { PrimaryButton } from "@/components/button";
import { cn } from "@workspace/ui/lib/utils";
import { PlusIcon } from "lucide-react";
import Head from "next/head";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { useMemo, useState } from "react";
import { useQueryPets } from "../../actions/pet-query";
import { PetPassCard, PetPassCardSkeleton } from "./pet-pass-card";
import { PetPassCarousel } from "./pet-pass-carousel";

const PetPassEmpty = ({ className }: { className?: string }) => {
  const router = useRouter();

  const handleRegist = () => {
    router.push("./id-card/pet-regist");
  };
  return (
    <div
      className={cn(
        "flex h-[350px] flex-col items-center justify-center rounded-[18px] bg-white px-[48px] py-[20px] shadow-lg",
        className,
      )}
    >
      <div className="flex flex-1 flex-col items-center justify-center">
        <div className="mb-[20px]">
          <Image
            src="/assets/id-card/pet-pass/add-pet.png"
            width={94}
            height={48}
            alt="고양이와 강아지"
          />
        </div>

        <h2 className="mb-[80px] text-center text-[14px] font-medium">
          사랑하는 나의 가족, 반려동물의 신분증을 등록해주세요
        </h2>
      </div>
      <div>
        <PrimaryButton
          className="align-center h-[40px] gap-[8px] text-[14px]"
          onClick={handleRegist}
        >
          <PlusIcon className="h-[18px] w-[18px]" />
          반려동물 등록하기
        </PrimaryButton>
      </div>
    </div>
  );
};

export const PetPassPage = () => {
  const { data: petData, isLoading } = useQueryPets();
  const [count, setCount] = useState<[number, number]>([0, 0]);

  const children = useMemo(
    () => [
      ...(petData?.data ?? []).map((item, index) => (
        <PetPassCard key={index} data={item} />
      )),
      <PetPassEmpty
        className={(petData?.data?.length ?? 0) > 0 ? "mr-[20px]" : undefined}
      />,
    ],
    [petData],
  );

  const handleCarouselValue = (index: number, maxIndex: number) => {
    setCount([index, maxIndex]);
  };

  return (
    <div className="-mx-[20px] flex h-full flex-1 flex-col">
      <Head>
        <title>반려동물신분증</title>
      </Head>
      {isLoading ? (
        <PetPassCardSkeleton className="m-[20px]" />
      ) : (
        <PetPassCarousel
          className="-ml-[20px] pb-[20px] pl-[20px]"
          onSelectSnap={handleCarouselValue}
        >
          {children}
        </PetPassCarousel>
      )}
      <div className="flex flex-row items-center justify-center px-[20px]">
        <div className="text-[14px] text-[#808c98]">
          {count[0] + 1} / {count[1]}
        </div>
      </div>
    </div>
  );
};
