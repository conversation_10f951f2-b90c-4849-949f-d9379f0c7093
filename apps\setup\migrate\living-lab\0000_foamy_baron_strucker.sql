CREATE TABLE "tb_ll_poll_file" (
	"uid" serial PRIMARY KEY NOT NULL,
	"poll_uid" integer,
	"poll_item_uid" integer,
	"original_name" text NOT NULL,
	"uri" text NOT NULL,
	"val_yn" varchar(1) DEFAULT 'Y' NOT NULL,
	"reg_dt" timestamp DEFAULT now() NOT NULL,
	"upd_dt" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "tb_ll_poll_item" (
	"uid" serial PRIMARY KEY NOT NULL,
	"poll_uid" integer NOT NULL,
	"name" text NOT NULL,
	"content" text NOT NULL,
	"order" integer DEFAULT 0 NOT NULL,
	"val_yn" varchar(1) DEFAULT 'Y' NOT NULL,
	"reg_dt" timestamp DEFAULT now() NOT NULL,
	"upd_dt" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "tb_ll_poll_ref" (
	"uid" serial PRIMARY KEY NOT NULL,
	"poll_uid" integer NOT NULL,
	"suggest_uid" integer,
	"val_yn" varchar(1) DEFAULT 'Y' NOT NULL,
	"reg_dt" timestamp DEFAULT now() NOT NULL,
	"upd_dt" timestamp DEFAULT now()
);
--> statement-breakpoint
CREATE TABLE "tb_ll_poll_vote" (
	"uid" serial PRIMARY KEY NOT NULL,
	"poll_uid" integer NOT NULL,
	"poll_item_uid" integer NOT NULL,
	"user_did" varchar(60) NOT NULL,
	"val_yn" varchar(1) DEFAULT 'Y' NOT NULL,
	"reg_dt" timestamp DEFAULT now() NOT NULL,
	"upd_dt" timestamp DEFAULT now() NOT NULL,
	CONSTRAINT "uq_ll_poll_vote" UNIQUE("user_did","poll_uid","poll_item_uid")
);
--> statement-breakpoint
CREATE TABLE "tb_ll_poll" (
	"uid" serial PRIMARY KEY NOT NULL,
	"depr" text NOT NULL,
	"name" text NOT NULL,
	"category" varchar(1) NOT NULL,
	"title" text NOT NULL,
	"content" text NOT NULL,
	"voter_cnt" integer DEFAULT 0 NOT NULL,
	"start_dt" timestamp NOT NULL,
	"end_dt" timestamp NOT NULL,
	"state" varchar(1) NOT NULL,
	"result" varchar(1) DEFAULT 'N' NOT NULL,
	"reason" text,
	"val_yn" varchar(1) DEFAULT 'Y' NOT NULL,
	"reg_dt" timestamp DEFAULT now() NOT NULL,
	"upd_dt" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "tb_ll_post_file" (
	"uid" integer PRIMARY KEY GENERATED BY DEFAULT AS IDENTITY (sequence name "tb_ll_post_file_uid_seq" INCREMENT BY 1 MINVALUE 1 MAXVALUE 2147483647 START WITH 1 CACHE 1),
	"post_uid" integer,
	"original_name" text NOT NULL,
	"uri" text NOT NULL,
	"val_yn" varchar(1) DEFAULT 'Y' NOT NULL,
	"reg_dt" timestamp DEFAULT now() NOT NULL,
	"upd_dt" timestamp DEFAULT now()
);
--> statement-breakpoint
CREATE TABLE "tb_ll_post" (
	"uid" integer PRIMARY KEY GENERATED BY DEFAULT AS IDENTITY (sequence name "tb_ll_post_uid_seq" INCREMENT BY 1 MINVALUE 1 MAXVALUE 2147483647 START WITH 1 CACHE 1),
	"board" varchar(32) NOT NULL,
	"depr" text NOT NULL,
	"name" text NOT NULL,
	"category" text NOT NULL,
	"title" text NOT NULL,
	"content" text NOT NULL,
	"hit_cnt" integer DEFAULT 0 NOT NULL,
	"start_dt" timestamp NOT NULL,
	"end_dt" timestamp NOT NULL,
	"val_yn" varchar(1) DEFAULT 'Y' NOT NULL,
	"reg_dt" timestamp DEFAULT now() NOT NULL,
	"upd_dt" timestamp DEFAULT now()
);
--> statement-breakpoint
CREATE TABLE "tb_ll_suggest_cmt_vote" (
	"uid" serial PRIMARY KEY NOT NULL,
	"user_did" varchar(60) NOT NULL,
	"suggest_uid" integer NOT NULL,
	"cmt_uid" integer NOT NULL,
	"vote" smallint NOT NULL,
	"reg_dt" timestamp DEFAULT now() NOT NULL,
	"upd_dt" timestamp DEFAULT now() NOT NULL,
	CONSTRAINT "uq_ll_suggest_cmt_vote" UNIQUE("user_did","suggest_uid","cmt_uid")
);
--> statement-breakpoint
CREATE TABLE "tb_ll_suggest_comment" (
	"uid" serial PRIMARY KEY NOT NULL,
	"user_did" varchar(60) NOT NULL,
	"suggest_uid" integer NOT NULL,
	"parent_uid" integer,
	"name" text NOT NULL,
	"content" text NOT NULL,
	"cmt_cnt" integer DEFAULT 0 NOT NULL,
	"up_cnt" integer DEFAULT 0 NOT NULL,
	"val_yn" varchar(1) DEFAULT 'Y' NOT NULL,
	"reg_dt" timestamp DEFAULT now() NOT NULL,
	"upd_dt" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "tb_ll_suggest_file" (
	"uid" serial PRIMARY KEY NOT NULL,
	"suggest_uid" integer,
	"cmt_uid" integer,
	"original_name" text NOT NULL,
	"uri" text NOT NULL,
	"val_yn" varchar(1) DEFAULT 'Y' NOT NULL,
	"reg_dt" timestamp DEFAULT now() NOT NULL,
	"upd_dt" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "tb_ll_suggest_vote" (
	"uid" serial PRIMARY KEY NOT NULL,
	"user_did" varchar(60) NOT NULL,
	"suggest_uid" integer NOT NULL,
	"vote" smallint NOT NULL,
	"reg_dt" timestamp DEFAULT now() NOT NULL,
	"upd_dt" timestamp DEFAULT now() NOT NULL,
	CONSTRAINT "uq_ll_suggest_vote" UNIQUE("user_did","suggest_uid")
);
--> statement-breakpoint
CREATE TABLE "tb_ll_suggest" (
	"uid" serial PRIMARY KEY NOT NULL,
	"user_did" varchar(60) NOT NULL,
	"name" text NOT NULL,
	"category" text NOT NULL,
	"title" text NOT NULL,
	"content" text NOT NULL,
	"hit_cnt" integer DEFAULT 0 NOT NULL,
	"cmt_cnt" integer DEFAULT 0 NOT NULL,
	"up_cnt" integer DEFAULT 0 NOT NULL,
	"down_cnt" integer DEFAULT 0 NOT NULL,
	"modreply_yn" varchar(1) DEFAULT 'N' NOT NULL,
	"adopt_yn" varchar(1) DEFAULT 'N' NOT NULL,
	"val_yn" varchar(1) DEFAULT 'Y' NOT NULL,
	"reg_dt" timestamp DEFAULT now() NOT NULL,
	"upd_dt" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "tb_ll_survey_ans" (
	"uid" integer PRIMARY KEY GENERATED BY DEFAULT AS IDENTITY (sequence name "tb_ll_survey_ans_uid_seq" INCREMENT BY 1 MINVALUE 1 MAXVALUE 2147483647 START WITH 1 CACHE 1),
	"survey_uid" integer NOT NULL,
	"item_uid" integer NOT NULL,
	"user_did" varchar(60) NOT NULL,
	"slct" integer,
	"answer" text,
	"val_yn" varchar(1) DEFAULT 'Y' NOT NULL,
	"reg_dt" timestamp DEFAULT now() NOT NULL,
	"upd_dt" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "tb_ll_survey_option" (
	"uid" integer PRIMARY KEY GENERATED BY DEFAULT AS IDENTITY (sequence name "tb_ll_survey_option_uid_seq" INCREMENT BY 1 MINVALUE 1 MAXVALUE 2147483647 START WITH 1 CACHE 1),
	"item_uid" integer NOT NULL,
	"content" text NOT NULL,
	"order" integer NOT NULL,
	"val_yn" varchar(1) DEFAULT 'Y' NOT NULL,
	"reg_dt" timestamp DEFAULT now() NOT NULL,
	"upd_dt" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "tb_ll_survey_item" (
	"uid" integer PRIMARY KEY GENERATED BY DEFAULT AS IDENTITY (sequence name "tb_ll_survey_item_uid_seq" INCREMENT BY 1 MINVALUE 1 MAXVALUE 2147483647 START WITH 1 CACHE 1),
	"survey_uid" integer NOT NULL,
	"content" text NOT NULL,
	"guide" text NOT NULL,
	"order" integer NOT NULL,
	"type" varchar(1) NOT NULL,
	"req_yn" varchar(1) DEFAULT 'N' NOT NULL,
	"val_yn" varchar(1) DEFAULT 'Y' NOT NULL,
	"reg_dt" timestamp DEFAULT now() NOT NULL,
	"upd_dt" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "tb_ll_survey" (
	"uid" integer PRIMARY KEY GENERATED BY DEFAULT AS IDENTITY (sequence name "tb_ll_survey_uid_seq" INCREMENT BY 1 MINVALUE 1 MAXVALUE 2147483647 START WITH 1 CACHE 1),
	"depr" text NOT NULL,
	"name" text NOT NULL,
	"category" text NOT NULL,
	"title" text NOT NULL,
	"content" text NOT NULL,
	"val_yn" varchar(1) DEFAULT 'Y' NOT NULL,
	"start_dt" timestamp NOT NULL,
	"end_dt" timestamp NOT NULL,
	"state" varchar(1) NOT NULL,
	"reg_dt" timestamp DEFAULT now() NOT NULL,
	"upd_dt" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
ALTER TABLE "tb_ll_poll_file" ADD CONSTRAINT "tb_ll_poll_file_poll_uid_tb_ll_poll_uid_fk" FOREIGN KEY ("poll_uid") REFERENCES "public"."tb_ll_poll"("uid") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "tb_ll_poll_file" ADD CONSTRAINT "tb_ll_poll_file_poll_item_uid_tb_ll_poll_item_uid_fk" FOREIGN KEY ("poll_item_uid") REFERENCES "public"."tb_ll_poll_item"("uid") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "tb_ll_poll_item" ADD CONSTRAINT "tb_ll_poll_item_poll_uid_tb_ll_poll_uid_fk" FOREIGN KEY ("poll_uid") REFERENCES "public"."tb_ll_poll"("uid") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "tb_ll_poll_ref" ADD CONSTRAINT "tb_ll_poll_ref_poll_uid_tb_ll_poll_uid_fk" FOREIGN KEY ("poll_uid") REFERENCES "public"."tb_ll_poll"("uid") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "tb_ll_poll_ref" ADD CONSTRAINT "tb_ll_poll_ref_suggest_uid_tb_ll_suggest_uid_fk" FOREIGN KEY ("suggest_uid") REFERENCES "public"."tb_ll_suggest"("uid") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "tb_ll_poll_vote" ADD CONSTRAINT "tb_ll_poll_vote_poll_uid_tb_ll_poll_uid_fk" FOREIGN KEY ("poll_uid") REFERENCES "public"."tb_ll_poll"("uid") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "tb_ll_poll_vote" ADD CONSTRAINT "tb_ll_poll_vote_poll_item_uid_tb_ll_poll_item_uid_fk" FOREIGN KEY ("poll_item_uid") REFERENCES "public"."tb_ll_poll_item"("uid") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "tb_ll_post_file" ADD CONSTRAINT "tb_ll_post_file_post_uid_tb_ll_post_uid_fk" FOREIGN KEY ("post_uid") REFERENCES "public"."tb_ll_post"("uid") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "tb_ll_suggest_cmt_vote" ADD CONSTRAINT "tb_ll_suggest_cmt_vote_suggest_uid_tb_ll_suggest_uid_fk" FOREIGN KEY ("suggest_uid") REFERENCES "public"."tb_ll_suggest"("uid") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "tb_ll_suggest_cmt_vote" ADD CONSTRAINT "tb_ll_suggest_cmt_vote_cmt_uid_tb_ll_suggest_comment_uid_fk" FOREIGN KEY ("cmt_uid") REFERENCES "public"."tb_ll_suggest_comment"("uid") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "tb_ll_suggest_comment" ADD CONSTRAINT "tb_ll_suggest_comment_suggest_uid_tb_ll_suggest_uid_fk" FOREIGN KEY ("suggest_uid") REFERENCES "public"."tb_ll_suggest"("uid") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "tb_ll_suggest_comment" ADD CONSTRAINT "tb_ll_suggest_comment_parent_uid_tb_ll_suggest_comment_uid_fk" FOREIGN KEY ("parent_uid") REFERENCES "public"."tb_ll_suggest_comment"("uid") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "tb_ll_suggest_file" ADD CONSTRAINT "tb_ll_suggest_file_suggest_uid_tb_ll_suggest_uid_fk" FOREIGN KEY ("suggest_uid") REFERENCES "public"."tb_ll_suggest"("uid") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "tb_ll_suggest_file" ADD CONSTRAINT "tb_ll_suggest_file_cmt_uid_tb_ll_suggest_comment_uid_fk" FOREIGN KEY ("cmt_uid") REFERENCES "public"."tb_ll_suggest_comment"("uid") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "tb_ll_suggest_vote" ADD CONSTRAINT "tb_ll_suggest_vote_suggest_uid_tb_ll_suggest_uid_fk" FOREIGN KEY ("suggest_uid") REFERENCES "public"."tb_ll_suggest"("uid") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "tb_ll_survey_ans" ADD CONSTRAINT "tb_ll_survey_ans_survey_uid_tb_ll_survey_uid_fk" FOREIGN KEY ("survey_uid") REFERENCES "public"."tb_ll_survey"("uid") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "tb_ll_survey_ans" ADD CONSTRAINT "tb_ll_survey_ans_item_uid_tb_ll_survey_item_uid_fk" FOREIGN KEY ("item_uid") REFERENCES "public"."tb_ll_survey_item"("uid") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "tb_ll_survey_ans" ADD CONSTRAINT "tb_ll_survey_ans_slct_tb_ll_survey_option_uid_fk" FOREIGN KEY ("slct") REFERENCES "public"."tb_ll_survey_option"("uid") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "tb_ll_survey_option" ADD CONSTRAINT "tb_ll_survey_option_item_uid_tb_ll_survey_item_uid_fk" FOREIGN KEY ("item_uid") REFERENCES "public"."tb_ll_survey_item"("uid") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "tb_ll_survey_item" ADD CONSTRAINT "tb_ll_survey_item_survey_uid_tb_ll_survey_uid_fk" FOREIGN KEY ("survey_uid") REFERENCES "public"."tb_ll_survey"("uid") ON DELETE no action ON UPDATE no action;