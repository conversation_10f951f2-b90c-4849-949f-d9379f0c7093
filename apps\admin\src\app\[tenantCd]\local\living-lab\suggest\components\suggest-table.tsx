"use client";

import {
  ColumnDef,
  getCoreRowModel,
  useReactTable,
} from "@tanstack/react-table";
import { Suggest } from "@workspace/db/crud/living-lab/suggest";
import { Button } from "@workspace/ui/components/button";
import { Checkbox } from "@workspace/ui/components/checkbox";
import { DataTable } from "@workspace/ui/components/data-table";
import Link from "next/link";
import { useMemo } from "react";

export const SuggestTable = ({
  tenantId: tenantId,
  data,
}: {
  tenantId: string;
  data: Suggest[];
}) => {
  const columns: ColumnDef<Suggest>[] = useMemo(
    () => [
      {
        id: "select",
        header: ({ table }) => (
          <Checkbox
            checked={
              table.getIsAllPageRowsSelected() ||
              (table.getIsSomePageRowsSelected() && "indeterminate")
            }
            onCheckedChange={(value) =>
              table.toggleAllPageRowsSelected(!!value)
            }
            aria-label="Select all"
          />
        ),
        cell: ({ row }) => (
          <Checkbox
            checked={row.getIsSelected()}
            onCheckedChange={(value) => row.toggleSelected(!!value)}
            aria-label="Select row"
          />
        ),
        enableSorting: false,
        enableHiding: false,
      },
      {
        accessorKey: "uid",
        header: "#",
      },
      {
        accessorKey: "category",
        header: "분류",
      },
      {
        accessorKey: "title",
        header: "제목",
        cell: ({ cell, row }) => (
          <Button variant="link" asChild>
            <Link
              href={`/${tenantId}/local/living-lab/suggest/${row.original.uid}`}
            >
              {cell.renderValue<string>()}
            </Link>
          </Button>
        ),
      },
      {
        accessorKey: "regDate",
        header: "등록일",
      },
      {
        accessorKey: "name",
        header: "작성자",
      },
      {
        accessorKey: "upCount",
        header: "찬성수",
      },
      {
        accessorKey: "downCount",
        header: "반대수",
      },
      {
        accessorKey: "hitCount",
        header: "조회수",
      },
      {
        accessorKey: "modReply",
        header: "관리자답변여부",
      },
      {
        accessorKey: "isValid",
        header: "게시상태",
      },
      {
        accessorKey: "isAdopted",
        header: "채택여부",
      },
    ],
    [],
  );

  const table = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
  });

  return (
    <div className="rounded-md border">
      <DataTable table={table} />
    </div>
  );
};
