{"id": "1daa9425-31ff-4cab-8d65-07a0e825dded", "prevId": "00000000-0000-0000-0000-000000000000", "version": "7", "dialect": "postgresql", "tables": {"public.tb_ll_poll_file": {"name": "tb_ll_poll_file", "schema": "", "columns": {"uid": {"name": "uid", "type": "serial", "primaryKey": true, "notNull": true}, "poll_uid": {"name": "poll_uid", "type": "integer", "primaryKey": false, "notNull": false}, "poll_item_uid": {"name": "poll_item_uid", "type": "integer", "primaryKey": false, "notNull": false}, "original_name": {"name": "original_name", "type": "text", "primaryKey": false, "notNull": true}, "uri": {"name": "uri", "type": "text", "primaryKey": false, "notNull": true}, "val_yn": {"name": "val_yn", "type": "<PERSON><PERSON><PERSON>(1)", "primaryKey": false, "notNull": true, "default": "'Y'"}, "reg_dt": {"name": "reg_dt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "upd_dt": {"name": "upd_dt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"tb_ll_poll_file_poll_uid_tb_ll_poll_uid_fk": {"name": "tb_ll_poll_file_poll_uid_tb_ll_poll_uid_fk", "tableFrom": "tb_ll_poll_file", "tableTo": "tb_ll_poll", "columnsFrom": ["poll_uid"], "columnsTo": ["uid"], "onDelete": "no action", "onUpdate": "no action"}, "tb_ll_poll_file_poll_item_uid_tb_ll_poll_item_uid_fk": {"name": "tb_ll_poll_file_poll_item_uid_tb_ll_poll_item_uid_fk", "tableFrom": "tb_ll_poll_file", "tableTo": "tb_ll_poll_item", "columnsFrom": ["poll_item_uid"], "columnsTo": ["uid"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.tb_ll_poll_item": {"name": "tb_ll_poll_item", "schema": "", "columns": {"uid": {"name": "uid", "type": "serial", "primaryKey": true, "notNull": true}, "poll_uid": {"name": "poll_uid", "type": "integer", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": true}, "order": {"name": "order", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "val_yn": {"name": "val_yn", "type": "<PERSON><PERSON><PERSON>(1)", "primaryKey": false, "notNull": true, "default": "'Y'"}, "reg_dt": {"name": "reg_dt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "upd_dt": {"name": "upd_dt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"tb_ll_poll_item_poll_uid_tb_ll_poll_uid_fk": {"name": "tb_ll_poll_item_poll_uid_tb_ll_poll_uid_fk", "tableFrom": "tb_ll_poll_item", "tableTo": "tb_ll_poll", "columnsFrom": ["poll_uid"], "columnsTo": ["uid"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.tb_ll_poll_ref": {"name": "tb_ll_poll_ref", "schema": "", "columns": {"uid": {"name": "uid", "type": "serial", "primaryKey": true, "notNull": true}, "poll_uid": {"name": "poll_uid", "type": "integer", "primaryKey": false, "notNull": true}, "suggest_uid": {"name": "suggest_uid", "type": "integer", "primaryKey": false, "notNull": false}, "val_yn": {"name": "val_yn", "type": "<PERSON><PERSON><PERSON>(1)", "primaryKey": false, "notNull": true, "default": "'Y'"}, "reg_dt": {"name": "reg_dt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "upd_dt": {"name": "upd_dt", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"tb_ll_poll_ref_poll_uid_tb_ll_poll_uid_fk": {"name": "tb_ll_poll_ref_poll_uid_tb_ll_poll_uid_fk", "tableFrom": "tb_ll_poll_ref", "tableTo": "tb_ll_poll", "columnsFrom": ["poll_uid"], "columnsTo": ["uid"], "onDelete": "no action", "onUpdate": "no action"}, "tb_ll_poll_ref_suggest_uid_tb_ll_suggest_uid_fk": {"name": "tb_ll_poll_ref_suggest_uid_tb_ll_suggest_uid_fk", "tableFrom": "tb_ll_poll_ref", "tableTo": "tb_ll_suggest", "columnsFrom": ["suggest_uid"], "columnsTo": ["uid"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.tb_ll_poll_vote": {"name": "tb_ll_poll_vote", "schema": "", "columns": {"uid": {"name": "uid", "type": "serial", "primaryKey": true, "notNull": true}, "poll_uid": {"name": "poll_uid", "type": "integer", "primaryKey": false, "notNull": true}, "poll_item_uid": {"name": "poll_item_uid", "type": "integer", "primaryKey": false, "notNull": true}, "user_did": {"name": "user_did", "type": "<PERSON><PERSON><PERSON>(60)", "primaryKey": false, "notNull": true}, "val_yn": {"name": "val_yn", "type": "<PERSON><PERSON><PERSON>(1)", "primaryKey": false, "notNull": true, "default": "'Y'"}, "reg_dt": {"name": "reg_dt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "upd_dt": {"name": "upd_dt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"tb_ll_poll_vote_poll_uid_tb_ll_poll_uid_fk": {"name": "tb_ll_poll_vote_poll_uid_tb_ll_poll_uid_fk", "tableFrom": "tb_ll_poll_vote", "tableTo": "tb_ll_poll", "columnsFrom": ["poll_uid"], "columnsTo": ["uid"], "onDelete": "no action", "onUpdate": "no action"}, "tb_ll_poll_vote_poll_item_uid_tb_ll_poll_item_uid_fk": {"name": "tb_ll_poll_vote_poll_item_uid_tb_ll_poll_item_uid_fk", "tableFrom": "tb_ll_poll_vote", "tableTo": "tb_ll_poll_item", "columnsFrom": ["poll_item_uid"], "columnsTo": ["uid"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"uq_ll_poll_vote": {"name": "uq_ll_poll_vote", "nullsNotDistinct": false, "columns": ["user_did", "poll_uid", "poll_item_uid"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.tb_ll_poll": {"name": "tb_ll_poll", "schema": "", "columns": {"uid": {"name": "uid", "type": "serial", "primaryKey": true, "notNull": true}, "depr": {"name": "depr", "type": "text", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "category": {"name": "category", "type": "<PERSON><PERSON><PERSON>(1)", "primaryKey": false, "notNull": true}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": true}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": true}, "voter_cnt": {"name": "voter_cnt", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "start_dt": {"name": "start_dt", "type": "timestamp", "primaryKey": false, "notNull": true}, "end_dt": {"name": "end_dt", "type": "timestamp", "primaryKey": false, "notNull": true}, "state": {"name": "state", "type": "<PERSON><PERSON><PERSON>(1)", "primaryKey": false, "notNull": true}, "result": {"name": "result", "type": "<PERSON><PERSON><PERSON>(1)", "primaryKey": false, "notNull": true, "default": "'N'"}, "reason": {"name": "reason", "type": "text", "primaryKey": false, "notNull": false}, "val_yn": {"name": "val_yn", "type": "<PERSON><PERSON><PERSON>(1)", "primaryKey": false, "notNull": true, "default": "'Y'"}, "reg_dt": {"name": "reg_dt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "upd_dt": {"name": "upd_dt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.tb_ll_post_file": {"name": "tb_ll_post_file", "schema": "", "columns": {"uid": {"name": "uid", "type": "integer", "primaryKey": true, "notNull": true, "identity": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "tb_ll_post_file_uid_seq", "schema": "public", "increment": "1", "startWith": "1", "minValue": "1", "maxValue": "2147483647", "cache": "1", "cycle": false}}, "post_uid": {"name": "post_uid", "type": "integer", "primaryKey": false, "notNull": false}, "original_name": {"name": "original_name", "type": "text", "primaryKey": false, "notNull": true}, "uri": {"name": "uri", "type": "text", "primaryKey": false, "notNull": true}, "val_yn": {"name": "val_yn", "type": "<PERSON><PERSON><PERSON>(1)", "primaryKey": false, "notNull": true, "default": "'Y'"}, "reg_dt": {"name": "reg_dt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "upd_dt": {"name": "upd_dt", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"tb_ll_post_file_post_uid_tb_ll_post_uid_fk": {"name": "tb_ll_post_file_post_uid_tb_ll_post_uid_fk", "tableFrom": "tb_ll_post_file", "tableTo": "tb_ll_post", "columnsFrom": ["post_uid"], "columnsTo": ["uid"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.tb_ll_post": {"name": "tb_ll_post", "schema": "", "columns": {"uid": {"name": "uid", "type": "integer", "primaryKey": true, "notNull": true, "identity": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "tb_ll_post_uid_seq", "schema": "public", "increment": "1", "startWith": "1", "minValue": "1", "maxValue": "2147483647", "cache": "1", "cycle": false}}, "board": {"name": "board", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": false, "notNull": true}, "depr": {"name": "depr", "type": "text", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "category": {"name": "category", "type": "text", "primaryKey": false, "notNull": true}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": true}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": true}, "hit_cnt": {"name": "hit_cnt", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "start_dt": {"name": "start_dt", "type": "timestamp", "primaryKey": false, "notNull": true}, "end_dt": {"name": "end_dt", "type": "timestamp", "primaryKey": false, "notNull": true}, "val_yn": {"name": "val_yn", "type": "<PERSON><PERSON><PERSON>(1)", "primaryKey": false, "notNull": true, "default": "'Y'"}, "reg_dt": {"name": "reg_dt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "upd_dt": {"name": "upd_dt", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.tb_ll_suggest_cmt_vote": {"name": "tb_ll_suggest_cmt_vote", "schema": "", "columns": {"uid": {"name": "uid", "type": "serial", "primaryKey": true, "notNull": true}, "user_did": {"name": "user_did", "type": "<PERSON><PERSON><PERSON>(60)", "primaryKey": false, "notNull": true}, "suggest_uid": {"name": "suggest_uid", "type": "integer", "primaryKey": false, "notNull": true}, "cmt_uid": {"name": "cmt_uid", "type": "integer", "primaryKey": false, "notNull": true}, "vote": {"name": "vote", "type": "smallint", "primaryKey": false, "notNull": true}, "reg_dt": {"name": "reg_dt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "upd_dt": {"name": "upd_dt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"tb_ll_suggest_cmt_vote_suggest_uid_tb_ll_suggest_uid_fk": {"name": "tb_ll_suggest_cmt_vote_suggest_uid_tb_ll_suggest_uid_fk", "tableFrom": "tb_ll_suggest_cmt_vote", "tableTo": "tb_ll_suggest", "columnsFrom": ["suggest_uid"], "columnsTo": ["uid"], "onDelete": "no action", "onUpdate": "no action"}, "tb_ll_suggest_cmt_vote_cmt_uid_tb_ll_suggest_comment_uid_fk": {"name": "tb_ll_suggest_cmt_vote_cmt_uid_tb_ll_suggest_comment_uid_fk", "tableFrom": "tb_ll_suggest_cmt_vote", "tableTo": "tb_ll_suggest_comment", "columnsFrom": ["cmt_uid"], "columnsTo": ["uid"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"uq_ll_suggest_cmt_vote": {"name": "uq_ll_suggest_cmt_vote", "nullsNotDistinct": false, "columns": ["user_did", "suggest_uid", "cmt_uid"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.tb_ll_suggest_comment": {"name": "tb_ll_suggest_comment", "schema": "", "columns": {"uid": {"name": "uid", "type": "serial", "primaryKey": true, "notNull": true}, "user_did": {"name": "user_did", "type": "<PERSON><PERSON><PERSON>(60)", "primaryKey": false, "notNull": true}, "suggest_uid": {"name": "suggest_uid", "type": "integer", "primaryKey": false, "notNull": true}, "parent_uid": {"name": "parent_uid", "type": "integer", "primaryKey": false, "notNull": false}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": true}, "cmt_cnt": {"name": "cmt_cnt", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "up_cnt": {"name": "up_cnt", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "val_yn": {"name": "val_yn", "type": "<PERSON><PERSON><PERSON>(1)", "primaryKey": false, "notNull": true, "default": "'Y'"}, "reg_dt": {"name": "reg_dt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "upd_dt": {"name": "upd_dt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"tb_ll_suggest_comment_suggest_uid_tb_ll_suggest_uid_fk": {"name": "tb_ll_suggest_comment_suggest_uid_tb_ll_suggest_uid_fk", "tableFrom": "tb_ll_suggest_comment", "tableTo": "tb_ll_suggest", "columnsFrom": ["suggest_uid"], "columnsTo": ["uid"], "onDelete": "no action", "onUpdate": "no action"}, "tb_ll_suggest_comment_parent_uid_tb_ll_suggest_comment_uid_fk": {"name": "tb_ll_suggest_comment_parent_uid_tb_ll_suggest_comment_uid_fk", "tableFrom": "tb_ll_suggest_comment", "tableTo": "tb_ll_suggest_comment", "columnsFrom": ["parent_uid"], "columnsTo": ["uid"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.tb_ll_suggest_file": {"name": "tb_ll_suggest_file", "schema": "", "columns": {"uid": {"name": "uid", "type": "serial", "primaryKey": true, "notNull": true}, "suggest_uid": {"name": "suggest_uid", "type": "integer", "primaryKey": false, "notNull": false}, "cmt_uid": {"name": "cmt_uid", "type": "integer", "primaryKey": false, "notNull": false}, "original_name": {"name": "original_name", "type": "text", "primaryKey": false, "notNull": true}, "uri": {"name": "uri", "type": "text", "primaryKey": false, "notNull": true}, "val_yn": {"name": "val_yn", "type": "<PERSON><PERSON><PERSON>(1)", "primaryKey": false, "notNull": true, "default": "'Y'"}, "reg_dt": {"name": "reg_dt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "upd_dt": {"name": "upd_dt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"tb_ll_suggest_file_suggest_uid_tb_ll_suggest_uid_fk": {"name": "tb_ll_suggest_file_suggest_uid_tb_ll_suggest_uid_fk", "tableFrom": "tb_ll_suggest_file", "tableTo": "tb_ll_suggest", "columnsFrom": ["suggest_uid"], "columnsTo": ["uid"], "onDelete": "no action", "onUpdate": "no action"}, "tb_ll_suggest_file_cmt_uid_tb_ll_suggest_comment_uid_fk": {"name": "tb_ll_suggest_file_cmt_uid_tb_ll_suggest_comment_uid_fk", "tableFrom": "tb_ll_suggest_file", "tableTo": "tb_ll_suggest_comment", "columnsFrom": ["cmt_uid"], "columnsTo": ["uid"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.tb_ll_suggest_vote": {"name": "tb_ll_suggest_vote", "schema": "", "columns": {"uid": {"name": "uid", "type": "serial", "primaryKey": true, "notNull": true}, "user_did": {"name": "user_did", "type": "<PERSON><PERSON><PERSON>(60)", "primaryKey": false, "notNull": true}, "suggest_uid": {"name": "suggest_uid", "type": "integer", "primaryKey": false, "notNull": true}, "vote": {"name": "vote", "type": "smallint", "primaryKey": false, "notNull": true}, "reg_dt": {"name": "reg_dt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "upd_dt": {"name": "upd_dt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"tb_ll_suggest_vote_suggest_uid_tb_ll_suggest_uid_fk": {"name": "tb_ll_suggest_vote_suggest_uid_tb_ll_suggest_uid_fk", "tableFrom": "tb_ll_suggest_vote", "tableTo": "tb_ll_suggest", "columnsFrom": ["suggest_uid"], "columnsTo": ["uid"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"uq_ll_suggest_vote": {"name": "uq_ll_suggest_vote", "nullsNotDistinct": false, "columns": ["user_did", "suggest_uid"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.tb_ll_suggest": {"name": "tb_ll_suggest", "schema": "", "columns": {"uid": {"name": "uid", "type": "serial", "primaryKey": true, "notNull": true}, "user_did": {"name": "user_did", "type": "<PERSON><PERSON><PERSON>(60)", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "category": {"name": "category", "type": "text", "primaryKey": false, "notNull": true}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": true}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": true}, "hit_cnt": {"name": "hit_cnt", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "cmt_cnt": {"name": "cmt_cnt", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "up_cnt": {"name": "up_cnt", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "down_cnt": {"name": "down_cnt", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "modreply_yn": {"name": "modreply_yn", "type": "<PERSON><PERSON><PERSON>(1)", "primaryKey": false, "notNull": true, "default": "'N'"}, "adopt_yn": {"name": "adopt_yn", "type": "<PERSON><PERSON><PERSON>(1)", "primaryKey": false, "notNull": true, "default": "'N'"}, "val_yn": {"name": "val_yn", "type": "<PERSON><PERSON><PERSON>(1)", "primaryKey": false, "notNull": true, "default": "'Y'"}, "reg_dt": {"name": "reg_dt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "upd_dt": {"name": "upd_dt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.tb_ll_survey_ans": {"name": "tb_ll_survey_ans", "schema": "", "columns": {"uid": {"name": "uid", "type": "integer", "primaryKey": true, "notNull": true, "identity": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "tb_ll_survey_ans_uid_seq", "schema": "public", "increment": "1", "startWith": "1", "minValue": "1", "maxValue": "2147483647", "cache": "1", "cycle": false}}, "survey_uid": {"name": "survey_uid", "type": "integer", "primaryKey": false, "notNull": true}, "item_uid": {"name": "item_uid", "type": "integer", "primaryKey": false, "notNull": true}, "user_did": {"name": "user_did", "type": "<PERSON><PERSON><PERSON>(60)", "primaryKey": false, "notNull": true}, "slct": {"name": "slct", "type": "integer", "primaryKey": false, "notNull": false}, "answer": {"name": "answer", "type": "text", "primaryKey": false, "notNull": false}, "val_yn": {"name": "val_yn", "type": "<PERSON><PERSON><PERSON>(1)", "primaryKey": false, "notNull": true, "default": "'Y'"}, "reg_dt": {"name": "reg_dt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "upd_dt": {"name": "upd_dt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"tb_ll_survey_ans_survey_uid_tb_ll_survey_uid_fk": {"name": "tb_ll_survey_ans_survey_uid_tb_ll_survey_uid_fk", "tableFrom": "tb_ll_survey_ans", "tableTo": "tb_ll_survey", "columnsFrom": ["survey_uid"], "columnsTo": ["uid"], "onDelete": "no action", "onUpdate": "no action"}, "tb_ll_survey_ans_item_uid_tb_ll_survey_item_uid_fk": {"name": "tb_ll_survey_ans_item_uid_tb_ll_survey_item_uid_fk", "tableFrom": "tb_ll_survey_ans", "tableTo": "tb_ll_survey_item", "columnsFrom": ["item_uid"], "columnsTo": ["uid"], "onDelete": "no action", "onUpdate": "no action"}, "tb_ll_survey_ans_slct_tb_ll_survey_option_uid_fk": {"name": "tb_ll_survey_ans_slct_tb_ll_survey_option_uid_fk", "tableFrom": "tb_ll_survey_ans", "tableTo": "tb_ll_survey_option", "columnsFrom": ["slct"], "columnsTo": ["uid"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.tb_ll_survey_option": {"name": "tb_ll_survey_option", "schema": "", "columns": {"uid": {"name": "uid", "type": "integer", "primaryKey": true, "notNull": true, "identity": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "tb_ll_survey_option_uid_seq", "schema": "public", "increment": "1", "startWith": "1", "minValue": "1", "maxValue": "2147483647", "cache": "1", "cycle": false}}, "item_uid": {"name": "item_uid", "type": "integer", "primaryKey": false, "notNull": true}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": true}, "order": {"name": "order", "type": "integer", "primaryKey": false, "notNull": true}, "val_yn": {"name": "val_yn", "type": "<PERSON><PERSON><PERSON>(1)", "primaryKey": false, "notNull": true, "default": "'Y'"}, "reg_dt": {"name": "reg_dt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "upd_dt": {"name": "upd_dt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"tb_ll_survey_option_item_uid_tb_ll_survey_item_uid_fk": {"name": "tb_ll_survey_option_item_uid_tb_ll_survey_item_uid_fk", "tableFrom": "tb_ll_survey_option", "tableTo": "tb_ll_survey_item", "columnsFrom": ["item_uid"], "columnsTo": ["uid"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.tb_ll_survey_item": {"name": "tb_ll_survey_item", "schema": "", "columns": {"uid": {"name": "uid", "type": "integer", "primaryKey": true, "notNull": true, "identity": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "tb_ll_survey_item_uid_seq", "schema": "public", "increment": "1", "startWith": "1", "minValue": "1", "maxValue": "2147483647", "cache": "1", "cycle": false}}, "survey_uid": {"name": "survey_uid", "type": "integer", "primaryKey": false, "notNull": true}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": true}, "guide": {"name": "guide", "type": "text", "primaryKey": false, "notNull": true}, "order": {"name": "order", "type": "integer", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "<PERSON><PERSON><PERSON>(1)", "primaryKey": false, "notNull": true}, "req_yn": {"name": "req_yn", "type": "<PERSON><PERSON><PERSON>(1)", "primaryKey": false, "notNull": true, "default": "'N'"}, "val_yn": {"name": "val_yn", "type": "<PERSON><PERSON><PERSON>(1)", "primaryKey": false, "notNull": true, "default": "'Y'"}, "reg_dt": {"name": "reg_dt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "upd_dt": {"name": "upd_dt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"tb_ll_survey_item_survey_uid_tb_ll_survey_uid_fk": {"name": "tb_ll_survey_item_survey_uid_tb_ll_survey_uid_fk", "tableFrom": "tb_ll_survey_item", "tableTo": "tb_ll_survey", "columnsFrom": ["survey_uid"], "columnsTo": ["uid"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.tb_ll_survey": {"name": "tb_ll_survey", "schema": "", "columns": {"uid": {"name": "uid", "type": "integer", "primaryKey": true, "notNull": true, "identity": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "tb_ll_survey_uid_seq", "schema": "public", "increment": "1", "startWith": "1", "minValue": "1", "maxValue": "2147483647", "cache": "1", "cycle": false}}, "depr": {"name": "depr", "type": "text", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "category": {"name": "category", "type": "text", "primaryKey": false, "notNull": true}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": true}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": true}, "val_yn": {"name": "val_yn", "type": "<PERSON><PERSON><PERSON>(1)", "primaryKey": false, "notNull": true, "default": "'Y'"}, "start_dt": {"name": "start_dt", "type": "timestamp", "primaryKey": false, "notNull": true}, "end_dt": {"name": "end_dt", "type": "timestamp", "primaryKey": false, "notNull": true}, "state": {"name": "state", "type": "<PERSON><PERSON><PERSON>(1)", "primaryKey": false, "notNull": true}, "reg_dt": {"name": "reg_dt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "upd_dt": {"name": "upd_dt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}