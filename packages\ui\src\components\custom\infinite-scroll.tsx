"use client";

import { ReactNode, useEffect, useRef } from "react";

export const InfiniteScroll = ({
  children,
  hasMore,
  loadMore,
  loadMoreComponent = null,
  className,
}: {
  children: ReactNode;
  hasMore: boolean;
  loadMore: () => void;
  loadMoreComponent?: ReactNode | null;
  className?: string;
}) => {
  const observerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        if (entries[0].isIntersecting && hasMore) {
          loadMore();
        }
      },
      {
        root: null,
        rootMargin: "0px",
        threshold: 1.0,
      },
    );
    if (observerRef.current) {
      observer.observe(observerRef.current);
    }
    return () => observer.disconnect();
  }, [hasMore, loadMore]);

  return (
    <div className={className}>
      {children}
      {hasMore && (
        <div className="mt-4 flex justify-center" ref={observerRef}>
          {loadMoreComponent}
        </div>
      )}
    </div>
  );
};
