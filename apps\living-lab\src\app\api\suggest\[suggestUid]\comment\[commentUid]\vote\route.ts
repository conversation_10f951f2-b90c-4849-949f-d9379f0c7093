import { increaseSuggestCommentVote } from "@workspace/db/crud/living-lab/suggest";
import { getSession } from "@workspace/utils/auth/session";
import { NextRequest, NextResponse } from "next/server";

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ suggestUid: number; commentUid: number }> },
) {
  const session = await getSession();
  if (!session)
    return NextResponse.json(new Error("Unauthorized"), { status: 401 });
  const { suggestUid, commentUid } = await params;

  const { vote } = await request.json();
  const voteNumber = Number(vote) !== 0 ? 1 : 0;

  await increaseSuggestCommentVote(
    session.tenantCd,
    session.userDid,
    suggestUid,
    commentUid,
    voteNumber,
  );
  return NextResponse.json({ vote });
}
