"use client";

import { SURVEY_CATEGORIES } from "@/app/utils/consts";
import { FilterSelect } from "@/components/filter-select";
import { UnderbarTabs } from "@workspace/ui/components/underbar-tabs";
import dayjs from "dayjs";
import { usePathname, useRouter } from "next/navigation";
import { useCallback } from "react";

export const SurveyFilter = ({
  isClosed,
  category,
  year,
  selectableYears,
  count,
}: {
  isClosed: boolean;
  category: string | undefined;
  year: number;
  selectableYears: number[];
  count: number;
}) => {
  const router = useRouter();
  const pathname = usePathname();

  const pollStatus = isClosed ? "closed" : "open";
  const categories = SURVEY_CATEGORIES.map((category) => ({
    name: category,
    value: category,
  }));

  const categoryValue =
    category && SURVEY_CATEGORIES.includes(category) ? category : undefined;
  const yearValue = selectableYears.includes(year) ? year : dayjs().year();

  const handleFilter = useCallback(
    (
      status: string,
      categoryValue: string | undefined,
      yearValue: number | undefined,
    ) => {
      const closed = status === "closed";
      const params = new URLSearchParams();
      if (categoryValue) params.set("category", categoryValue);
      if (closed && yearValue) {
        params.set("year", yearValue.toString());
      }
      if (closed) params.set("closed", "true");
      router.replace(`${pathname}?${params.toString()}`);
    },
    [pathname, router],
  );

  const handleCategoryChange = (newCategory: string | undefined) => {
    handleFilter(pollStatus, newCategory, year);
  };

  const handleYearChange = (newYear: number) => {
    handleFilter(pollStatus, category, newYear);
  };

  return (
    <div>
      <FilterSelect
        category={categoryValue}
        categories={categories}
        showYears={isClosed}
        year={year}
        selectableYears={selectableYears}
        count={count}
        handleCategoryChange={handleCategoryChange}
        handleYearChange={handleYearChange}
      />
      <UnderbarTabs
        onValueChange={(value) => {
          handleFilter(value, category, yearValue);
        }}
        value={isClosed ? "closed" : "open"}
        items={[
          { label: "진행 중인 설문", value: "open" },
          { label: "완료된 설문", value: "closed" },
        ]}
      />
    </div>
  );
};
