import { SUGGEST_CATEGORIES } from "@/app/utils/consts";
import { savePendingFiles } from "../common/form";

export const parseSuggestForm = async (request: Request) => {
  const formData = await request.formData();
  const category = formData.get("category")?.toString() ?? "";
  const title = formData.get("title")?.toString();
  const content = formData.get("content")?.toString();
  if (!title || !content) {
    throw new Error("내용 오류");
  }

  const activeFiles = formData
    .getAll("activeFiles")
    .map((item) => ({ uid: Number(item.toString()) }));
  const pendingFiles = formData.getAll("pendingFiles");
  if (!SUGGEST_CATEGORIES.includes(category) || !title || !content) {
    throw new Error("내용 오류");
  }

  const savedFiles = await savePendingFiles(pendingFiles);

  return {
    category,
    title,
    content,
    savedFiles,
    activeFiles,
  };
};
