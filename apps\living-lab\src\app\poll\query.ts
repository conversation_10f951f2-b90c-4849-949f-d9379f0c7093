import { postPollVote } from "@/actions/poll";
import { useMutation } from "@tanstack/react-query";

export const useMutationPollVotes = ({
  pollUid,
}: {
  pollUid: number | null;
}) => {
  return useMutation({
    mutationKey: ["poll/vote", pollUid],
    mutationFn: async ({
      items,
      signature,
    }: {
      items: number[];
      signature: string;
    }) => {
      if (!pollUid) return;
      const result = await postPollVote(pollUid, items, signature);
      return result;
    },
  });
};
