"use client";

import { usePollStore } from "@/app/poll/store-provider";
import Header from "@/components/common/header";
import { useRouter } from "next/navigation";
import { ReactNode, useEffect } from "react";
import { useShallow } from "zustand/shallow";

export const PollVoteContainer = ({
  pollUid,
  voteStep,
  signStep,
}: {
  pollUid: number;
  voteStep: ReactNode;
  signStep: ReactNode;
}) => {
  const route = useRouter();
  const [step, setStep, reset] = usePollStore(
    useShallow((state) => [state.step, state.setStep, state.reset]),
  );
  useEffect(() => {
    reset();
    setStep("vote");
  }, []);

  return (
    <main className="flex max-h-screen min-h-screen flex-col bg-[#FFFFFF]">
      <Header
        title="투표하기"
        parent={`/poll/${pollUid}`}
        onBackPress={() => {
          if (step === "sign") setStep("vote");
          else route.back();
        }}
      />
      <div className="flex flex-1 overflow-y-hidden">
        <div className="flex flex-1 flex-col overflow-y-auto">
          {step === "vote" ? voteStep : step === "sign" ? signStep : null}
        </div>
      </div>
    </main>
  );
};
