import { AppContent } from "@/app/[tenantCd]/components/container";
import { isMainTenant } from "@/lib/tenant";
import { readUserByUserDid } from "@workspace/db/crud/common/user";
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "@workspace/ui/components/tabs";
import { Suspense } from "react";
import {
  UserDetailHeader,
  UserDetailHeaderSkeleton,
} from "./components/user-detail-header";
import { UserDetailProfile } from "./components/user-detail-profile";
import { UserDetailTerms } from "./components/user-detail-terms";
import { UserDetailVc } from "./components/user-detail-vc";

const UserDetailContent = async ({
  tenantCd,
  userDid,
}: {
  tenantCd: string;
  userDid: string;
}) => {
  const userData = await readUserByUserDid(tenantCd, userDid);
  if (!userData) {
    return (
      <div className="flex h-[100px] w-full items-center justify-center">
        사용자를 찾을 수 없습니다.
      </div>
    );
  }

  return (
    <UserDetailHeader userData={userData}>
      <Tabs defaultValue="basic" className="w-full">
        <TabsList>
          <TabsTrigger value="basic">기본 정보</TabsTrigger>
          <TabsTrigger value="terms">약관/알림 동의</TabsTrigger>
          {!isMainTenant(tenantCd) && (
            <TabsTrigger value="vc">인증내역</TabsTrigger>
          )}
          <TabsTrigger value="ex-service">외부서비스</TabsTrigger>
          <TabsTrigger value="pet-pass">반려동물</TabsTrigger>
          <TabsTrigger value="pedometer">걸음수</TabsTrigger>
        </TabsList>
        <TabsContent value="basic">
          <UserDetailProfile
            userData={userData}
            isMainTenant={isMainTenant(tenantCd)}
          />
        </TabsContent>
        <TabsContent value="terms">
          <UserDetailTerms userData={userData} />
        </TabsContent>
        {!isMainTenant(tenantCd) && (
          <TabsContent value="vc">
            <UserDetailVc tenantCd={tenantCd} userData={userData} />
          </TabsContent>
        )}
      </Tabs>
    </UserDetailHeader>
  );
};

export default async function UserDetailPage({
  params,
}: Readonly<{
  params: Promise<{ tenantCd: string; userDid: string }>;
}>) {
  const { tenantCd, userDid } = await params;
  const decodedUserDid = decodeURIComponent(userDid);

  return (
    <AppContent title="회원상세정보">
      <Suspense fallback={<UserDetailHeaderSkeleton />}>
        <UserDetailContent tenantCd={tenantCd} userDid={decodedUserDid} />
      </Suspense>
    </AppContent>
  );
}
