"use client";

import { SessionPayload } from "@/lib/session";
import { useGlobalStore } from "@/lib/store/store-provider";
import { Button } from "@workspace/ui/components/button";
import { ArrowLeft } from "iconoir-react";
import { useRouter } from "next/navigation";
import { ReactNode } from "react";

export const AppHeader = ({
  tenantName,
  children,
}: {
  tenantName: string;
  children: ReactNode;
}) => {
  return (
    <div className="flex h-[60px]">
      <div className="fixed flex h-[60px] w-full justify-between border-b-[1px]">
        <div className="ml-[40px] flex items-center justify-center">
          <img
            src="/assets/header-logo.svg"
            alt="CITY Wallet 로고"
            className="h-[18px] w-[128px]"
          />
          <div className="ml-[16px]">{tenantName}</div>
        </div>
        <div className="mr-[40px] flex items-center">{children}</div>
      </div>
    </div>
  );
};

export const AppBodyContainer = ({ children }: { children: ReactNode }) => {
  return (
    <div className="h-[calc(100vh-60px)] w-full bg-[#fafafa]">
      <div className="relative h-full w-full overflow-auto p-[20px]">
        {children}
      </div>
    </div>
  );
};

export const AppContent = ({
  title,
  titleSuffix,
  hasBack,
  children,
}: {
  title: ReactNode;
  titleSuffix?: ReactNode;
  hasBack?: boolean;
  children?: ReactNode;
}) => {
  const router = useRouter();

  return (
    <>
      <div className="flex items-center">
        {hasBack && (
          <Button variant="none" onClick={() => router.back()}>
            <ArrowLeft width={24} height={24} />
          </Button>
        )}
        <div className="flex flex-1 items-center justify-between">
          <div className="flex">
            <h1 className="pb-[10px] text-[24px] font-normal">{title}</h1>
          </div>
          {titleSuffix && (
            <div className="flex text-[14px] text-gray-500">{titleSuffix}</div>
          )}
        </div>
      </div>
      {children}
    </>
  );
};

export const AppContainer = ({
  session,
  children,
}: {
  session: SessionPayload;
  children: ReactNode;
}) => {
  const setSession = useGlobalStore((state) => state.setSession);
  setSession(session);

  return <>{children}</>;
};
