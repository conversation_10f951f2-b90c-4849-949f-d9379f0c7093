"use client";

import { markAllMessagesAsRead } from "@/actions/push/push-action";
import { useInfiniteQueryPushHistory } from "@/actions/push/push-query";
import { ButtonNone } from "@/components/button";
import { Card, CardContent } from "@workspace/ui/components/card";
import { InfiniteScroll } from "@workspace/ui/components/custom/infinite-scroll";
import { Skeleton } from "@workspace/ui/components/skeleton";
import { cn } from "@workspace/ui/lib/utils";
import { format } from "date-fns";
import { useEffect, useMemo } from "react";

type PushHistoryItemType = {
  id: string;
  title: string;
  body: string;
  regDate: string;
  read: boolean;
};

const PushHistoryItem = ({
  title,
  body,
  regDate,
  read,
}: PushHistoryItemType) => {
  const dateText = format(new Date(regDate), "yyyy.MM.dd HH:mm");
  return (
    <Card className={cn(!read && "bg-gray-50", "mb-2 border-0 shadow-sm")}>
      <CardContent className="p-[10px]">
        <h3 className="mb-[10px] text-[16px] font-semibold text-black">
          {title}
        </h3>
        <p className="mb-[10px] text-[12px] text-[#636F7A]">{dateText}</p>
        <p className="text-[13px] leading-relaxed text-[#636F7A]">{body}</p>
      </CardContent>
    </Card>
  );
};

const PushHistorySkeleton = () => {
  return (
    <div>
      {[...Array(5)].map((_, index) => (
        <Card key={index} className={cn("mb-2 border-0 shadow-sm")}>
          <CardContent className="mb-[20px] p-[10px]">
            <Skeleton className="mb-[10px] h-[16px] w-3/4" />
            <Skeleton className="mb-[10px] h-[12px] w-1/3" />
            <div className="space-y-1">
              <Skeleton className="h-[13px] w-full" />
              <Skeleton className="h-[13px] w-5/6" />
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
};

export const PushHistoryList = () => {
  const { data, fetchNextPage, hasNextPage, isLoading } =
    useInfiniteQueryPushHistory();

  const contents = useMemo(
    () =>
      data?.pages.flatMap((page) =>
        page.individualMessageList.map((message) => ({
          id: message.agentMsgId,
          title: message.messageTitle,
          body: message.messageBody,
          regDate: message.regDate,
          read: message.readCheck,
        })),
      ) ?? [],
    [data],
  );

  useEffect(() => {
    if (contents.length && contents.some((item) => !item.read)) {
      markAllMessagesAsRead();
    }
  }, [contents]);

  return (
    <div className="h-full w-full">
      {isLoading ? (
        <PushHistorySkeleton />
      ) : contents.length === 0 ? (
        <div className="flex h-full items-center justify-center p-4 text-center">
          알림이 없습니다.
        </div>
      ) : (
        <InfiniteScroll
          hasMore={hasNextPage}
          loadMore={fetchNextPage}
          loadMoreComponent={
            <div className="mt-4 text-center">더 불러오기...</div>
          }
        >
          {contents.map((item, index) => (
            <PushHistoryItem key={index} {...item} />
          ))}
        </InfiniteScroll>
      )}
    </div>
  );
};
