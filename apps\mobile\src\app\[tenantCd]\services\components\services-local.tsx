import { Menu } from "@/consts/menu-items";
import { Routes, useNavigate } from "@workspace/webview-state";
import { useEffect } from "react";
import { GyeptGreenPoint } from "./services-gyept";
import { ServiceList } from "./services-list";

const useGetServices = (tenantCd: string) => {
  const { navigate } = useNavigate();
  const services: Menu[] = [];
  if (tenantCd === "gyept") {
    services.push(
      ...[
        {
          items: [
            {
              title: "에너지음",
              imageUrl: "/assets/gyept/menu/green-g1.png",
              description: "빌딩 에너지 관리를 통해 절약하기",
              onPress: () => navigate(Routes.gyeptG1()),
            },
            {
              title: "이노베이션센터",
              imageUrl: "/assets/gyept/menu/green-innovation.png",
              description: "시민 참여형 혁신거점",
              onPress: () => navigate(Routes.gyeptG7()),
            },
            {
              title: "도시숲 시민활동",
              imageUrl: "/assets/gyept/menu/green-forest.png",
              description: "시민이 직접 참여해서 키우는 나무",
            },
            {
              title: "페트랑",
              imageUrl: "/assets/gyept/menu/green-g5.png",
              description: "분리수거에 적극 참여하고 포인트 받기",
              onPress: () => navigate(Routes.gyeptG4()),
            },
          ],
        },
        {
          title: "로컬 라운지",
          items: [
            {
              title: "공지사항",
              imageUrl: "/assets/gyept/menu/notice.png",
              onPress: () => window.open("https://blog.naver.com/pt_story"),
            },
            {
              title: "SNS",
              imageUrl: "/assets/gyept/menu/sns.png",
              onPress: () =>
                window.open("https://www.instagram.com/love.ptcity/"),
            },
            {
              title: "홈페이지",
              imageUrl: "/assets/gyept/menu/website.png",
              onPress: () => window.open("https://www.pyeongtaek.go.kr/"),
            },
            {
              title: "유튜브",
              imageUrl: "/assets/gyept/menu/youtube.png",
              onPress: () =>
                window.open("https://www.youtube.com/@pyeongtaek_city"),
            },
          ],
        },
      ],
    );
  }

  return services;
};

const TopContent = ({ tenantCd }: { tenantCd: string }) => {
  if (tenantCd !== "gyept") return null;

  return <GyeptGreenPoint />;
};

export const ServicesLocal = ({
  tenantCd,
  onNotFound,
}: {
  tenantCd: string;
  onNotFound: () => void;
}) => {
  const services = useGetServices(tenantCd);
  const Component =
    tenantCd === "gyept" ? <TopContent tenantCd={tenantCd} /> : null;

  useEffect(() => {
    if (!Component && services.length === 0) {
      onNotFound();
    }
  }, []);

  return (
    <div className="flex flex-col gap-[8px]">
      {Component}
      {services.map((item, index) => (
        <ServiceList key={index} data={item} />
      ))}
    </div>
  );
};
