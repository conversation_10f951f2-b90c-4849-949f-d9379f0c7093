import {
  boolean,
  char,
  integer,
  numeric,
  pgTable,
  smallint,
  timestamp,
  varchar,
} from "drizzle-orm/pg-core";

export const pointTransactions = pgTable("tb_point_transaction", {
  pntTxId: integer("pnt_tx_id").notNull().unique(),
  userDid: varchar("user_did", { length: 255 }),
  rewardCd: varchar("reward_cd", { length: 100 }),
  srvcCd: varchar("srvc_cd", { length: 100 }),
  pntTxType: varchar("pnt_tx_type", { length: 100 }),
  pntTypeCd: varchar("pnt_type_cd"),
  pntAmt: numeric("pnt_amt", { precision: 15, scale: 0 }),
  moneyBlnc: numeric("money_blnc"),
  pointBlnc: numeric("point_blnc"),
  crtDt: timestamp("crt_dt").notNull(),
  rxSig: varchar("rx_sig", { length: 255 }),
  txSig: varchar("tx_sig", { length: 255 }),
  txTimestamp: varchar("tx_timestamp", { length: 50 }),
  txHash: varchar("tx_hash", { length: 255 }),
  txRslt: varchar("tx_rslt", { length: 255 }),
  mkroot: varchar("mkroot", { length: 255 }),
  pntBlnc: numeric("pnt_blnc", { precision: 15, scale: 0 }),
});

export const pointEarnDetails = pgTable("point_earn_details", {
  srvcCd: varchar("srvc_cd", { length: 100 }),
  userDid: varchar("user_did", { length: 255 }),
  userNm: varchar("user_nm", { length: 100 }), // 사용자 이름
  telnoEnc: varchar("telno_enc", { length: 100 }), // 암호화된 전화번호
  brdt: varchar("brdt", { length: 20 }), // 생년월일
  gender: varchar("gender", { length: 10 }), // 성별
  pntAmt: numeric("pnt_amt", { precision: 15, scale: 0 }), // 적립액
  pointBlnc: numeric("point_blnc", { precision: 15, scale: 0 }), // 잔액
  crtDt: timestamp("crt_dt"), // 생성일시
  expDt: timestamp("exp_dt"), // 만료일시
  txRslt: varchar("tx_rslt", { length: 255 }), // 결과 상태
});

export const pointDetails = pgTable("tb_point_detail", {
  pntDetId: integer("pnt_det_id").primaryKey().notNull(), // SERIAL PK
  userDid: varchar("user_did", { length: 255 }),
  pntTxType: varchar("pnt_tx_type", { length: 100 }),
  pntTypeCd: varchar("pnt_type_cd", { length: 100 }),
  pntAmt: numeric("pnt_amt", { precision: 15, scale: 0 }),
  pntPrDetailId: integer("pnt_pr_detail_id").notNull(),
  pntTxId: integer("pnt_tx_id").notNull(),
  crtDt: timestamp("crt_dt").notNull(),
  expDt: timestamp("exp_dt"),
});

export const userDids = pgTable("tb_user_did", {
  userDid: varchar("user_did", { length: 255 }).notNull().unique(),
  userPincode: varchar("user_pincode", { length: 255 }).notNull(),
  didStts: varchar("did_stts", { length: 10 }).notNull(),
  hsmTk: varchar("hsm_tk", { length: 255 }).notNull(),
  issueDt: timestamp("issue_dt", { mode: "string" }).notNull(),
  dscdDt: timestamp("dscd_dt", { mode: "string" }),
});

export const users = pgTable("tb_user_info", {
  userDid: varchar("user_did", { length: 255 })
    .notNull()
    .references(() => userDids.userDid, {
      onDelete: "restrict",
      onUpdate: "restrict",
    }),
  userCi: varchar("user_ci", { length: 255 }),
  userNm: varchar("user_nm", { length: 100 }).notNull(),
  userStts: varchar("user_stts", { length: 10 }).notNull(),
  telnoEnc: varchar("telno_enc", { length: 100 }).notNull(),
  mblOt: varchar("mbl_ot", { length: 10 }),
  brdt: char("brdt", { length: 8 }),
  gender: varchar("gender", { length: 4 }),
  frgnrVal: smallint("frgnr_val").notNull(),
  citizenYn: smallint("citizen_yn"),
  joinDt: timestamp("join_dt").notNull(),
  whdwlDt: timestamp("whdwl_dt"),
  lastLgnDt: timestamp("last_lgn_dt"),
  lastChgDt: timestamp("last_chg_dt"),
});

export const pointPolicies = pgTable("tb_service_reward_info", {
  srvcCd: varchar("srvc_cd", { length: 100 }).notNull(),
  rewardCd: varchar("reward_cd", { length: 100 }).notNull(),
  rewardNm: varchar("reward_nm", { length: 100 }),
  rewardRate: varchar("reward_rate", { length: 100 }),
  amt: numeric("amt", { precision: 15, scale: 0 }),
  dailyLimitCount: smallint("daily_limit_count"),
  monthlyLimitCount: smallint("monthly_limit_count"),
  totalLimitCount: smallint("total_limit_count"),
  maxAmtOnce: numeric("max_amt_once", { precision: 15, scale: 0 }),
  maxAmtDaily: numeric("max_amt_daily", { precision: 15, scale: 0 }),
  maxAmtMonthly: numeric("max_amt_monthly", { precision: 15, scale: 0 }),
  maxAmtTotal: numeric("max_amt_total", { precision: 15, scale: 0 }),
});

// export const services = pgTable("tb_service", {
//   srvcCd: varchar("srvc_cd", { length: 100 }).notNull().primaryKey(),
//   srvcNm: varchar("srvc_nm", { length: 255 }),
//   srvcType: varchar("srvc_type", { length: 100 }),
//   pntTypeCd: varchar("pnt_type_cd", { length: 50 }),
//   srvcStts: varchar("srvc_stts", { length: 50 }),
//   srvcDid: varchar("srvc_did", { length: 255 }),
//   subNm: varchar("sub_nm", { length: 255 }),
//   srvcUrl: varchar("srvc_url", { length: 255 }),
//   srvcBudget: numeric("srvc_budget", { precision: 15, scale: 0 }),
//   bgngDt: timestamp("bgng_dt"),
//   endDt: timestamp("end_dt"),
// });
