import { getMainDb } from "@workspace/db/drizzle";
import { ADMIN_ACTION, adminLogs } from "@workspace/db/schema/admin/admin";
import { ClientInfo } from "@workspace/utils/client-utils";
import { ExtractTablesWithRelations } from "drizzle-orm";
import { PgTransaction } from "drizzle-orm/pg-core";
import { PostgresJsQueryResultHKT } from "drizzle-orm/postgres-js";

type AdminLogType = {
  tenantCd: string;
  adminUid: number;
  action: ADMIN_ACTION;
  description?: string;
  metadata?: any;
  clientInfo: ClientInfo;
};

export const addAdminLog = async (model: AdminLogType) => {
  const db = await getMainDb();
  await db.transaction(async (tx) => {
    await addAdminLogTx(tx, model);
  });
};

export const addAdminLogTx = async (
  tx: PgTransaction<
    PostgresJsQueryResultHKT,
    Record<string, unknown>,
    ExtractTablesWithRelations<Record<string, unknown>>
  >,
  {
    tenantCd,
    adminUid,
    action,
    description,
    metadata,
    clientInfo,
  }: AdminLogType,
) => {
  await tx.insert(adminLogs).values({
    tenantCd,
    adminUid,
    action,
    description: description ?? null,
    metadata: metadata ?? null,
    ipAddress: clientInfo.ip,
    userAgent: clientInfo.agent,
  });
};
