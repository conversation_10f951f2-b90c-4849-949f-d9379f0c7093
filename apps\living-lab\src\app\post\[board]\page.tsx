import { GuidePanel } from "@/app/suggest/components/common";
import { BOARDS } from "@/app/utils/consts";
import Header from "@/components/common/header";
import { EmptyList } from "@/components/common/list";
import {
  readPosts,
  readPostsMinYear,
} from "@workspace/db/crud/living-lab/post";
import { getSession } from "@workspace/utils/auth/session";
import dayjs from "dayjs";
import { notFound, redirect, RedirectType } from "next/navigation";
import { Suspense } from "react";
import { PostFilter } from "./components/post-filter";
import { PostItem } from "./components/post-item";

const PostContents = async ({
  tenantCd,
  board,
  category,
  categories,
  year,
}: {
  tenantCd: string;
  board: string;
  category?: string;
  categories: string[];
  year: number | undefined;
}) => {
  const currentYear = dayjs().year();
  const items = await readPosts({ tenantCd: tenantCd, board, category, year });
  const minYear = (await readPostsMinYear(tenantCd, board)) ?? currentYear;
  const years = Array.from(
    { length: currentYear - minYear + 1 },
    (_, i) => currentYear - i,
  );

  const selectableCategories = categories.map((category) => ({
    name: category,
    value: category,
  }));

  return (
    <>
      <PostFilter
        category={category}
        year={Number.isNaN(year) ? currentYear : year!}
        selectableYears={years}
        selectableCategories={selectableCategories}
        count={items.length}
      />
      <div className="flex flex-1 overflow-y-hidden">
        <div className="w-full overflow-y-auto bg-[#FAFBFF] py-[20px]">
          {items.length < 1 ? (
            <EmptyList>게시물이 없습니다.</EmptyList>
          ) : (
            <div className="space-[10px] flex flex-col space-y-[10px]">
              {items.map((item, index) => (
                <PostItem
                  key={index}
                  item={item}
                  className="mx-[20px] rounded-lg bg-white p-[20px] shadow-lg"
                />
              ))}
            </div>
          )}
        </div>
      </div>
    </>
  );
};

const BoardContentsSkeleton = () => {
  // TODO
  return null;
};

export default async function PostPage({
  params,
  searchParams,
}: {
  params: Promise<{ board: string }>;
  searchParams: Promise<{ category?: string; year?: string }>;
}) {
  const session = await getSession();
  if (!session) redirect("/", RedirectType.replace);

  const { tenantCd } = session;
  const { board } = await params;
  const { category, year } = await searchParams;

  const boardInfo = BOARDS.find((b) => b.value === board);
  if (boardInfo === undefined) notFound();

  return (
    <main className="flex max-h-screen min-h-screen flex-col bg-[#FFFFFF]">
      <Header title={boardInfo.title} parent="/home" />
      <div className="flex flex-1 overflow-y-hidden">
        <div className="flex flex-1 flex-col overflow-y-auto">
          <GuidePanel type={boardInfo.value} />
          <Suspense fallback={<BoardContentsSkeleton />}>
            <PostContents
              tenantCd={tenantCd}
              board={boardInfo.value}
              category={category}
              categories={boardInfo.categories}
              year={year ? Number.parseInt(year) : undefined}
            />
          </Suspense>
        </div>
      </div>
    </main>
  );
}
