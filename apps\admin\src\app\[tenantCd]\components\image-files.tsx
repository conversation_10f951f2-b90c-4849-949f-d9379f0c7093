export const getImageURL = (baseURL: string, uri: string): string => {
  return `${baseURL}/api/attachments/${uri}`;
};

export const ImageFiles = ({
  files,
  baseURL,
}: {
  files: { uri: string; originalName: string }[];
  baseURL: string;
}) => {
  if (files.length < 1) return;
  return (
    <div>
      {files.map((file, index) => (
        <div key={index} className="mb-[10px]">
          {
            // eslint-disable-next-line @next/next/no-img-element
            <img
              src={getImageURL(baseURL, file.uri)}
              alt={file.originalName}
              className="max-h-[320px] max-w-full border-[2px]"
            />
          }
        </div>
      ))}
    </div>
  );
};
