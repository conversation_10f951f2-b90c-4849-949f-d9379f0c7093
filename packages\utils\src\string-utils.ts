export const formatBirth = (dateString: string) => {
  // convert yyyyMMdd to yyyy-MM-dd
  return dateString.replace(/(\d{4})(\d{2})(\d{2})/, "$1.$2.$3");
};

export const maskedAddress = (address: string) => {
  const addressArray = address.split(" ");
  if (addressArray.length < 4) return address;
  return (
    `${addressArray[0]} ${addressArray[1]} ${addressArray[2]} ` +
    addressArray
      .splice(3)
      .map((item) => item.replace(/._*/g, "*"))
      .join(" ")
  );
};
