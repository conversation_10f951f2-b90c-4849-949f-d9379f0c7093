import {
  integer,
  pgTable,
  text,
  timestamp,
  varchar,
} from "drizzle-orm/pg-core";

export enum SURVEY_STATE {
  IN_PROGRESS = "I", // 진행중
  COUNTING = "C", // 집계중
  CLOSED = "X", // 종료
}
const surveyStates = [
  SURVEY_STATE.IN_PROGRESS,
  SURVEY_STATE.COUNTING,
  SURVEY_STATE.CLOSED,
] as const;

export enum SURVEY_ITEM_TYPE {
  SHORT_TEXT = "I", // 단문
  LONG_TEXT = "L", // 장문
  SINGLE_SELECT = "S", // 단일선택
  MULTIPLE_SELECT = "M", // 다중선택
}

const surveyItemTypes = [
  SURVEY_ITEM_TYPE.SHORT_TEXT,
  SURVEY_ITEM_TYPE.LONG_TEXT,
  SURVEY_ITEM_TYPE.SINGLE_SELECT,
  SURVEY_ITEM_TYPE.MULTIPLE_SELECT,
] as const;

export const surveys = pgTable("tb_ll_survey", {
  uid: integer("uid").primaryKey().generatedByDefaultAsIdentity(),
  depr: text("depr").notNull(),
  name: text("name").notNull(),
  category: text("category").notNull(),
  title: text("title").notNull(),
  content: text("content").notNull(),
  isValid: varchar("val_yn", { length: 1, enum: ["Y", "N"] })
    .default("Y")
    .notNull(),
  startDate: timestamp("start_dt").notNull(),
  endDate: timestamp("end_dt").notNull(),
  state: varchar("state", {
    length: 1,
    enum: surveyStates,
  }).notNull(),
  regDate: timestamp("reg_dt").defaultNow().notNull(),
  updDate: timestamp("upd_dt")
    .defaultNow()
    .notNull()
    .$onUpdate(() => new Date()),
});

export const surveyItems = pgTable("tb_ll_survey_item", {
  uid: integer("uid").primaryKey().generatedByDefaultAsIdentity(),
  surveyUid: integer("survey_uid")
    .references(() => surveys.uid)
    .notNull(),
  content: text("content").notNull(),
  guide: text("guide").notNull(),
  order: integer("order").notNull(),
  type: varchar("type", { length: 1, enum: surveyItemTypes }).notNull(),
  isRequired: varchar("req_yn", { length: 1 }).default("N").notNull(),
  isValid: varchar("val_yn", { length: 1 }).default("Y").notNull(),
  regDate: timestamp("reg_dt").defaultNow().notNull(),
  updDate: timestamp("upd_dt")
    .defaultNow()
    .notNull()
    .$onUpdate(() => new Date()),
});

export const surveyItemOptions = pgTable("tb_ll_survey_option", {
  uid: integer("uid").primaryKey().generatedByDefaultAsIdentity(),
  itemUid: integer("item_uid")
    .references(() => surveyItems.uid)
    .notNull(),
  content: text("content").notNull(),
  order: integer("order").notNull(),
  isValid: varchar("val_yn", { length: 1, enum: ["Y", "N"] })
    .default("Y")
    .notNull(),
  regDate: timestamp("reg_dt").defaultNow().notNull(),
  updDate: timestamp("upd_dt")
    .defaultNow()
    .notNull()
    .$onUpdate(() => new Date()),
});

export const surveyAnswers = pgTable("tb_ll_survey_ans", {
  uid: integer("uid").primaryKey().generatedByDefaultAsIdentity(),
  surveyUid: integer("survey_uid")
    .references(() => surveys.uid)
    .notNull(),
  itemUid: integer("item_uid")
    .references(() => surveyItems.uid)
    .notNull(),
  userDid: varchar("user_did", { length: 60 }).notNull(),
  select: integer("slct").references(() => surveyItemOptions.uid),
  answer: text("answer"),
  isValid: varchar("val_yn", { length: 1, enum: ["Y", "N"] })
    .default("Y")
    .notNull(),
  regDate: timestamp("reg_dt").defaultNow().notNull(),
  updDate: timestamp("upd_dt")
    .defaultNow()
    .notNull()
    .$onUpdate(() => new Date()),
});
