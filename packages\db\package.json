{"name": "@workspace/db", "version": "1.0.0", "description": "", "scripts": {"db:generate": "pnpm db:generate:admin && pnpm db:generate:living-lab && pnpm db:generate:pet-pass && pnpm db:generate:store", "db:generate:admin": "drizzle-kit generate --config=./configs/admin.ts", "db:generate:living-lab": "drizzle-kit generate --config=./configs/living-lab.ts", "db:generate:pet-pass": "drizzle-kit generate --config=./configs/pet-pass.ts", "db:generate:store": "drizzle-kit generate --config=./configs/store.ts", "db:push": "drizzle-kit push", "db:pull": "drizzle-kit pull", "db:seed": "ts-node ./src/seed/main.ts"}, "dependencies": {"@workspace/utils": "workspace:*", "date-fns": "^4.1.0", "dayjs": "^1.11.13", "dotenv": "^16.4.5", "drizzle-orm": "^0.44.2", "postgres": "^3.4.7"}, "devDependencies": {"@types/eslint": "catalog:", "@types/node": "catalog:", "@workspace/eslint-config": "workspace:*", "@workspace/typescript-config": "workspace:*", "drizzle-kit": "^0.31.1", "drizzle-seed": "^0.1.2", "eslint": "catalog:", "ts-node": "^10.9.2"}, "exports": {"./*": "./src/*.ts"}}