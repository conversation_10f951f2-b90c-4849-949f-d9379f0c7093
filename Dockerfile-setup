# syntax=docker.io/docker/dockerfile:1
ARG NODE_VERSION=20.11.0
FROM node:${NODE_VERSION}-slim AS slim

FROM slim AS base
RUN corepack enable && corepack prepare pnpm@9.12.3 --activate
RUN npm add --global turbo
RUN pnpm config set store-dir ~/.pnpm-store

FROM base AS pruner
ARG PROJECT=setup
ARG ENV_FILE

WORKDIR /app
COPY . .
RUN find . -name ".env.*" -exec rm {} \;
COPY apps/${PROJECT}/${ENV_FILE} apps/${PROJECT}

RUN if [ -n "${ENV_FILE}" ]; then \
	mv apps/${PROJECT}/${ENV_FILE} apps/${PROJECT}/.env.production; \
	fi

RUN pnpm turbo prune --scope=${PROJECT} --docker

FROM base AS builder
ARG TENANT_ID
WORKDIR /app

COPY --from=pruner /app/out/json/ .
RUN --mount=type=cache,id=pnpm,target=~/.pnpm-store pnpm install --frozen-lockfile

COPY --from=pruner /app/out/full/ .
RUN --mount=type=cache,id=pnpm,target=~/.pnpm-store pnpm prune --prod --no-optional

FROM base AS runner
COPY --from=builder /app/node_modules /app/node_modules
COPY --from=builder /app/apps/setup /app/apps/setup
COPY --from=builder /app/packages /app/packages

WORKDIR /app/apps/setup

ENTRYPOINT [ "pnpm", "migrate" ]