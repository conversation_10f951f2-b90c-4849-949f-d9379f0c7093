import { CategoryBadge } from "@/components/common/badge";
import { formatAsDate } from "@/lib/datetime";
import { getBaseURL } from "@/middleware";
import { convertNewlineToJSX } from "@toss/react";
import { Masker } from "@toss/utils";
import {
  increaseSuggestHitCount,
  readSuggest,
} from "@workspace/db/crud/living-lab/suggest";
import { buttonVariants } from "@workspace/ui/components/button";
import { Skeleton } from "@workspace/ui/components/skeleton";
import { cn } from "@workspace/ui/lib/utils";
import { getSession } from "@workspace/utils/auth/session";
import Link from "next/link";
import { notFound } from "next/navigation";
import { ImageFiles } from "../components/common";
import { SuggestComments } from "./comments";
import { SuggestVote } from "./vote";

export const SuggestContentSkeleton = () => {
  return (
    <div>
      <div className="mt-[10px] border-b-[1px] px-[20px] pb-[40px]">
        <div className="mb-[10px] flex flex-row items-center justify-between">
          <Skeleton className="h-[20px] w-[100px]" />
          <Skeleton className="h-[20px] w-[50px]" />
        </div>
        <Skeleton className="mb-[12px] h-[32px] w-full" />
        <div className="flex justify-between text-sm text-[#808C98]">
          <Skeleton className="h-[20px] w-[80px]" />
          <Skeleton className="h-[20px] w-[50px]" />
        </div>
        <div className="py-[20px]">
          <Skeleton className="mb-[20px] h-[200px] w-full" />
          <Skeleton className="mb-[10px] h-[20px] w-2/5" />
          <Skeleton className="mb-[10px] h-[20px] w-full" />
          <Skeleton className="mb-[10px] h-[20px] w-3/5" />
        </div>
      </div>
    </div>
  );
};

export const SuggestContent = async ({ uid }: { uid: number }) => {
  const session = await getSession();
  if (!session) return null;
  const content = await readSuggest(session.tenantCd, uid);
  if (!content) notFound();
  await increaseSuggestHitCount(session.tenantCd, uid);

  const hasEditPermission =
    session.userDid === content.userDid || session.roles.includes("ADMIN");

  return (
    <div>
      <div className="mt-[10px] border-b-[1px] px-[20px] pb-[40px]">
        <div className="mb-[10px] flex flex-row items-center justify-between">
          <CategoryBadge>{content.category}</CategoryBadge>
          <div className="text-[12px]">{Masker.maskName(content.name)}</div>
        </div>
        <h1 className="mb-[12px] text-[26px] font-semibold">{content.title}</h1>

        <div className="flex justify-between text-sm text-[#808C98]">
          <span>{formatAsDate(content.regDate)}</span>
          <span>조회수 {content.hitCount}</span>
        </div>
        <div className="py-[20px]">
          <ImageFiles baseURL={await getBaseURL()} files={content.files} />
          {convertNewlineToJSX(content.content)}
        </div>
        {hasEditPermission && (
          <div className="flex justify-end py-[20px]">
            <Link
              className={cn(
                buttonVariants({ variant: "outline" }),
                "rounded-[10px] text-[14px] font-medium",
              )}
              href={`/suggest/${uid}/edit`}
            >
              수정/삭제
            </Link>
          </div>
        )}
        <SuggestVote uid={uid} up={content.upCount} down={content.downCount} />
      </div>
      <div className="mb-[10px] px-[20px]">
        <SuggestComments uid={uid} count={content.commentCount} />
      </div>
    </div>
  );
};
