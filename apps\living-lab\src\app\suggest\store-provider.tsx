"use client";

import { createContext, ReactNode, useContext, useRef } from "react";
import { useStore } from "zustand";
import { createSuggestStore, SuggestStore } from "./store";

type SuggestStoreApi = ReturnType<typeof createSuggestStore>;

export const SuggestStoreContext = createContext<SuggestStoreApi | undefined>(
  undefined,
);

export const SuggestStoreProvider = ({ children }: { children: ReactNode }) => {
  const storeRef = useRef<SuggestStoreApi>(null);
  if (!storeRef.current) {
    storeRef.current = createSuggestStore();
  }
  return (
    <SuggestStoreContext.Provider value={storeRef.current}>
      {children}
    </SuggestStoreContext.Provider>
  );
};

export const useSuggestStore = <T,>(
  selector: (store: SuggestStore) => T,
): T => {
  const context = useContext(SuggestStoreContext);
  if (!context) {
    throw new Error("useSuggestStore must be used within a StoreProvider");
  }
  return useStore(context, selector);
};
