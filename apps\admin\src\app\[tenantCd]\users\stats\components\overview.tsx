import { commaizeNumber } from "@toss/utils";
import { readUserCount } from "@workspace/db/crud/common/user";
import { Card } from "@workspace/ui/components/card";

const StatCard = ({
  title,
  subtitle,
  value,
  className = "",
}: {
  title: string;
  subtitle?: string;
  value: number | undefined;
  className?: string;
}) => (
  <Card className={`p-4 ${className}`}>
    <div className="flex items-center justify-between">
      <div className="text-sm text-gray-600">
        {title}
        {subtitle && (
          <div className="mt-1 text-xs text-gray-400">({subtitle})</div>
        )}
      </div>
      {/* <CircleHelp size={16} /> */}
    </div>
    <div className="mt-2 text-2xl font-bold">
      {value !== undefined ? commaizeNumber(value) : "-"}
    </div>
  </Card>
);

const UserStatsCount = async ({ tenantCd }: { tenantCd: string }) => {
  const result = await readUserCount(tenantCd);
  return <StatCard title="전체 회원 현황" subtitle="누적" value={result} />;
};

const UserStatsDormant = async ({ tenantCd }: { tenantCd: string }) => {
  const result = await readUserCount(tenantCd, {
    lastLgnDt: [
      undefined,
      new Date(Date.now() - 1000 * 60 * 60 * 24 * 365 * 1),
    ], // 1년 이상 미사용 회원
    userStts: "NORMAL",
  });
  console.log(result);
  return <StatCard title="장기 미사용 회원" subtitle="누적" value={result} />;
};

export const UserStatsOverview = ({ tenantCd }: { tenantCd: string }) => {
  //TODO: 환불 요청 회원, 탈퇴 회원 처리
  return (
    <div className="flex w-80 flex-col gap-4">
      <UserStatsCount tenantCd={tenantCd} />
      <UserStatsDormant tenantCd={tenantCd} />
      <StatCard title="환불 요청 회원" subtitle="탈퇴대기" value={undefined} />
      <StatCard title="탈퇴 회원" subtitle="개인정보 파기" value={undefined} />
    </div>
  );
};
