export type CategoryType = {
  displayText: string;
  searchTag?: string;
  image: string;
  imageSelected: string;
};

export const categoryList: CategoryType[] = [
  {
    displayText: "커피/음료",
    image: "/images/category_coffee.svg",
    imageSelected: "/images/category_coffee_selected.svg",
  },
  {
    displayText: "베이커리/도넛",
    image: "/images/category_bakery.svg",
    imageSelected: "/images/category_bakery_selected.svg",
  },
  {
    displayText: "아이스크림",
    image: "/images/category_icecream.svg",
    imageSelected: "/images/category_icecream_selected.svg",
  },
  {
    displayText: "편의점",
    image: "/images/category_cvs.svg",
    imageSelected: "/images/category_cvs_selected.svg",
  },
  {
    displayText: "치킨/버거",
    image: "/images/category_burger.svg",
    imageSelected: "/images/category_burger_selected.svg",
  },
  {
    displayText: "상품권",
    searchTag: "마트상품권/백화점상품권",
    image: "/images/category_gift.svg",
    imageSelected: "/images/category_gift_selected.svg",
  },
  {
    displayText: "주유권",
    searchTag: "주유상품권",
    image: "/images/category_oil.svg",
    imageSelected: "/images/category_oil_selected.svg",
  },
];

export const convertTagToArray = (tag?: string) => {
  return tag
    ?.split("/")
    ?.map((tag: string) => tag.trim())
    ?.filter(Boolean);
};

export const checkCategory = (category: string) => {
  return categoryList.some((item) => {
    const searchTags = (item.searchTag ?? item.displayText).split("/") ?? [];
    return searchTags.includes(category);
  });
};

export const categoryAll = categoryList
  .map((item) => item.searchTag ?? item.displayText)
  .join("/");
