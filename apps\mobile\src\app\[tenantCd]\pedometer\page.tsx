import { fetchPedometerGoal } from "@/actions/pedometer/pedometer-action";
import { MainContainer } from "@/components/main-container";
import { BottomBar } from "./components/bottom-bar";
import { PedometerToday } from "./components/pedometer-today";
import { PedometerWeekly } from "./components/pedometer-weekly";

export default async function PedometerPage() {
  const goalCount = await fetchPedometerGoal();
  return (
    <MainContainer containerClassName="flex flex-col py-[20px]">
      <div className="flex h-full flex-1 flex-col gap-[12px]">
        <PedometerToday goalCount={goalCount.goalCount} />
        <PedometerWeekly />
      </div>
      <div>
        <BottomBar />
      </div>
    </MainContainer>
  );
}
