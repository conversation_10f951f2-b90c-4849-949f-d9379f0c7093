import { useModalStore } from "@workspace/ui/providers/modal-provider";
import { ModalOptions } from "@workspace/ui/providers/modal-store";
import { ReactNode, useCallback } from "react";
import { Button } from "../components/button";

export const useShowModalAlert = () => {
  const [showModal, hideModal] = useModalStore((state) => [
    state.showModal,
    state.hideModal,
  ]);

  return useCallback(
    ({
      title,
      children,
      options,
    }: {
      title?: string;
      children: ReactNode;
      options?: ModalOptions;
    }) => {
      const modalId = showModal({
        title: title ?? "알림",
        children: (
          <div>
            <div className="flex flex-col items-center justify-center text-[16px] font-medium text-[#636F7A]">
              {children}
            </div>
            <div className="mt-[20px]">
              <Button
                className="h-[40px] w-full"
                onClick={() => hideModal(modalId)}
              >
                확인
              </Button>
            </div>
          </div>
        ),
        options,
      });
      return modalId;
    },
    [],
  );
};
