import { formatAsDate } from "@/lib/datetime";
import { ReactNode } from "react";
import { CategoryBadge } from "./badge";

export const PostInfo = async ({
  title,
  children,
}: {
  title: string;
  children: ReactNode;
}) => {
  return (
    <div className="mb-[6px] text-[12px]">
      <span className="mr-[6px] text-[#808C98]">{title}</span>
      {children}
    </div>
  );
};

export const PostContent = async ({
  badges,
  title,
  name,
  depr,
  regDate,
  hitCount,
  headers,
  children,
}: {
  badges: (string | undefined)[];
  title: string;
  name: string;
  depr: string;
  regDate?: string | null;
  hitCount?: number;
  headers?: ReactNode;
  children: ReactNode;
}) => {
  return (
    <div className="border-b-[1px] border-[#F5F5F5] px-[20px] py-[10px]">
      <div className="mb-[10px] flex gap-[10px]">
        {badges.map(
          (badge, index) =>
            badge && <CategoryBadge key={index}>{badge}</CategoryBadge>,
        )}
      </div>
      <div className="mb-[20px] pb-[10px] text-[26px] font-semibold">
        {title}
      </div>
      <div className="mb-[20px] flex flex-row">
        <div className="mr-[14px]">
          <div className="h-[40px] w-[40px] rounded-sm bg-slate-400"></div>
        </div>
        <div className="justify-between">
          <div className="text-[14px] font-medium">{name}</div>
          <div className="text-[12px] text-[#808C98]">{depr}</div>
        </div>
      </div>
      {regDate || hitCount ? (
        <div className="flex justify-between text-sm text-[#808C98]">
          {regDate && <span>{formatAsDate(regDate)}</span>}
          {hitCount && <span>조회수 {hitCount}</span>}
        </div>
      ) : null}
      {headers}
      <div className="py-[40px] text-[14px]">{children}</div>
    </div>
  );
};
