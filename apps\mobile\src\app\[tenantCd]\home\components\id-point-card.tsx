import { useQueryPointBalance } from "@/actions/point/point-query";
import { PointBalanceType } from "@/actions/point/pont-action";
import { ButtonNone } from "@/components/button";
import { MAIN_TENANT_CD } from "@/consts/consts";
import assets, { ImageAsset } from "@/services/assets";
import { ImageAssetType } from "@/services/assets/types";
import { commaizeNumber } from "@toss/utils";
import { Skeleton } from "@workspace/ui/components/skeleton";
import { cn } from "@workspace/ui/lib/utils";
import { Routes, useNavigate } from "@workspace/webview-state";
import Image from "next/image";
import { Fragment } from "react";

const PointCardButtons = ({
  bgColor = "#256bff",
  separatorClassName = "bg-[#ffffff]",
  className,
}: {
  bgColor?: string;
  separatorClassName?: string;
  className?: string;
}) => {
  const { navigate } = useNavigate();
  const items = [
    {
      text: "전환",
      onPress: () => navigate(Routes.walletPointExport()),
    },
    {
      text: "내역",
      onPress: () => navigate(Routes.walletPointHistory()),
    },
  ];

  return (
    <div
      className={cn(
        "flex w-full gap-[8px] rounded-[6px] text-[#ffffff]",
        className,
      )}
      style={{ background: bgColor }}
    >
      {items.map((item, index) => (
        <Fragment key={index}>
          {index > 0 && (
            <div
              className={cn("h-[20px] w-[1px] self-center", separatorClassName)}
            ></div>
          )}
          <ButtonNone
            className="flex h-[32px] flex-1 items-center justify-center"
            onClick={(e) => {
              e.stopPropagation();
              item.onPress();
            }}
            asChild
          >
            <div className="gap-[10px]">
              <span className="text-[14px] font-medium">{item.text}</span>
            </div>
          </ButtonNone>
        </Fragment>
      ))}
    </div>
  );
};

const TenantLogo = ({
  logoImage,
}: {
  logoImage: ImageAssetType | undefined;
}) => {
  const { navigate } = useNavigate();
  const handleClick = () => {
    navigate(Routes.citizenId());
  };
  return (
    <ButtonNone onClick={!logoImage ? handleClick : undefined}>
      <div className="flex">
        {logoImage ? (
          <ImageAsset src={logoImage} />
        ) : (
          <span className="text-[12px] text-[#256bff]">내 지역 인증하기</span>
        )}
      </div>
    </ButtonNone>
  );
};

const UserName = ({ userName }: { userName: string }) => {
  const tracking = userName.length > 5 ? "tracking-[2px]" : "tracking-[10px]";

  return (
    <div className={cn("flex-1 text-end text-[18px] font-semibold", tracking)}>
      {userName}
    </div>
  );
};

const PointList = ({
  tenantCd,
  data,
  pointTextFgColor = "#256bff",
  pointTextBgColor = "#ecf2fd",
  className,
}: {
  tenantCd: string;
  data: PointBalanceType[] | undefined;
  pointTextFgColor?: string;
  pointTextBgColor?: string;
  itemClassName?: string;
  itemBgClassName?: string;
  className?: string;
}) => {
  const { navigate } = useNavigate();
  const openCitizenId = () => {
    navigate(Routes.citizenId());
  };

  if (!data) return null;
  const balanceList = data?.filter((item) => item.tenantCd !== MAIN_TENANT_CD);
  if (balanceList.length === 0) return null;

  return (
    <div className={cn("flex w-full flex-col gap-[2px]", className)}>
      {balanceList.map((item, index) => (
        <div key={index} className={cn("flex w-full flex-row gap-[4px]")}>
          <div className="flex flex-1 flex-col gap-[4px]">
            <div
              key={item.tenantCd}
              className={cn(
                "flex h-full w-full items-center justify-between rounded-[16px] px-[8px] py-[3px]",
              )}
              style={{
                backgroundColor: pointTextBgColor || "#f0f0f0",
                color: pointTextFgColor || "#000000",
              }}
            >
              <div className={cn("text-[12px] font-medium")}>{item.name}</div>
              <div className={cn("text-[14px] font-medium")}>
                {commaizeNumber(item.balance)} P
              </div>
            </div>
          </div>
          {tenantCd !== item.tenantCd && (
            <ButtonNone onClick={openCitizenId}>
              <div className="flex items-center justify-center rounded-[16px] border-[1px] border-[#256bff] bg-white p-[6px] text-[10px] font-medium text-[#256bff]">
                확인하러가기
              </div>
            </ButtonNone>
          )}
        </div>
      ))}
    </div>
  );
};

export const IdPointCard = ({
  tenantCd,
  userName,
  className,
}: {
  tenantCd: string;
  userName: string;
  className?: string;
}) => {
  const { navigate } = useNavigate();
  const { data } = useQueryPointBalance();
  const {
    backgroundImage,
    logoImage,
    pointTextFgColor,
    pointTextBgColor,
    pointButtonBgColor,
  } = assets(tenantCd).home.idCard;

  const totalBalance = data?.find(
    (item) => item.tenantCd === MAIN_TENANT_CD,
  )?.balance;

  const handleOpenIdCard = () => {
    navigate(Routes.idCard());
  };
  const handlePointPress = () => {
    navigate({
      url: "cw.app://main/wallet",
    });
  };

  return (
    <div className={cn("overflow-clip rounded-[10px] shadow-lg", className)}>
      <div
        className="flex min-h-[180px] flex-col items-center justify-between bg-white bg-cover bg-center bg-no-repeat p-[14px]"
        style={{
          backgroundImage: backgroundImage
            ? `url(${backgroundImage})`
            : undefined,
        }}
      >
        <div className="flex w-full flex-row items-start justify-between px-[4px]">
          <TenantLogo logoImage={logoImage} />
          <ButtonNone onClick={handleOpenIdCard}>
            <div className="flex justify-end">
              <UserName userName={userName} />
            </div>
          </ButtonNone>
        </div>
        <div className="flex flex-1" />

        <div className="mb-[12px] flex w-full items-end justify-end">
          <ButtonNone onClick={handlePointPress}>
            <div className="flex h-[24px] items-center text-[24px] font-semibold">
              <div className="mr-[4px]">
                {totalBalance !== undefined ? (
                  commaizeNumber(totalBalance)
                ) : (
                  <Skeleton className="h-[24px] w-[80px] bg-white/20" />
                )}
              </div>
              P
              <Image
                src="/assets/page-arrow-right-b.svg"
                width={20}
                height={20}
                alt="포인트"
              />
            </div>
          </ButtonNone>
        </div>
        <PointList
          tenantCd={tenantCd}
          data={data}
          pointTextFgColor={pointTextFgColor}
          pointTextBgColor={pointTextBgColor}
          className="mb-[10px]"
        />
        <PointCardButtons bgColor={pointButtonBgColor} />
      </div>
    </div>
  );
};
