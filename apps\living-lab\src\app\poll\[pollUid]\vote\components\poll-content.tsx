import { GuidePanel, GuideTypes } from "@/app/suggest/components/common";
import { readPoll, readPollItems } from "@workspace/db/crud/living-lab/poll";
import { POLL_CATEGORY } from "@workspace/db/schema/living-lab/poll";
import { notFound } from "next/navigation";
import { PollReferences } from "./poll-reference";
import { PollItemsComponent, PollVoteButton } from "./poll-vote-items";

export const PollVoteContent = async ({
  tenantCd,
  pollUid,
}: {
  tenantCd: string;
  pollUid: number;
}) => {
  const poll = await readPoll(tenantCd, pollUid);
  if (!poll) notFound();

  const guideTypes: { [key: string]: GuideTypes } = {
    [POLL_CATEGORY.GENERAL]: "pollGeneral",
    [POLL_CATEGORY.YES_NO]: "pollYesNo",
    [POLL_CATEGORY.MULTIPLE]: "pollMultiple",
  };

  return (
    <>
      <GuidePanel type={guideTypes[poll.category]} />
      <PollItems
        tenantId={tenantCd}
        pollUid={pollUid}
        type={poll.category === POLL_CATEGORY.MULTIPLE ? "multiple" : "single"}
      />
      <PollReferences tenantId={tenantCd} pollUid={pollUid} />
      <PollVoteButton pollUid={pollUid} />
    </>
  );
};

const PollItems = async ({
  tenantId,
  pollUid,
  type,
}: {
  tenantId: string;
  pollUid: number;
  type: "single" | "multiple";
}) => {
  const pollItems = await readPollItems(tenantId, pollUid);
  return (
    <div className="flex flex-col border-b-[1px] border-[#F5F5F5] px-[20px]">
      <PollItemsComponent pollItems={pollItems} type={type} />
    </div>
  );
};
