{"extends": "@workspace/typescript-config/nextjs.json", "compilerOptions": {"target": "ES2017", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "strict": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "plugins": [{"name": "next"}], "paths": {"@/*": ["./src/*"]}}, "include": ["svgr.d.ts", "next-env.d.ts", "next.config.ts", "postcss.config.mjs", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts", "../../packages/utils/src/files.ts"], "exclude": ["node_modules"]}