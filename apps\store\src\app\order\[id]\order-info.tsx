export const OrderStep = ({ step }: { step: "confirm" | "done" }) => {
  const colorActive = "#256bff";
  const colorInactive = "#d6d6d6";

  const status1Color = step == "confirm" ? colorActive : colorInactive;
  const status2Color = step == "done" ? colorActive : colorInactive;

  return (
    <div className="container mx-auto">
      <div className="flex items-center justify-center gap-2">
        <div>
          <p className="text-base font-bold" style={{ color: status1Color }}>
            01 주문 정보
          </p>
        </div>
        <div>
          <p className="text-[#d6d6d6]">{">"}</p>
        </div>
        <div>
          <p className="text-base font-bold" style={{ color: status2Color }}>
            02 주문 완료
          </p>
        </div>
      </div>
    </div>
  );
};

export const OrderTitle = ({ children }: { children: string }) => {
  return (
    <div className="border-b border-gray-200 py-[4px]">
      <h1 className="text-[16px] text-[#292929]">{children}</h1>
    </div>
  );
};
