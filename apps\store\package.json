{"name": "store", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack --port 3021", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@tanstack/react-query": "^5.59.19", "@toss/utils": "^1.6.1", "@workspace/db": "workspace:*", "@workspace/ui": "workspace:*", "@workspace/utils": "workspace:*", "@workspace/webview-state": "workspace:*", "axios": "^1.9.0", "date-fns": "^4.1.0", "lucide-react": "^0.454.0", "next": "catalog:next", "node-cron": "^4.0.7", "react": "^19.0.0", "react-dom": "^19.0.0", "usehooks-ts": "^3.1.1"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@workspace/eslint-config": "workspace:*", "@workspace/typescript-config": "workspace:*", "postcss": "catalog:", "tailwindcss": "catalog:", "typescript": "catalog:"}}