"use client";

import { createContext, ReactNode, useContext, useEffect, useRef } from "react";
import { useStore } from "zustand";
import { useShallow } from "zustand/shallow";
import { Dialog } from "../components/dialog";
import { createModalStore, ModalStore } from "./modal-store";

export type ModalStoreApi = ReturnType<typeof createModalStore>;

export const ModalStoreContext = createContext<ModalStoreApi | undefined>(
  undefined,
);

export const useModalStore = <T,>(selector: (store: ModalStore) => T): T => {
  const modalStoreContext = useContext(ModalStoreContext);
  if (!modalStoreContext) {
    throw new Error("store must be used within a StoreProvider");
  }
  return useStore(modalStoreContext, useShallow(selector));
};

export const ModalProvider = ({ children }: { children: ReactNode }) => {
  const storeRef = useRef<ModalStoreApi>(null);
  if (!storeRef.current) {
    storeRef.current = createModalStore();
  }
  return (
    <ModalStoreContext.Provider value={storeRef.current}>
      {children}
      <ModalContainer />
    </ModalStoreContext.Provider>
  );
};

export const ModalContainer = () => {
  const currentModalId = useRef<number | null>(null);
  const [modals, hideModal] = useModalStore((state) => [
    state.modals,
    state.hideModal,
  ]);
  const lastModal = modals.length > 0 ? modals[modals.length - 1] : null;
  const initialStateRef = useRef<boolean>(false);

  useEffect(() => {
    if (!initialStateRef.current) {
      const currentState = window.history.state || {};
      window.history.replaceState({ ...currentState }, "");
      initialStateRef.current = true;
    }

    if (!lastModal) {
      currentModalId.current = null;
      return;
    }

    if (currentModalId.current === lastModal.id) return;

    currentModalId.current = lastModal.id;
    const currentState = window.history.state || {};
    window.history.pushState({ ...currentState, modalId: lastModal.id }, "");

    const handlePopState = () => {
      if (currentModalId.current !== null) {
        hideModal(currentModalId.current);
        currentModalId.current = null;
      }
    };

    window.addEventListener("popstate", handlePopState);
    return () => {
      window.removeEventListener("popstate", handlePopState);
    };
  }, [lastModal, hideModal]);

  if (!lastModal) return null;
  const { id, content, isVisible } = lastModal;
  if (!content.children) return null;

  const handleOpenChange = (open: boolean) => {
    if (open) return;
    hideModal(id);
  };

  return (
    <Dialog open={isVisible} onOpenChange={(open) => handleOpenChange(open)}>
      {content.children}
    </Dialog>
  );
};
