html, body {
  touch-action: manipulation;
  overscroll-behavior-y: none;
  user-select: none;
  -webkit-tap-highlight-color: transparent;
}

*:not(input):not(textarea):not(select) {
  -webkit-touch-callout: none;
  -webkit-user-select: none;
}

img, a {
  /* pointer-events: none; */
  -webkit-user-drag: none;
  -webkit-touch-callout: none;
}

.theme-main {
  --primary-local: 30, 107, 255;
  --bg-from: #eef3ff;
  --bg-to: #F6F7F8;
}

.theme-gyept {
  --primary-local: 0, 157, 165;
  --secondary-local: 194, 239, 242;
  --secondary-local-foreground: 0, 157, 164;
  --bg-from: #E4F4F4;
  --bg-to: #F6F7F8;
}
