import {
  integer,
  pgTable,
  serial,
  text,
  timestamp,
  unique,
  varchar,
} from "drizzle-orm/pg-core";
import { suggests } from "./suggest";

export enum POLL_CATEGORY {
  GENERAL = "G", // 일반
  YES_NO = "Y", // 찬반
  MULTIPLE = "M", // 중복
}

const pollCategories = [
  POLL_CATEGORY.GENERAL,
  POLL_CATEGORY.YES_NO,
  POLL_CATEGORY.MULTIPLE,
] as const;

export enum POLL_STATE {
  VOTING = "V", // 투표중 or 기한 만료
  COUNTING = "C", // 집계중
  CLOSED = "X", // 종료
}

const pollStates = [
  POLL_STATE.VOTING,
  POLL_STATE.COUNTING,
  POLL_STATE.CLOSED,
] as const;

export const polls = pgTable("tb_ll_poll", {
  uid: serial("uid").primaryKey(),
  depr: text("depr").notNull(),
  name: text("name").notNull(),
  category: varchar("category", { length: 1, enum: pollCategories }).notNull(),
  title: text("title").notNull(),
  content: text("content").notNull(),
  voterCount: integer("voter_cnt").default(0).notNull(),
  startDate: timestamp("start_dt").notNull(),
  endDate: timestamp("end_dt").notNull(),
  state: varchar("state", { length: 1, enum: pollStates }).notNull(), // 투표 상태 - POLL_STATE
  result: varchar("result", { length: 1, enum: ["Y", "N"] })
    .default("N")
    .notNull(),
  reason: text("reason"),
  isValid: varchar("val_yn", { length: 1, enum: ["Y", "N"] })
    .default("Y")
    .notNull(),
  regDate: timestamp("reg_dt").defaultNow().notNull(),
  updDate: timestamp("upd_dt")
    .defaultNow()
    .notNull()
    .$onUpdate(() => new Date()),
});

export const pollReferences = pgTable("tb_ll_poll_ref", {
  uid: serial("uid").primaryKey(),
  pollUid: integer("poll_uid")
    .references(() => polls.uid)
    .notNull(),
  suggestUid: integer("suggest_uid").references(() => suggests.uid),
  //TOOD: 반영안내 게시글
  isValid: varchar("val_yn", { length: 1, enum: ["Y", "N"] })
    .default("Y")
    .notNull(),
  regDate: timestamp("reg_dt").defaultNow().notNull(),
  updDate: timestamp("upd_dt")
    .defaultNow()
    .$onUpdate(() => new Date()),
});

export const pollItems = pgTable("tb_ll_poll_item", {
  uid: serial("uid").primaryKey(),
  pollUid: integer("poll_uid")
    .references(() => polls.uid)
    .notNull(),
  name: text("name").notNull(),
  content: text("content").notNull(),
  order: integer("order").default(0).notNull(),
  isValid: varchar("val_yn", { length: 1, enum: ["Y", "N"] })
    .default("Y")
    .notNull(),
  regDate: timestamp("reg_dt").defaultNow().notNull(),
  updDate: timestamp("upd_dt")
    .defaultNow()
    .notNull()
    .$onUpdate(() => new Date()),
});

export const pollVotes = pgTable(
  "tb_ll_poll_vote",
  {
    uid: serial("uid").primaryKey(),
    pollUid: integer("poll_uid")
      .references(() => polls.uid)
      .notNull(),
    pollItemUid: integer("poll_item_uid")
      .references(() => pollItems.uid)
      .notNull(),
    userDid: varchar("user_did", { length: 60 }).notNull(),
    isValid: varchar("val_yn", { length: 1, enum: ["Y", "N"] })
      .default("Y")
      .notNull(),
    regDate: timestamp("reg_dt").defaultNow().notNull(),
    updDate: timestamp("upd_dt")
      .defaultNow()
      .notNull()
      .$onUpdate(() => new Date()),
  },
  (table) => [
    unique("uq_ll_poll_vote").on(
      table.userDid,
      table.pollUid,
      table.pollItemUid,
    ),
  ],
);

export const pollFiles = pgTable("tb_ll_poll_file", {
  uid: serial("uid").primaryKey(),
  pollUid: integer("poll_uid").references(() => polls.uid),
  pollItemUid: integer("poll_item_uid").references(() => pollItems.uid),
  originalName: text("original_name").notNull(),
  uri: text("uri").notNull(),
  isValid: varchar("val_yn", { length: 1, enum: ["Y", "N"] })
    .default("Y")
    .notNull(),
  regDate: timestamp("reg_dt").defaultNow().notNull(),
  updDate: timestamp("upd_dt")
    .defaultNow()
    .notNull()
    .$onUpdate(() => new Date()),
});
