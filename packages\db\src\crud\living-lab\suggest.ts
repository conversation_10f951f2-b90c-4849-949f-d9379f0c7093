import "server-only";
import {
  and,
  desc,
  eq,
  getTenantDb,
  isNotNull,
  isNull,
  min,
  notInArray,
} from "@workspace/db/drizzle";
import {
  suggestComments,
  suggestCommentVotes,
  suggestFiles,
  suggests,
  suggestVotes,
} from "@workspace/db/schema/living-lab/suggest";
import { betweenYear, increment } from "@workspace/db/utils";
import dayjs from "dayjs";

export type Suggest = typeof suggests.$inferSelect;
export type SuggestComment = typeof suggestComments.$inferSelect;
export type SuggestCommentDto = SuggestComment & { files: SuggestFile[] };
export type SuggestFile = typeof suggestFiles.$inferSelect;

export type SuggestInsert = typeof suggests.$inferInsert;
export type SuggestCommentInsert = typeof suggestComments.$inferInsert;
export type SuggestFileInsert = typeof suggestFiles.$inferInsert;

type AttachFile = { originalName: string; uri: string };

export const readSuggestMinYear = async (tenantId: string) => {
  const tenantDb = await getTenantDb(tenantId);
  const result = await tenantDb
    .select({ minDate: min(suggests.regDate) })
    .from(suggests)
    .where(eq(suggests.isValid, "Y"));
  return Number.parseInt(dayjs(result[0].minDate ?? new Date()).format("YYYY"));
};

export const readSuggests = async (
  tenantId: string,
  category: string | undefined,
  year: number | undefined,
) => {
  const tenantDb = await getTenantDb(tenantId);
  const result = await tenantDb
    .select({ suggest: suggests, files: suggestFiles })
    .from(suggests)
    .leftJoin(
      suggestFiles,
      and(
        eq(suggestFiles.suggestUid, suggests.uid),
        isNull(suggestFiles.suggestCommentUid),
        eq(suggestFiles.isValid, "Y"),
      ),
    )
    .where(
      and(
        category ? eq(suggests.category, category) : undefined,
        betweenYear(suggests.regDate, year),
        eq(suggests.isValid, "Y"),
      ),
    )
    .orderBy(desc(suggests.uid));

  const suggestMap = result.reduce((acc, row) => {
    const suggest = row.suggest;
    if (!acc.has(suggest.uid)) {
      acc.set(suggest.uid, {
        ...suggest,
        files: row.files ? [row.files] : [],
      });
    } else if (row.files) {
      acc.get(suggest.uid)?.files.push(row.files);
    }
    return acc;
  }, new Map<number, Suggest & { files: SuggestFile[] }>());

  return Array.from(suggestMap.values());
};

export const addSuggest = async (
  tenantId: string,
  model: SuggestInsert,
  files: AttachFile[],
) => {
  const tenantDb = await getTenantDb(tenantId);
  return await tenantDb.transaction(async (tx) => {
    const insertId = await tx.insert(suggests).values(model).returning();
    const { uid } = insertId[0];
    for (const file of files) {
      await tx.insert(suggestFiles).values({
        suggestUid: uid,
        originalName: file.originalName,
        uri: file.uri,
      });
    }
    return uid;
  });
};

export const updateSuggest = async (
  tenantId: string,
  uid: number,
  model: SuggestInsert,
  pendingFiles: AttachFile[],
  activeFiles: { uid: number }[],
) => {
  const tenantDb = await getTenantDb(tenantId);
  return await tenantDb.transaction(async (db) => {
    await db
      .update(suggests)
      .set({
        name: model.name,
        category: model.category,
        title: model.title,
        content: model.content,
      })
      .where(eq(suggests.uid, uid));
    await db
      .update(suggestFiles)
      .set({ isValid: "N" })
      .where(
        and(
          eq(suggestFiles.suggestUid, uid),
          isNull(suggestFiles.suggestCommentUid),
          notInArray(
            suggestFiles.uid,
            activeFiles.map((item) => item.uid),
          ),
        ),
      );
    for (const file of pendingFiles) {
      await db.insert(suggestFiles).values({
        suggestUid: uid,
        originalName: file.originalName,
        uri: file.uri,
      });
    }
    return uid;
  });
};

export const deleteSuggest = async (tenantId: string, uid: number) => {
  const tenantDb = await getTenantDb(tenantId);
  return await tenantDb.transaction(async (db) => {
    await db
      .update(suggests)
      .set({ isValid: "N" })
      .where(eq(suggests.uid, uid));
    await db
      .update(suggestFiles)
      .set({ isValid: "N" })
      .where(eq(suggestFiles.suggestUid, uid));
  });
};

export const readSuggest = async (
  tenantId: string,
  uid: number,
  filterValid: boolean = true,
) => {
  const tenantDb = await getTenantDb(tenantId);
  const result = await tenantDb
    .select()
    .from(suggests)
    .where(
      and(
        eq(suggests.uid, uid),
        filterValid ? eq(suggests.isValid, "Y") : undefined,
      ),
    );

  const files = await tenantDb
    .select()
    .from(suggestFiles)
    .where(
      and(
        eq(suggestFiles.suggestUid, uid),
        isNull(suggestFiles.suggestCommentUid),
        filterValid ? eq(suggestFiles.isValid, "Y") : undefined,
      ),
    )
    .orderBy(suggestFiles.uid);
  return result.length > 0 ? { ...result[0], files } : null;
};

export const increaseSuggestHitCount = async (
  tenantId: string,
  uid: number,
) => {
  const tenantDb = await getTenantDb(tenantId);
  return await tenantDb
    .update(suggests)
    .set({ hitCount: increment(suggests.hitCount) })
    .where(eq(suggests.uid, uid));
};

export const readSuggestVote = async (tenantId: string, uid: number) => {
  const tenantDb = await getTenantDb(tenantId);
  const result = await tenantDb
    .select({ up: suggests.upCount, down: suggests.downCount })
    .from(suggests)
    .where(eq(suggests.uid, uid));
  return result.length > 0 ? result[0] : { up: 0, down: 0 };
};

export const readSuggestVoteByUser = async (
  tenantId: string,
  userDid: string,
  suggestUid: number,
) => {
  const tenantDb = await getTenantDb(tenantId);
  const result = await tenantDb
    .select({ vote: suggestVotes.vote })
    .from(suggestVotes)
    .where(
      and(
        eq(suggestVotes.userDid, userDid),
        eq(suggestVotes.suggestUid, suggestUid),
      ),
    );
  return result.length > 0 ? result[0].vote : 0;
};

export const increateSuggestVote = async (
  tenantId: string,
  userDid: string,
  suggestUid: number,
  vote: -1 | 0 | 1,
) => {
  const tenantDb = await getTenantDb(tenantId);
  await tenantDb.transaction(async (db) => {
    const currentVote = await readSuggestVoteByUser(
      tenantId,
      userDid,
      suggestUid,
    );
    if (currentVote === vote) return;

    await db
      .insert(suggestVotes)
      .values({ suggestUid, userDid, vote })
      .onConflictDoUpdate({ target: suggestVotes.vote, set: { vote } });

    if (currentVote !== 0) {
      await db
        .update(suggests)
        .set(
          currentVote === 1
            ? { upCount: increment(suggests.upCount, -1) }
            : { downCount: increment(suggests.downCount, -1) },
        )
        .where(eq(suggests.uid, suggestUid));
    }
    if (vote !== 0) {
      await db
        .update(suggests)
        .set(
          vote === 1
            ? { upCount: increment(suggests.upCount) }
            : { downCount: increment(suggests.downCount) },
        )
        .where(eq(suggests.uid, suggestUid));
    }
  });
};

export const addSuggestComment = async (
  tenantId: string,
  model: SuggestCommentInsert,
  files: AttachFile[],
) => {
  const tenantDb = await getTenantDb(tenantId);
  return await tenantDb.transaction(async (db) => {
    if (model.parentUid) {
      await db
        .update(suggestComments)
        .set({ commentCount: increment(suggestComments.commentCount) })
        .where(eq(suggestComments.uid, model.parentUid));
    } else {
      await db
        .update(suggests)
        .set({ commentCount: increment(suggests.commentCount) })
        .where(eq(suggests.uid, model.suggestUid));
    }
    const insertId = await db.insert(suggestComments).values(model).returning();
    const { uid: commentUid } = insertId[0];
    for (const file of files) {
      await db.insert(suggestFiles).values({
        suggestUid: model.suggestUid,
        suggestCommentUid: commentUid,
        originalName: file.originalName,
        uri: file.uri,
      });
    }
    return commentUid;
  });
};

export const updateSuggestComment = async (
  tenantId: string,
  commentUid: number,
  model: SuggestCommentInsert,
  pendingFiles: AttachFile[],
  activeFiles: { uid: number }[],
) => {
  const tenantDb = await getTenantDb(tenantId);
  return await tenantDb.transaction(async (db) => {
    await db
      .update(suggestComments)
      .set({ name: model.name, content: model.content })
      .where(eq(suggestComments.uid, commentUid));
    await db
      .update(suggestFiles)
      .set({ isValid: "N" })
      .where(
        and(
          eq(suggestFiles.suggestCommentUid, commentUid),
          notInArray(
            suggestFiles.uid,
            activeFiles.map((item) => item.uid),
          ),
        ),
      );
    for (const file of pendingFiles) {
      await db.insert(suggestFiles).values({
        suggestUid: model.suggestUid,
        suggestCommentUid: commentUid,
        originalName: file.originalName,
        uri: file.uri,
      });
    }
    return commentUid;
  });
};

export const deleteSuggestComment = async (
  tenantId: string,
  suggestUid: number,
  parentUid: number | null,
  commentUid: number,
) => {
  const tenantDb = await getTenantDb(tenantId);
  await await tenantDb.transaction(async (db) => {
    await db
      .update(suggestComments)
      .set({ isValid: "N" })
      .where(
        and(
          eq(suggestComments.suggestUid, suggestUid),
          eq(suggestComments.uid, commentUid),
        ),
      );
    await db
      .update(suggestFiles)
      .set({ isValid: "N" })
      .where(
        and(
          eq(suggestFiles.suggestUid, suggestUid),
          eq(suggestFiles.suggestCommentUid, commentUid),
        ),
      );

    if (parentUid) {
      await db
        .update(suggestComments)
        .set({ commentCount: increment(suggestComments.commentCount, -1) })
        .where(
          and(
            eq(suggestComments.suggestUid, suggestUid),
            eq(suggestComments.uid, parentUid),
          ),
        );
    } else {
      await db
        .update(suggests)
        .set({ commentCount: increment(suggests.commentCount, -1) })
        .where(eq(suggests.uid, suggestUid));
    }
  });
};

export const readSuggestComments = async (
  tenantId: string,
  uid: number,
  parentUid: number | null = null,
) => {
  const tenantDb = await getTenantDb(tenantId);
  const result = await tenantDb
    .select({
      comment: suggestComments,
      files: suggestFiles,
    })
    .from(suggestComments)
    .leftJoin(
      suggestFiles,
      and(
        eq(suggestFiles.suggestUid, suggestComments.suggestUid),
        eq(suggestFiles.suggestCommentUid, suggestComments.uid),
        eq(suggestFiles.isValid, "Y"),
      ),
    )
    .where(
      and(
        eq(suggestComments.suggestUid, uid),
        parentUid
          ? eq(suggestComments.parentUid, parentUid)
          : isNull(suggestComments.parentUid),
      ),
    );

  const commentMap = result.reduce(
    (acc, row) => {
      const comment = row.comment;
      if (!acc.has(comment.uid)) {
        acc.set(comment.uid, {
          ...comment,
          files: row.files ? [row.files] : [],
        });
      } else if (row.files) {
        acc.get(comment.uid)?.files.push(row.files);
      }
      return acc;
    },
    new Map<
      number,
      SuggestComment & {
        files: SuggestFile[];
      }
    >(),
  );

  return Array.from(commentMap.values());
};

export const readSuggestCommentById = async (
  tenantId: string,
  commentUid: number,
) => {
  const tenantDb = await getTenantDb(tenantId);
  const result = await tenantDb
    .select()
    .from(suggestComments)
    .where(eq(suggestComments.uid, commentUid));
  return result.length > 0 ? result[0] : null;
};

export const readSuggestCommentVoteIdsByUser = async (
  tenantId: string,
  userDid: string,
  uid: number,
) => {
  const tenantDb = await getTenantDb(tenantId);
  const result = await tenantDb
    .select({ commentUid: suggestCommentVotes.commentUid })
    .from(suggestCommentVotes)
    .where(
      and(
        eq(suggestCommentVotes.userDid, userDid),
        eq(suggestCommentVotes.suggestUid, uid),
        isNotNull(suggestCommentVotes.commentUid),
      ),
    );
  return result.map((row) => row.commentUid);
};

export const increaseSuggestCommentVote = async (
  tenantId: string,
  userDid: string,
  suggestUid: number,
  commentUid: number,
  newVote: 0 | 1,
) => {
  const tenantDb = await getTenantDb(tenantId);
  await tenantDb.transaction(async (db) => {
    const voteResult = await db
      .select({ up: suggestCommentVotes.vote })
      .from(suggestCommentVotes)
      .where(
        and(
          eq(suggestCommentVotes.userDid, userDid),
          eq(suggestCommentVotes.suggestUid, suggestUid),
          eq(suggestCommentVotes.commentUid, commentUid),
        ),
      );
    const prevVote = voteResult.length > 0 ? voteResult[0].up : 0;
    if (prevVote === newVote) return;
    await db
      .insert(suggestCommentVotes)
      .values({
        userDid,
        suggestUid,
        commentUid,
        vote: newVote,
      })
      .onConflictDoUpdate({
        target: suggestCommentVotes.uid,
        set: { vote: newVote },
      });
    if (prevVote !== 0) {
      await db
        .update(suggestComments)
        .set({ upCount: increment(suggestComments.upCount, -1) })
        .where(eq(suggestComments.uid, commentUid));
    }
    if (newVote !== 0) {
      await db
        .update(suggestComments)
        .set({ upCount: increment(suggestComments.upCount) })
        .where(eq(suggestComments.uid, commentUid));
    }
  });
};
