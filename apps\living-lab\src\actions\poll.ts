"use server";

import { addPollVote } from "@workspace/db/crud/living-lab/poll";
import { getSession } from "@workspace/utils/auth/session";
import { actionError, ErrorCode } from "@workspace/utils/consts/errors";

export const postPollVote = async (
  pollUid: number,
  items: number[],
  _signature: string,
) => {
  const session = await getSession();
  if (!session) return actionError(ErrorCode.UNAUTHORIZED);

  const { tenantCd, userDid } = session;
  if (Number.isNaN(pollUid)) {
    return actionError(ErrorCode.INVALID_ID);
  }

  const result = await addPollVote(tenantCd, userDid, pollUid, items);
  console.log(result);
  return result;
};
