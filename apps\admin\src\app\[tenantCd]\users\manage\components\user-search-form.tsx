"use client";

import {
  applyDate<PERSON><PERSON>eP<PERSON><PERSON>,
  DateRangePicker,
} from "@/components/date-range-picker";
import { UserSearchFilter } from "@workspace/db/crud/common/user";
import { Button } from "@workspace/ui/components/button";
import { Card } from "@workspace/ui/components/card";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
} from "@workspace/ui/components/form";
import { Input } from "@workspace/ui/components/input";
import { Label } from "@workspace/ui/components/label";
import {
  RadioGroup,
  RadioGroupItem,
} from "@workspace/ui/components/radio-group";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@workspace/ui/components/select";
import { format } from "date-fns";
import { useRouter } from "next/navigation";
import { useForm } from "react-hook-form";
import { z } from "zod";

const formSchema = z.object({
  searchType: z.string(),
  keyword: z.string().optional(),
  gender: z.string(),
  vc: z.string(),
  vcDt: z.tuple([z.date().optional(), z.date().optional()]).optional(),
  joinDt: z.tuple([z.date().optional(), z.date().optional()]).optional(),
});

export const UserSearchForm = ({
  initialParams,
  isMainTenant = false,
}: {
  initialParams?: UserSearchFilter;
  isMainTenant?: boolean;
}) => {
  const router = useRouter();
  const form = useForm<z.infer<typeof formSchema>>({
    values: {
      searchType: initialParams?.searchType ?? "all",
      keyword: initialParams?.keyword ?? "",
      gender: initialParams?.gender ?? "-",
      vc: initialParams?.vc ?? "-",
      vcDt: initialParams?.vcDt,
      joinDt: initialParams?.joinDt,
    },
  });

  const handleSubmit = form.handleSubmit((data) => {
    const searchParams = new URLSearchParams();
    if (data.keyword) {
      searchParams.set("searchType", data.searchType);
      searchParams.set("keyword", data.keyword);
    }
    if (data.gender !== "-") {
      searchParams.set("gender", data.gender);
    }
    if (data.vc !== "-") {
      searchParams.set("vc", data.vc);
    }
    applyDateRangeParams(searchParams, "vcDt", data.vcDt);
    applyDateRangeParams(searchParams, "joinDt", data.joinDt);
    router.push(`?${searchParams.toString()}`);
  });

  return (
    <Card className="p-4">
      <Form {...form}>
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* 첫 번째 행: 검색어, 성별 */}
          <div className="flex flex-wrap gap-4">
            <div className="flex min-w-[300px] flex-1 items-center gap-2">
              <Label className="w-20 text-sm font-medium">검색어</Label>
              <FormField
                control={form.control}
                name="searchType"
                render={({ field }) => (
                  <FormItem>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger className="w-24">
                          <SelectValue />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="all">전체</SelectItem>
                        <SelectItem value="userNm">이름</SelectItem>
                        <SelectItem value="phone">전화번호</SelectItem>
                        <SelectItem value="birth">생년월일</SelectItem>
                      </SelectContent>
                    </Select>
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="keyword"
                render={({ field }) => (
                  <FormItem className="flex-1">
                    <FormControl>
                      <Input
                        {...field}
                        type="text"
                        placeholder="검색어를 입력하세요"
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
            </div>

            <div className="flex min-w-[300px] flex-1 items-center gap-2">
              <Label className="w-20 text-sm font-medium">성별</Label>
              <FormField
                control={form.control}
                name="gender"
                render={({ field }) => (
                  <FormItem>
                    <FormControl>
                      <RadioGroup
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                        className="flex gap-4"
                      >
                        <div className="flex items-center space-x-1">
                          <RadioGroupItem value="-" id="gender-all" />
                          <Label htmlFor="gender-all" className="text-sm">
                            전체
                          </Label>
                        </div>
                        <div className="flex items-center space-x-1">
                          <RadioGroupItem value="M" id="gender-male" />
                          <Label htmlFor="gender-male" className="text-sm">
                            남성
                          </Label>
                        </div>
                        <div className="flex items-center space-x-1">
                          <RadioGroupItem value="F" id="gender-female" />
                          <Label htmlFor="gender-female" className="text-sm">
                            여성
                          </Label>
                        </div>
                      </RadioGroup>
                    </FormControl>
                  </FormItem>
                )}
              />
            </div>
          </div>
          {!isMainTenant && (
            <div className="flex flex-wrap gap-4">
              <div className="flex min-w-[300px] flex-1 items-center gap-2">
                <Label className="w-20 text-sm font-medium">시민인증일시</Label>
                <div className="flex items-center gap-2">
                  <FormField
                    control={form.control}
                    name="vcDt"
                    render={({ field }) => (
                      <DateRangePicker
                        dateRange={field.value}
                        onChange={field.onChange}
                      />
                    )}
                  />
                </div>
              </div>
              <div className="flex min-w-[300px] flex-1 items-center gap-2">
                <Label className="w-20 text-sm font-medium">시민인증</Label>
                <FormField
                  control={form.control}
                  name="vc"
                  render={({ field }) => (
                    <FormItem>
                      <FormControl>
                        <RadioGroup
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                          className="flex gap-4"
                        >
                          <div className="flex items-center space-x-1">
                            <RadioGroupItem value="-" id="vc-all" />
                            <Label htmlFor="vc-all" className="text-sm">
                              전체
                            </Label>
                          </div>
                          <div className="flex items-center space-x-1">
                            <RadioGroupItem value="Y" id="vc-verified" />
                            <Label htmlFor="vc-verified" className="text-sm">
                              인증
                            </Label>
                          </div>
                          <div className="flex items-center space-x-1">
                            <RadioGroupItem value="N" id="vc-unverified" />
                            <Label htmlFor="vc-unverified" className="text-sm">
                              미인증
                            </Label>
                          </div>
                        </RadioGroup>
                      </FormControl>
                    </FormItem>
                  )}
                />
              </div>
            </div>
          )}

          <div className="flex flex-wrap gap-4">
            <div className="flex min-w-[300px] flex-1 items-center gap-2">
              <Label className="w-20 text-sm font-medium">가입일시</Label>
              <div className="flex items-center gap-2">
                <FormField
                  control={form.control}
                  name="joinDt"
                  render={({ field }) => (
                    <DateRangePicker
                      dateRange={field.value}
                      onChange={field.onChange}
                    />
                  )}
                />
              </div>
            </div>
          </div>

          <div className="flex flex-wrap items-center gap-4">
            <div className="ml-auto flex gap-4">
              <Button type="submit" className="px-6 py-2">
                검색
              </Button>
            </div>
          </div>
        </form>
      </Form>
    </Card>
  );
};
