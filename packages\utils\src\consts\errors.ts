export type ActionError<E extends object = Record<string, unknown>> = {
  status: "error";
  error: ErrorCode;
} & E;

export const isActionError = (error: unknown): error is ActionError => {
  if (error === undefined) return false;
  return (error as ActionError).error !== undefined;
};

export const actionError = (errorCode: ErrorCode): ActionError => {
  return { status: "error", error: errorCode };
};

export class LLResult {
  result: unknown;
}

export enum ErrorCode {
  UNAUTHORIZED = "UNAUTHORIZED",
  FORBIDDEN = "FORBIDDEN",
  INVALID_ID = "INVALID_ID",
  POLL_CLOSED = "POLL_CLOSED",
  POLL_VOTED_ALREADY = "POLL_VOTED_ALREADY",
  SURVEY_CLOSED = "SURVEY_CLOSED",
  SURVEY_ANSWERED_ALREADY = "SURVEY_ANSWERED_ALREADY",
}

export const getErrorMessage = (e: ActionError) => {
  switch (e.error) {
    case ErrorCode.UNAUTHORIZED:
      return "권한이 없습니다.";
    case ErrorCode.FORBIDDEN:
      return "접근 권한이 없습니다.";
    case ErrorCode.INVALID_ID:
      return "잘못된 ID입니다.";
    case ErrorCode.POLL_CLOSED:
      return "투표가 종료되었습니다.";
    case ErrorCode.POLL_VOTED_ALREADY:
      return "이미 투표하셨습니다.";
    case ErrorCode.SURVEY_CLOSED:
      return "설문이 종료되었습니다.";
    case ErrorCode.SURVEY_ANSWERED_ALREADY:
      return "이미 설문에 응답하셨습니다.";
    default:
      return "알 수 없는 오류가 발생했습니다.";
  }
};
