import axios, { isAxiosError } from "axios";
import { toCamel<PERSON>ase<PERSON>eys, toSnakeCaseKeys } from "es-toolkit";
import {
  PetPassAPMSRequest,
  PetPassAPMSResponse,
  PetPassBreedsResponse,
  PetPassDeletePetResponse,
  PetPassErrorResponse,
  PetPassHairColorsResponse,
  PetPassOwnerResponse,
  PetPassPetsResponse,
  PetPassEntryRequest as PetPassQrEntryRequest,
  PetPassEntryResponse as PetPassQrEntryResponse,
  PetPassRegistOwnerRequest,
  PetPassRegistPetRequest,
  PetPassRegistPetResponse,
} from "./pet-types";

const PET_PASS_URL =
  process.env.PET_PASS_URL ?? "https://dev-api.petpass.kr/v1";
const PET_PASS_API_TOKEN = "3ZBMwkW21bU6CfaHkQhSJj4Z8+tsn4XQfhAqeuFjrNw=";

const petPassApi = axios.create({
  baseURL: PET_PASS_URL,
  headers: {
    Authorization: `Bearer ${PET_PASS_API_TOKEN}`,
  },
});

petPassApi.interceptors.response.use(
  async (response) => {
    try {
      if (response.status >= 200 && response.status < 300) {
        response.data = toCamelCaseKeys(response.data);
      }
      return response;
    } catch (e) {
      console.error("Error parsing response:", e);
      throw e;
    }
  },
  (error) => {
    if (isAxiosError(error) && error.response) {
      if (error.response.data) {
        error.response.data = toCamelCaseKeys(error.response.data);
        return Promise.resolve(error.response);
      }
    }
    return Promise.reject(error);
  },
);

export const getOwnerByUserDid = async ({ userDid }: { userDid: string }) => {
  const response = await petPassApi.get(`owners/${userDid}`);
  return response.data as PetPassOwnerResponse | PetPassErrorResponse;
};

export const addOwner = async (model: PetPassRegistOwnerRequest) => {
  const response = await petPassApi.post("owners", toSnakeCaseKeys(model));
  return response.data as PetPassOwnerResponse | PetPassErrorResponse;
};

// 반려동물정보목록
export const getPets = async (userDid: string) => {
  const response = await petPassApi.get(`pets/${userDid}`);
  return response.data as PetPassPetsResponse | PetPassErrorResponse;
};

// 반려동물정보등록
export const addPet = async (
  userDid: string,
  model: PetPassRegistPetRequest,
) => {
  const response = await petPassApi.post(
    `pets/${userDid}`,
    toSnakeCaseKeys(model),
  );
  return response.data as PetPassRegistPetResponse | PetPassErrorResponse;
};

// 반려동물정보삭제
export const deletePet = async (userDid: string, qrpIdx: number) => {
  const response = await petPassApi.delete(`pets/${userDid}/${qrpIdx}`);
  return response.data as PetPassDeletePetResponse | PetPassErrorResponse;
};

// 출입정보입력 QR발급
export const getQrEntry = async (
  userDid: string,
  model: PetPassQrEntryRequest,
) => {
  const response = await petPassApi.post(
    `qr/entry/${userDid}`,
    toSnakeCaseKeys(model),
  );
  return response.data as PetPassQrEntryResponse | PetPassErrorResponse;
};

// 동물등록정보조회
export const getAPMS = async (model: PetPassAPMSRequest) => {
  const response = await petPassApi.get("country-animal", {
    params: toSnakeCaseKeys(model),
  });
  return response.data as PetPassAPMSResponse | PetPassErrorResponse;
};

// 품종목록
export const getBreeds = async () => {
  const response = await petPassApi.get("breeds");
  return response.data as PetPassBreedsResponse | PetPassErrorResponse;
};

// 털색상목록
export const getHairColors = async (breedCode: string) => {
  const response = await petPassApi.get(`hair_colors/${breedCode}`);
  return response.data as PetPassHairColorsResponse | PetPassErrorResponse;
};
