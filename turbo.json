{"$schema": "https://turbo.build/schema.json", "ui": "tui", "tasks": {"build": {"dependsOn": ["^build"], "env": ["ATTACHMENT_PATH", "DATABASE_URL", "NODE_ENV", "PET_PASS_URL", "PLATFORM_SAAS_URL"], "inputs": ["$TURBO_DEFAULT$", ".env*"], "outputs": [".next/**", "!.next/cache/**"]}, "lint": {"dependsOn": ["^lint"]}, "dev": {"dependsOn": ["^db:generate"], "cache": false, "persistent": true}, "db:generate": {"cache": false}, "db:push": {"cache": false, "env": ["DATABASE_URL"]}, "test": {}, "test:watch": {"cache": false, "persistent": true}}}