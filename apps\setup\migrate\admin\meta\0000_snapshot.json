{"id": "e36af819-513b-4073-94ea-814094a9ae75", "prevId": "00000000-0000-0000-0000-000000000000", "version": "7", "dialect": "postgresql", "tables": {"public.tb_admin_info": {"name": "tb_admin_info", "schema": "", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "identity": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "tb_admin_info_id_seq", "schema": "public", "increment": "1", "startWith": "1", "minValue": "1", "maxValue": "2147483647", "cache": "1", "cycle": false}}, "user_id": {"name": "user_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "user_pwd": {"name": "user_pwd", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "user_nm": {"name": "user_nm", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "user_email": {"name": "user_email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "user_phone": {"name": "user_phone", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true}, "user_did": {"name": "user_did", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "user_depr": {"name": "user_depr", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "val_yn": {"name": "val_yn", "type": "<PERSON><PERSON><PERSON>(1)", "primaryKey": false, "notNull": true, "default": "'N'"}, "last_login_dt": {"name": "last_login_dt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "reg_dt": {"name": "reg_dt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "upd_dt": {"name": "upd_dt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"tb_admin_info_user_id_unique": {"name": "tb_admin_info_user_id_unique", "nullsNotDistinct": false, "columns": ["user_id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.tb_admin_logs": {"name": "tb_admin_logs", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "tenant_cd": {"name": "tenant_cd", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true}, "admin_uid": {"name": "admin_uid", "type": "integer", "primaryKey": false, "notNull": true}, "action": {"name": "action", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "metadata": {"name": "metadata", "type": "jsonb", "primaryKey": false, "notNull": false}, "ip_address": {"name": "ip_address", "type": "<PERSON><PERSON><PERSON>(45)", "primaryKey": false, "notNull": false}, "user_agent": {"name": "user_agent", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "reg_dt": {"name": "reg_dt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "upd_dt": {"name": "upd_dt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"idx_admin_logs_admin": {"name": "idx_admin_logs_admin", "columns": [{"expression": "admin_uid", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_admin_logs_action": {"name": "idx_admin_logs_action", "columns": [{"expression": "action", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_admin_logs_reg_dt": {"name": "idx_admin_logs_reg_dt", "columns": [{"expression": "reg_dt", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_admin_logs_upd_dt": {"name": "idx_admin_logs_upd_dt", "columns": [{"expression": "upd_dt", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"tb_admin_logs_admin_uid_tb_admin_info_id_fk": {"name": "tb_admin_logs_admin_uid_tb_admin_info_id_fk", "tableFrom": "tb_admin_logs", "tableTo": "tb_admin_info", "columnsFrom": ["admin_uid"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.tb_admin_role": {"name": "tb_admin_role", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "admin_uid": {"name": "admin_uid", "type": "integer", "primaryKey": false, "notNull": true}, "tenant_cd": {"name": "tenant_cd", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false}, "user_role": {"name": "user_role", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true, "default": "'UNAUTHORIZED'"}, "val_yn": {"name": "val_yn", "type": "<PERSON><PERSON><PERSON>(1)", "primaryKey": false, "notNull": true, "default": "'Y'"}, "valid_from": {"name": "valid_from", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "valid_to": {"name": "valid_to", "type": "timestamp", "primaryKey": false, "notNull": false}, "reg_dt": {"name": "reg_dt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "upd_dt": {"name": "upd_dt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"idx_admin_role_tenant": {"name": "idx_admin_role_tenant", "columns": [{"expression": "tenant_cd", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "admin_uid", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"tb_admin_role_admin_uid_tb_admin_info_id_fk": {"name": "tb_admin_role_admin_uid_tb_admin_info_id_fk", "tableFrom": "tb_admin_role", "tableTo": "tb_admin_info", "columnsFrom": ["admin_uid"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}