import type { Metadata, Viewport } from "next";
import "./styles.css";
import "@workspace/ui/globals.css";
import "@workspace/ui/globals-mobile.css";
import QueryClientProvider from "@/services/providers/query-client-provider";
import { ModalProvider } from "@workspace/ui/providers/modal-provider";
import { GlobalBridgeProvider } from "@workspace/webview-state";

export const metadata: Metadata = {
  title: "",
  description: "",
};

export const viewport: Viewport = {
  themeColor: "#ffffff",
  width: "device-width",
  initialScale: 1,
  maximumScale: 1,
  userScalable: false,
  viewportFit: "cover",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="ko-KR">
      <body className="antialiased">
        <GlobalBridgeProvider appScheme="cw.mobile">
          <ModalProvider>
            <QueryClientProvider>{children}</QueryClientProvider>
          </ModalProvider>
        </GlobalBridgeProvider>
      </body>
    </html>
  );
}
