"use client";

import { categoryList, CategoryType } from "@/types/category";
import { Card, CardContent } from "@workspace/ui/components/card";
import { cn } from "@workspace/ui/lib/utils";
import Image from "next/image";
import { useRouter } from "next/navigation";
import React, { useCallback, useState } from "react";

export const CategoryList = () => {
  const router = useRouter();
  const [pressedIndex, setPressedIndex] = useState(-1);

  const showProductList = useCallback(
    (item: CategoryType) => {
      router.push(`/products/${item.searchTag ?? item.displayText}`);
    },
    [router],
  );

  return (
    <div className="flex-start grid grid-cols-3 gap-2 p-0">
      {categoryList.map((item, index) => (
        <div key={index}>
          <Card
            className={cn(
              "h-full cursor-pointer transition-transform active:scale-[0.97]",
              pressedIndex === index ? "shadow-xs" : "",
            )}
            onClick={() => showProductList(item)}
            onMouseDown={() => setPressedIndex(index)}
            onMouseUp={() => setPressedIndex(-1)}
            onMouseLeave={() => setPressedIndex(-1)}
          >
            <CardContent className="flex flex-col items-center justify-center p-4 text-center">
              <Image
                src={index === pressedIndex ? item.imageSelected : item.image}
                width={40}
                height={40}
                alt={item.displayText}
              />
              <span
                className={cn(
                  "mt-2 text-sm",
                  index === pressedIndex
                    ? "font-bold text-[#256bff]"
                    : "font-normal text-[#5c5c5c]",
                )}
              >
                {item.displayText}
              </span>
            </CardContent>
          </Card>
        </div>
      ))}
    </div>
  );
};
