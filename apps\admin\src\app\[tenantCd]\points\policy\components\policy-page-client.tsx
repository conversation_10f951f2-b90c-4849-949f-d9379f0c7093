"use client";

import { PointPolicyItem } from "@workspace/db/crud/common/points";
import { Button } from "@workspace/ui/components/button";
import { useState } from "react";
import { PolicyFormDialog } from "./policy-form";
import { PolicyList } from "./policy-list";

interface PolicyPageClientProps {
  data: PointPolicyItem[];
  count: number;
  pageNumber: number;
  pageSize: number;
}

export const PolicyPageClient = ({
  data,
  count,
  pageNumber,
  pageSize,
}: PolicyPageClientProps) => {
  const [dialogOpen, setDialogOpen] = useState(false);
  const [dialogMode, setDialogMode] = useState<"create" | "edit">("create");
  const [selectedPolicy, setSelectedPolicy] = useState<PointPolicyItem | null>(
    null,
  );

  const handleCreateClick = () => {
    setDialogMode("create");
    setSelectedPolicy(null);
    setDialogOpen(true);
  };

  const handleEditClick = (policy: PointPolicyItem) => {
    setDialogMode("edit");
    setSelectedPolicy(policy);
    setDialogOpen(true);
  };

  return (
    <>
      <div className="mb-4 flex justify-end">
        <Button onClick={handleCreateClick}>등록</Button>
      </div>

      <PolicyList
        data={data}
        count={count}
        pageNumber={pageNumber}
        pageSize={pageSize}
        onEditClick={handleEditClick}
      />

      <PolicyFormDialog
        open={dialogOpen}
        onOpenChange={setDialogOpen}
        policy={selectedPolicy}
        mode={dialogMode}
      />
    </>
  );
};
