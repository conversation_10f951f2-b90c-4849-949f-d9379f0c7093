import { NavigateParams, NavigateRequest } from "./types";

export type BoardType = "notice" | "faq";

export const Routes = {
  boardDetail: (params?: NavigateParams) => {
    const { board, id } = params as { board: BoardType; id: number };
    return {
      url: `cw.mobile://board/${board}/${id}`,
    };
  },
  citizenId: (params?: NavigateParams) => ({
    url: "cw.app://citizen-id",
    params,
  }),
  idCard: (params?: NavigateParams) => ({
    url: "cw.mobile://id-card",
    params,
  }),
  pedometer: () => ({ url: "cw.app://pedometer" }),
  pedometerDetail: () => ({ url: "cw.mobile://pedometer/detail" }),
  livingLab: () => ({
    url: "cw.living-lab://home",
    options: { hideHeader: true, disableSwipeBack: true },
  }),
  qrScan: () => ({ url: "cw.app://qr-scan" }),
  store: () => ({ url: "cw.store://" }),

  walletPointExport: () => ({ url: "cw.mobile://wallet/point-export" }),
  walletPointExportKonai: () => ({
    url: "cw.mobile://wallet/point-export/konai",
  }),
  walletPointHistory: (params?: NavigateParams) => ({
    url: "cw.mobile://wallet/point-history",
    params,
  }),

  // gyept
  gyeptG1: () => ({
    title: "에너지음",
    url: "https://ptscm.kevinlab.com?entry=0",
    options: {
      hideHeader: false,
      disableSwipeBack: true,
      delegateBackButton: true,
      requiredNonce: true,
      webViewId: "gyept-g1",
    },
  }),
  gyeptG4: () => ({
    title: "페트랑",
    url: "https://g4.petlang.net/",
    options: {
      hideHeader: false,
      disableSwipeBack: false,
      requiredNonce: true,
      webViewId: "gyept-g4",
    },
  }),
  gyeptG7: () => ({
    title: "이노베이션센터",
    url: "https://cw-dev.data-alliance.com/misc/gyept/cw-web-g7/",
  }),
} as const satisfies Record<
  string,
  (params?: NavigateParams) => NavigateRequest
>;
