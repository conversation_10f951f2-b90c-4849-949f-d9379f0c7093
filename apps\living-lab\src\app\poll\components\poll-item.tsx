import { PollSurveyItem } from "@/components/poll-survey/poll-survey-item";
import { Poll } from "@workspace/db/crud/living-lab/poll";
import { POLL_STATE } from "@workspace/db/schema/living-lab/poll";
import dayjs from "dayjs";
import { getPollCategoryName, getPollStateName, isPollClosed } from "../consts";

export const PollItem = ({ item }: { item: Poll }) => {
  const period = [dayjs(item.startDate), dayjs(item.endDate)];
  const variant =
    item.state === POLL_STATE.CLOSED
      ? "closed"
      : isPollClosed(item.state, period)
        ? "expired"
        : "default";
  const stateText = getPollStateName(item.state, period);

  return (
    <PollSurveyItem
      href={`/poll/${item.uid}`}
      category={getPollCategoryName(item.category)}
      title={item.title}
      period={period}
      state={stateText}
      variant={variant}
    />
  );
};
