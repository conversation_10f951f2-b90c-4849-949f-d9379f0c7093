"use client";

import { parseTenantPath } from "@/lib/tenant";
import { Button } from "@workspace/ui/components/button";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@workspace/ui/components/collapsible";
import { cn } from "@workspace/ui/lib/utils";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { ReactNode } from "react";

export type SideMenuItem = {
  title: string;
  icon?: string;
} & (
  | {
      link: string;
      children?: never;
    }
  | {
      link?: never;
      children: SideMenuItem[];
    }
);

const checkIsActive = (
  pathname: string,
  menuItem: SideMenuItem,
  isParent: boolean,
): boolean => {
  if (menuItem.link) return pathname.startsWith(menuItem.link);
  return (
    isParent &&
    (menuItem.children?.some((child) =>
      checkIsActive(pathname, child, isParent),
    ) ??
      false)
  );
};

const SidebarMenu = ({
  children,
  className,
}: {
  children: ReactNode;
  className?: string;
}) => {
  return (
    <ul
      className={cn(
        "flex w-full min-w-0 flex-col gap-[10px] pl-[20px]",
        className,
      )}
    >
      {children}
    </ul>
  );
};

const SidebarMenuItem = ({ item }: { item: SideMenuItem }) => {
  const pathname = usePathname();
  const [tenant, rest] = parseTenantPath(pathname);
  const isActive = checkIsActive(rest, item, true);

  if (item.link) {
    return (
      <li className="group/menu-item relative">
        <Button
          asChild
          variant="ghost"
          className={cn(
            "w-full justify-start",
            isActive ? "font-bold" : "text-muted-foreground",
          )}
        >
          {item.link.startsWith("#") ? (
            <div className="text-gray-400">
              {item.icon && (
                <img src={`/assets/${item.icon}`} alt={item.title} />
              )}
              {item.title}
            </div>
          ) : (
            <Link href={`/${tenant}${item.link}`}>
              {item.icon && (
                <img src={`/assets/${item.icon}`} alt={item.title} />
              )}
              {item.title}
            </Link>
          )}
        </Button>
      </li>
    );
  }

  if (!item.children) throw new Error("Invalid menu item");

  return (
    <Collapsible
      asChild
      defaultOpen={checkIsActive(rest, item, true)}
      className="group/collapsible"
    >
      <li className="group/menu-item relative">
        <CollapsibleTrigger asChild>
          <Button
            variant="ghost"
            className={cn(
              "w-full justify-start",
              isActive ? "font-bold" : "text-muted-foreground",
            )}
          >
            {item.icon && <img src={`/assets/${item.icon}`} alt={item.title} />}
            {item.title}
          </Button>
        </CollapsibleTrigger>
        <CollapsibleContent className="pb-[10px] pt-[10px]">
          <SidebarMenu className="pl-[20px]">
            {item.children.map((child, index) => (
              <SidebarMenuItem key={index} item={child} />
            ))}
          </SidebarMenu>
        </CollapsibleContent>
      </li>
    </Collapsible>
  );
};

export const AppSidebar = ({ items }: { items: SideMenuItem[] }) => {
  return (
    <div className="h-[calc(100vh-80px)] w-[260px] border-r-[1px]">
      <div className="relative h-full overflow-y-auto py-[20px] pr-[20px]">
        <SidebarMenu>
          {items.map((item, index) => (
            <SidebarMenuItem key={index} item={item} />
          ))}
        </SidebarMenu>
      </div>
    </div>
  );
};
