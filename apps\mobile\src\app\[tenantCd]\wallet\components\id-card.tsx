"use client";

import { <PERSON><PERSON><PERSON>one, PrimaryButton } from "@/components/button";
import assets, { ImageAsset } from "@/services/assets";
import { cn } from "@workspace/ui/lib/utils";
import { Routes, useNavigate } from "@workspace/webview-state";
import { NavigateRequest } from "@workspace/webview-state/types";
import Image from "next/image";
import { UserName } from "./common";

const menuItems: { icon: string; label: string; href: NavigateRequest }[] = [
  {
    icon: "/assets/wallet/id-card-citizen.svg",
    label: "모바일시민증",
    href: Routes.idCard(),
  },
  {
    icon: "/assets/wallet/id-card-pet.svg",
    label: "반려동물신분증",
    href: Routes.idCard({ category: "pet" }),
  },
];

export const IdCardSelector = ({ className }: { className?: string }) => {
  const { navigate } = useNavigate();

  return (
    <div
      className={cn("rounded-[10px] py-[12px] shadow-lg", className)}
      style={{ background: "var(--bg-id-menu)" }}
    >
      <div className="flex items-center px-[16px]">
        <Image
          src="/assets/wallet/id-card-title.png"
          width={26}
          height={26}
          alt=""
          className="mr-[8px]"
        />
        <span className="text-primary-local text-[16px] font-semibold">
          모바일 증명서
        </span>
      </div>
      <div className="flex gap-[8px] px-[12px] pt-[12px]">
        {menuItems.map((item, index) => (
          <div key={index} className="flex-1">
            <PrimaryButton
              className="bg-primary-local hover:bg-primary-local/90"
              onClick={() => navigate(item.href)}
            >
              <Image
                src={item.icon}
                width={24}
                height={24}
                alt=""
                className="mr-[6px]"
              />
              {item.label}
            </PrimaryButton>
          </div>
        ))}
      </div>
    </div>
  );
};

export const IdCard = ({
  tenantCd,
  userNm,
  className,
}: {
  tenantCd: string;
  userNm: string;
  className?: string;
}) => {
  const { navigate } = useNavigate();
  const { logoImage } = assets(tenantCd).wallet.idCard;
  const handleClick = () => {
    navigate(Routes.idCard());
  };
  return (
    <ButtonNone asChild onClick={handleClick}>
      <div
        className={cn(
          "-z-10 h-[110px] w-full rounded-t-[16px] shadow-lg",
          "bg-gradient-to-r from-[#2197D7] to-[#76CFFF]",
          className,
        )}
      >
        <div
          className={cn("flex h-full w-full items-start")}
          style={{
            backgroundImage: "url('/assets/wallet/id-card-mask.png')",
            backgroundSize: "cover",
            backgroundPosition: "right top",
          }}
        >
          <div
            className={cn(
              "flex w-full flex-row items-center justify-center p-[20px]",
            )}
          >
            {logoImage ? <ImageAsset src={logoImage} /> : null}
            <UserName userNm={userNm} />
          </div>
        </div>
      </div>
    </ButtonNone>
  );
};
