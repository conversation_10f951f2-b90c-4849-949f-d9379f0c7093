# syntax=docker.io/docker/dockerfile:1
ARG NODE_VERSION=20.11.0
FROM node:${NODE_VERSION}-slim AS slim

FROM slim AS base
RUN corepack enable && corepack prepare pnpm@9.12.3 --activate
RUN npm add --global turbo
RUN pnpm config set store-dir ~/.pnpm-store

FROM base AS pruner
ARG PROJECT
ARG ENV_FILE

WORKDIR /app
COPY . .
RUN find . -name ".env.*" -exec rm {} \;
COPY apps/${PROJECT}/${ENV_FILE} apps/${PROJECT}

RUN if [ -n "${ENV_FILE}" ]; then \
	mv apps/${PROJECT}/${ENV_FILE} apps/${PROJECT}/.env.production; \
	fi

RUN pnpm turbo prune --scope=${PROJECT} --docker

FROM base AS builder
ARG PROJECT

WORKDIR /app

# COPY --from=pruner /app/out/pnpm-lock.yaml ./pnpm-lock.yaml
# COPY --from=pruner /app/out/pnpm-workspace.yaml ./pnpm-workspace.yaml
COPY --from=pruner /app/out/json/ .

RUN --mount=type=cache,id=pnpm,target=~/.pnpm-store pnpm install --frozen-lockfile

COPY --from=pruner /app/out/full/ .

RUN pnpm turbo build --filter=${PROJECT}
# || (echo "Build failed, opening shell for debugging" && /bin/sh)

RUN --mount=type=cache,id=pnpm,target=~/.pnpm-store pnpm prune --prod --no-optional
RUN rm -rf ./**/*/src

FROM slim AS runner
ARG PROJECT

RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nodejs

WORKDIR /app

COPY --from=builder /app/apps/${PROJECT}/next.config.ts .
COPY --from=builder /app/apps/${PROJECT}/package.json .

# Automatically leverage output traces to reduce image size
COPY --from=builder --chown=nodejs:nodejs /app/apps/${PROJECT}/.next/standalone ./
COPY --from=builder --chown=nodejs:nodejs /app/apps/${PROJECT}/.next/static ./apps/${PROJECT}/.next/static
COPY --from=builder --chown=nodejs:nodejs /app/apps/${PROJECT}/public ./apps/${PROJECT}/public
RUN mkdir -p /tmp/next/cache \
  && chown nodejs:nodejs /tmp/next/cache \
  && ln -s /tmp/next/cache /app/apps/${PROJECT}/.next/cache

USER nodejs
WORKDIR /app/apps/${PROJECT}

ARG PORT=3000
ENV PORT=${PORT}
ARG NODE_ENV
ENV NODE_ENV=${NODE_ENV}
ENV NEXT_TELEMETRY_DISABLED=1

EXPOSE ${PORT}

CMD ["node", "server.js"]