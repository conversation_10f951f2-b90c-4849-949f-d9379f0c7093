import "server-only";
import {
  and,
  asc,
  count,
  countDistinct,
  desc,
  eq,
  getMainDb,
  inArray,
  like,
  lt,
  SQL,
  sum,
} from "@workspace/db/drizzle";
import {
  products,
  productTags,
  tags,
} from "@workspace/db/schema/store/product";
import { orderDetails, orders } from "../../schema/store/order";

export const ProductStatus = {
  FOR_SALE: 1,
  END_OF_SALE: 10,
} as const;

export const OrderStatus = {
  PAYMENT_PENDING: 1,
  PAYMENT_APPROVED: 2,
  DELIVERY_STARTED: 10,
  DELIVERY_FAILED: 11,
  DELIVERY_FINISHED: 12,
  COMPLETED: 20,
  CANCELED: 30,
} as const;

export type OrderStatus = (typeof OrderStatus)[keyof typeof OrderStatus];

type ProductUpsert = {
  sourceId: string;
  name: string;
  brand?: string;
  price: number;
  imageUrl: string;
  status: number;
  tags?: string[];
};

type ProductsFilter = {
  searchTag?: string[];
  keyword?: string;
};

export const upsertProducts = async (
  sourceType: string,
  productList: ProductUpsert[],
) => {
  const db = await getMainDb();
  await db.transaction(async (tx) => {
    await tx
      .update(products)
      .set({ status: ProductStatus.END_OF_SALE })
      .where(eq(products.sourceType, sourceType));

    const allTags = new Set(productList.flatMap((item) => item.tags || []));
    for (const tag of allTags) {
      await tx
        .insert(tags)
        .values({ name: tag })
        .onConflictDoUpdate({ target: tags.name, set: { name: tag } });
    }
    const tagMap: Record<string, number> = {};
    const tagResults = await tx.select().from(tags);
    for (const tag of tagResults) {
      tagMap[tag.name] = tag.id;
    }

    for (const item of productList) {
      const condition = and(
        eq(products.sourceType, sourceType),
        eq(products.sourceId, item.sourceId),
      );

      let productId: number;
      const result = await tx.select().from(products).where(condition);
      if (result.length > 0) {
        productId = result[0].id;
        await tx
          .update(products)
          .set({ ...item })
          .where(condition);
      } else {
        const result = await tx
          .insert(products)
          .values({ sourceType, ...item })
          .returning({ id: products.id });
        productId = result[0].id;
      }

      if (item.tags && item.tags.length > 0) {
        await tx.insert(productTags).values(
          item.tags.map((tag) => ({
            productId,
            tagId: tagMap[tag],
          })),
        );
      }
    }
  });
};

export const readProduct = async (id: number) => {
  const db = await getMainDb();
  const result = await db
    .select({
      id: products.id,
      name: products.name,
      price: products.price,
      brand: products.brand,
      imageUrl: products.imageUrl,
      sourceId: products.sourceId,
      sourceType: products.sourceType,
    })
    .from(products)
    .where(
      and(eq(products.id, id), eq(products.status, ProductStatus.FOR_SALE)),
    );
  return result.length > 0 ? result[0] : null;
};

export const readProducts = async ({
  searchTag,
  keyword,
  sortOrder,
  pageIndex,
  pageSize = 20,
}: ProductsFilter & {
  sortOrder?: string;
  pageIndex?: number;
  pageSize?: number;
}) => {
  const db = await getMainDb();

  const take = pageSize > 0 ? pageSize : 20;
  const skip = (pageIndex ? pageIndex : 0) * take;

  let baseConditions: SQL[] = [eq(products.status, ProductStatus.FOR_SALE)];
  if (keyword) {
    baseConditions.push(like(products.name, `%${keyword}%`));
  }
  const sortOrderCondition =
    sortOrder === "priceLow"
      ? asc(products.price)
      : sortOrder === "priceHigh"
        ? desc(products.price)
        : asc(products.id);

  let query;
  if (searchTag && searchTag.length > 0) {
    query = db
      .selectDistinct({
        id: products.id,
        name: products.name,
        price: products.price,
        brand: products.brand,
        imageUrl: products.imageUrl,
        sourceId: products.sourceId,
        sourceType: products.sourceType,
      })
      .from(products)
      .innerJoin(productTags, eq(products.id, productTags.productId))
      .innerJoin(tags, eq(productTags.tagId, tags.id))
      .where(and(inArray(tags.name, searchTag), ...baseConditions))
      .orderBy(sortOrderCondition);
  } else {
    query = db
      .select({
        id: products.id,
        name: products.name,
        price: products.price,
        brand: products.brand,
        imageUrl: products.imageUrl,
        sourceId: products.sourceId,
        sourceType: products.sourceType,
      })
      .from(products)
      .where(and(...baseConditions))
      .orderBy(sortOrderCondition);
  }

  return await query.limit(take).offset(skip);
};

export const readProductsCount = async ({
  searchTag,
  keyword,
}: ProductsFilter) => {
  const db = await getMainDb();

  let baseConditions: SQL[] = [eq(products.status, ProductStatus.FOR_SALE)];
  if (keyword) {
    baseConditions.push(like(products.name, `%${keyword}%`));
  }

  let query;
  if (searchTag && searchTag.length > 0) {
    query = db
      .select({ count: countDistinct(products.id) })
      .from(products)
      .innerJoin(productTags, eq(products.id, productTags.productId))
      .innerJoin(tags, eq(productTags.tagId, tags.id))
      .where(and(inArray(tags.name, searchTag), ...baseConditions));
  } else {
    query = db
      .select({ count: count(products.id) })
      .from(products)
      .where(and(...baseConditions));
  }

  const result = await query;
  return result.length > 0 ? result[0].count : 0;
};

export const readPendingOrdersTotalPrice = async () => {
  const db = await getMainDb();
  const result = await db
    .select({ totalPrice: sum(orders.price) })
    .from(orders)
    .where(eq(orders.status, OrderStatus.PAYMENT_PENDING));
  return result.length > 0 ? Number(result[0].totalPrice || 0) : 0;
};

export const createOrder = async ({
  userDid,
  title,
  price,
  receiverName,
  receiverPhone,
  productId,
}: {
  userDid: string;
  title: string;
  price: number;
  receiverName: string;
  receiverPhone: string;
  productId: number;
}) => {
  const db = await getMainDb();
  const orderId = await db.transaction(async (tx) => {
    const insertId = await tx
      .insert(orders)
      .values({
        userDid,
        status: OrderStatus.PAYMENT_PENDING,
        title,
        price,
        receiverName,
        receiverPhone,
      })
      .returning();
    const { id: orderId } = insertId[0];

    await tx.insert(orderDetails).values({
      orderId,
      productId,
      price,
      count: 1,
    });
    return orderId;
  });
  return orderId;
};

export const cancelPendingOrder = async ({
  userDid,
  orderId,
}: {
  userDid?: string;
  orderId?: number;
}) => {
  const db = await getMainDb();
  await db
    .update(orders)
    .set({ status: OrderStatus.CANCELED })
    .where(
      and(
        userDid ? eq(orders.userDid, userDid) : undefined,
        orderId ? eq(orders.id, orderId) : undefined,
        eq(orders.status, OrderStatus.PAYMENT_PENDING),
      ),
    );
};

export const cancelPendingOrders = async (before: string) => {
  const db = await getMainDb();
  await db
    .update(orders)
    .set({ status: OrderStatus.CANCELED })
    .where(
      and(
        eq(orders.status, OrderStatus.PAYMENT_PENDING),
        lt(orders.time, before),
      ),
    );
};
