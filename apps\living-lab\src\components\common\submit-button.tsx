import { But<PERSON> } from "@workspace/ui/components/button";
import { cn } from "@workspace/ui/lib/utils";
import { ReactNode } from "react";

export const SubmitButton = ({
  children,
  disabled,
  className,
  onClick,
}: {
  children: ReactNode;
  disabled?: boolean;
  className?: string;
  onClick: () => void;
}) => {
  return (
    <div className={cn("h-[80px] w-full bg-[#ffffff] py-[12px]", className)}>
      <Button
        className="h-[52px] w-full rounded-[10px] text-[16px] font-semibold"
        onClick={onClick}
        disabled={disabled}
      >
        {children}
      </Button>
    </div>
  );
};
