"use client";

import { processPayment, processPlaceOrder } from "@/actions/product-action";
import { useOrderSession } from "@/components/common/session";
import { ProductDetailItem } from "@/types/product";
import { Button } from "@workspace/ui/components/button";
import { useShowModalAlert } from "@workspace/ui/hooks/modal";
import { useBridgeStore } from "@workspace/webview-state";
import { useRouter } from "next/navigation";
import { useCallback, useEffect, useState } from "react";

export const OrderButton = ({
  product,
  nonce,
}: {
  product: ProductDetailItem;
  nonce: string;
}) => {
  const router = useRouter();
  const [orderSession] = useOrderSession();
  const [disabled, setDisabled] = useState(false);
  const requestApproval = useBridgeStore((state) => state.requestApproval);

  const showModalAlert = useShowModalAlert();

  useEffect(() => {
    setDisabled(
      orderSession?.id !== product.id || orderSession?.nonce !== nonce,
    );
  }, [setDisabled]);

  const handlePurchase = useCallback(async () => {
    if (disabled) return;
    const payload = await processPlaceOrder({
      productId: product.id,
      name: product.name,
      price: product.price,
      nonce,
    });
    if (!payload.data) {
      showModalAlert({
        title: "주문 실패",
        children: (
          <span>
            <br />
            {payload.error}
          </span>
        ),
      });
      return;
    }
    const result = await requestApproval({
      type: "payment",
      payload: payload.data,
    });
    await processPayment(result);
  }, [product]);

  return (
    <Button
      className="h-[52px] w-full rounded-[10px] text-[16px] font-semibold shadow-lg transition-transform active:scale-[0.97]"
      onClick={handlePurchase}
      disabled={disabled}
    >
      결제하기
    </Button>
  );
};
