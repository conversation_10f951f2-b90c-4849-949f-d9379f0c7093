import { relations, sql } from "drizzle-orm";
import {
  bigint,
  integer,
  pgTable,
  primaryKey,
  smallint,
  timestamp,
  uniqueIndex,
  varchar,
} from "drizzle-orm/pg-core";

export const products = pgTable(
  "tb_store_product",
  {
    id: bigint("id", { mode: "number" })
      .primaryKey()
      .generatedByDefaultAsIdentity(),
    sourceType: varchar("source_type", { length: 50 }).notNull(), // e.g. "giftishow"
    sourceId: varchar("source_id", { length: 50 }).notNull(),
    name: varchar("name", { length: 255 }).notNull(),
    brand: varchar("brand", { length: 32 }),
    price: integer("price").notNull(),
    imageUrl: varchar("image_url", { length: 400 }).notNull(),
    status: smallint("status").notNull(),
    createdTime: timestamp("created_time")
      .default(sql`NOW()`)
      .notNull(),
    modifiedTime: timestamp("modified_time")
      .default(sql`CURRENT_TIMESTAMP`)
      .$onUpdate(() => new Date())
      .notNull(),
  },
  (table) => [
    {
      uniqueIndex: {
        productId: uniqueIndex("product_id").on(
          table.sourceType,
          table.sourceId,
        ),
      },
    },
  ],
);

export const tags = pgTable("tb_store_tag", {
  id: bigint("id", { mode: "number" })
    .primaryKey()
    .generatedByDefaultAsIdentity(),
  name: varchar("name", { length: 20 }).unique().notNull(),
});

export const productTags = pgTable(
  "tb_store_product_tag",
  {
    productId: bigint("product_id", { mode: "number" }).notNull(),
    tagId: bigint("tag_id", { mode: "number" }).notNull(),
  },
  (table) => [
    {
      pk: primaryKey({ columns: [table.productId, table.tagId] }),
    },
  ],
);
