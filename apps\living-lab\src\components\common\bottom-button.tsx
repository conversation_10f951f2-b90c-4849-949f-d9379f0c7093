import { ReactNode } from "react";
import { SubmitButton } from "./submit-button";

export const BottomButton = ({
  children,
  disabled,
  onClick,
}: {
  children: ReactNode;
  disabled?: boolean;
  onClick: () => void;
}) => {
  return (
    <div className="fixed bottom-[0px] w-full">
      <div className="h-[24px] w-full bg-gradient-to-t from-[#ffffff] to-[rgba(255,255,255,0)]"></div>
      <SubmitButton onClick={onClick} disabled={disabled} className="px-[20px]">
        {children}
      </SubmitButton>
    </div>
  );
};
