import { fetchUserInfo } from "@workspace/utils/api/platform-api";
import { encryptJWE } from "@workspace/utils/auth/jwt-utils";
import { Session, SESSION_KEY } from "@workspace/utils/auth/types";
import { cookies } from "next/headers";
import { NextRequest, NextResponse } from "next/server";

const token =
  "********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************";

export async function GET(request: NextRequest) {
  const result = await fetchUserInfo(token);
  const session: Session = {
    ...result,
    tenantCd: "gyept",
    tenantNm: "평택시",
    roles: [],
    platformToken: token,
  };
  const appToken = await encryptJWE(session);

  const cookieStorage = await cookies();
  cookieStorage.set(SESSION_KEY, appToken);

  const proto = request.headers.get("x-forwarded-proto") || "http";
  const host =
    request.headers.get("x-forwarded-host") || request.headers.get("host");

  return NextResponse.redirect(new URL("/home", `${proto}://${host}`));
}
