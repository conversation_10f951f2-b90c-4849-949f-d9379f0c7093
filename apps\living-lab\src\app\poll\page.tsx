import Header from "@/components/common/header";
import { EmptyList } from "@/components/common/list";
import { readPollMinYear, readPolls } from "@workspace/db/crud/living-lab/poll";
import { getSession } from "@workspace/utils/auth/session";
import { redirect, RedirectType } from "next/navigation";
import { Suspense } from "react";
import { GuidePanel } from "../suggest/components/common";
import { PollFilter } from "./components/poll-filter";
import { PollItem } from "./components/poll-item";
import { POLL_CATEGORIES } from "./consts";

const PollContents = async ({
  tenantCd,
  category,
  year,
  isPollClosed,
}: {
  tenantCd: string;
  category: string | undefined;
  year: number | undefined;
  isPollClosed: boolean;
}) => {
  const categoryValue = POLL_CATEGORIES.find((item) => item.value === category);
  const polls = await readPolls(
    tenantCd,
    categoryValue?.value,
    year,
    isPollClosed,
  );
  const currentYear = new Date().getFullYear();
  const minYear = isPollClosed ? await readPollMinYear(tenantCd) : currentYear;
  const years = Array.from(
    { length: currentYear - minYear + 1 },
    (_, i) => currentYear - i,
  );

  return (
    <>
      <PollFilter
        isPollClosed={isPollClosed}
        category={category}
        year={!year || Number.isNaN(year) ? currentYear : year!}
        selectableYears={years}
        count={polls.length}
      />
      <div className="flex flex-1 overflow-y-hidden">
        <div className="w-full overflow-y-auto bg-[#FAFBFF] py-[20px]">
          {polls.length < 1 ? (
            <EmptyList>
              {isPollClosed
                ? "현재 완료된 주민 투표가 없습니다."
                : "현재 진행 중인 주민 투표가 없습니다."}
              <div className="mt-[10px] text-[12px]">
                {isPollClosed
                  ? "주민 투표가 완료되면 확인하실 수 있습니다."
                  : "주민 투표가 등록되면 참여하실 수 있습니다."}
              </div>
            </EmptyList>
          ) : (
            <div className="flex flex-col space-y-[10px]">
              {polls.map((item, index) => (
                <PollItem key={index} item={item} />
              ))}
            </div>
          )}
        </div>
      </div>
    </>
  );
};

const PollContentsSkeleton = () => {
  return (
    <div className="flex flex-1 overflow-y-hidden">
      <div className="w-full overflow-y-auto bg-[#FAFBFF] py-[20px]">
        <div className="flex flex-col space-y-[10px]">
          {Array.from({ length: 3 }).map((_, index) => (
            <div
              key={index}
              className="mx-[20px] flex flex-row rounded-lg bg-white p-[20px] shadow-lg"
            >
              <div className="flex-1 flex-col">
                <div className="mb-[6px] h-[12px] w-[100px] animate-pulse bg-gray-200"></div>
                <div className="mb-[16px] h-[16px] w-[200px] animate-pulse bg-gray-200"></div>
                <div className="h-[12px] w-[150px] animate-pulse bg-gray-200"></div>
              </div>
              <div className="h-[40px] w-[40px] animate-pulse rounded-full bg-gray-200"></div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default async function PollPage({
  searchParams,
}: {
  searchParams: Promise<{ closed?: string; category?: string; year?: string }>;
}) {
  const session = await getSession();
  if (!session) redirect("/", RedirectType.replace);

  const { tenantCd } = session;
  const { closed, category, year } = await searchParams;

  return (
    <main className="flex max-h-screen min-h-screen flex-col bg-[#FFFFFF]">
      <Header title="주민투표" parent="/home" />
      <GuidePanel type="poll" />
      <Suspense fallback={<PollContentsSkeleton />}>
        <PollContents
          tenantCd={tenantCd}
          isPollClosed={!!closed}
          category={category}
          year={year ? Number.parseInt(year) : undefined}
        />
      </Suspense>
    </main>
  );
}
