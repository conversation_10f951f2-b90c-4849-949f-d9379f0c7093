import { savePendingFiles } from "@/app/api/common/form";
import {
  addSuggestComment,
  readSuggestComments,
} from "@workspace/db/crud/living-lab/suggest";
import { getSession } from "@workspace/utils/auth/session";
import { NextRequest, NextResponse } from "next/server";

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ suggestUid: number }> },
) {
  const session = await getSession();
  if (!session) {
    return NextResponse.json(new Error("Unauthorized"), { status: 401 });
  }

  const { suggestUid } = await params;
  if (!suggestUid) {
    return NextResponse.json({ error: "Invalid id" }, { status: 400 });
  }

  const result = await readSuggestComments(session.tenantCd, suggestUid);
  return NextResponse.json(result);
}

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ suggestUid: number }> },
) {
  const session = await getSession();
  if (!session) {
    return NextResponse.json(new Error("Unauthorized"), { status: 401 });
  }

  const { suggestUid } = await params;
  if (!suggestUid) {
    return NextResponse.json({ error: "Invalid id" }, { status: 400 });
  }

  try {
    const formData = await request.formData();
    const parentUid = formData.get("parentUid")?.toString();
    const content = formData.get("content")?.toString();
    const files = formData.getAll("pendingFiles");
    if (!content) {
      return NextResponse.json({ error: "내용 오류" }, { status: 400 });
    }

    const savedFiles = await savePendingFiles(files);

    const commentUid = await addSuggestComment(
      session.tenantCd,
      {
        suggestUid: suggestUid,
        parentUid: parentUid ? parseInt(parentUid) : null,
        userDid: session.userDid,
        name: session.userNm,
        content,
      },
      savedFiles,
    );
    return NextResponse.json({ commentUid });
  } catch (error) {
    const errorMessage =
      error instanceof Error ? error.message : "Unknown error";
    return NextResponse.json({ error: errorMessage }, { status: 500 });
  }
}
