import { CategoryBadge } from "@/components/common/badge";
import { readPosts } from "@workspace/db/crud/living-lab/post";
import { Skeleton } from "@workspace/ui/components/skeleton";
import Link from "next/link";

export const NoticeItemsSkeleton = () => {
  return (
    <div>
      {[...Array(3)].map((_, index) => (
        <div key={index} className="my-[4px] flex">
          <Skeleton className="mr-[8px] h-4 w-10" />
          <Skeleton className="h-4 w-full" />
        </div>
      ))}
    </div>
  );
};

export const NoticeItems = async ({ tenantCd }: { tenantCd: string }) => {
  const posts = await readPosts({ tenantCd, board: "notice", pageSize: 3 });
  return (
    <div>
      {posts.map((item, index) => (
        <Link key={index} href={`/post/notice/${item.uid}`}>
          <div className="my-[4px] flex items-center overflow-clip">
            <span className="truncate">
              <CategoryBadge className="mr-[8px]">
                {item.category}
              </CategoryBadge>
              {item.title}
            </span>
          </div>
        </Link>
      ))}
    </div>
  );
};
