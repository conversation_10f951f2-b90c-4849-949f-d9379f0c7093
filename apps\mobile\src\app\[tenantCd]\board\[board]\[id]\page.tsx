import { fetchBoardContent } from "@/actions/board/board-action";
import { MainContainer } from "@/components/main-container";
import { Skeleton } from "@workspace/ui/components/skeleton";
import { Metadata } from "next";
import { Suspense } from "react";

type BoardDetailPageProps = {
  params: Promise<{ tenantCd: string; board: string; id: string }>;
};

export const generateMetadata = async ({
  params,
}: BoardDetailPageProps): Promise<Metadata> => {
  const { board } = await params;

  return {
    title:
      board === "notice" ? "공지사항" : board === "faq" ? "자주묻는질문" : "",
  };
};

const BoardDetailSkeleton = () => {
  return (
    <div className="space-y-6">
      <Skeleton className="h-[32px] w-3/4" />
      <div className="mt-5 space-y-4">
        <Skeleton className="h-4 w-full" />
        <Skeleton className="h-4 w-full" />
        <Skeleton className="h-4 w-5/6" />
        <Skeleton className="h-4 w-4/5" />
        <Skeleton className="h-4 w-2/3" />
      </div>
    </div>
  );
};

const BoardDetail = async ({ board, id }: { board: string; id: number }) => {
  const result = await fetchBoardContent({ board, id });
  return (
    <div>
      <h1 className="text-[26px] font-semibold">{result.ttl}</h1>
      <div
        className="mt-[20px] text-[14px]"
        dangerouslySetInnerHTML={{ __html: result.dtlCn }}
      />
    </div>
  );
};

export default async function BoardDetailPage({
  params,
}: Readonly<BoardDetailPageProps>) {
  const { board, id } = await params;

  return (
    <MainContainer className="bg-white" gradient={false}>
      <Suspense fallback={<BoardDetailSkeleton />}>
        <BoardDetail board={board} id={parseInt(id, 10)} />
      </Suspense>
    </MainContainer>
  );
}
