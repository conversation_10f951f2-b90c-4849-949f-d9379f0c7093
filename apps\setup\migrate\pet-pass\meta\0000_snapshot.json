{"id": "5c2060bd-83d3-4324-9906-85b58f4bd25e", "prevId": "00000000-0000-0000-0000-000000000000", "version": "7", "dialect": "postgresql", "tables": {"public.tb_pets_info": {"name": "tb_pets_info", "schema": "", "columns": {"uid": {"name": "uid", "type": "integer", "primaryKey": true, "notNull": true, "identity": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "tb_pets_info_uid_seq", "schema": "public", "increment": "1", "startWith": "1", "minValue": "1", "maxValue": "2147483647", "cache": "1", "cycle": false}}, "user_did": {"name": "user_did", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "qrp_idx": {"name": "qrp_idx", "type": "integer", "primaryKey": false, "notNull": true}, "own_name": {"name": "own_name", "type": "<PERSON><PERSON><PERSON>(64)", "primaryKey": false, "notNull": true}, "pet_reg_no": {"name": "pet_reg_no", "type": "<PERSON><PERSON><PERSON>(15)", "primaryKey": false, "notNull": true}, "pet_name": {"name": "pet_name", "type": "<PERSON><PERSON><PERSON>(64)", "primaryKey": false, "notNull": true}, "pet_birth": {"name": "pet_birth", "type": "<PERSON><PERSON><PERSON>(64)", "primaryKey": false, "notNull": true}, "pet_weight": {"name": "pet_weight", "type": "<PERSON><PERSON><PERSON>(64)", "primaryKey": false, "notNull": true}, "pet_type": {"name": "pet_type", "type": "<PERSON><PERSON><PERSON>(1)", "primaryKey": false, "notNull": true}, "pet_breed": {"name": "pet_breed", "type": "<PERSON><PERSON><PERSON>(64)", "primaryKey": false, "notNull": true}, "pet_breed_code": {"name": "pet_breed_code", "type": "<PERSON><PERSON><PERSON>(64)", "primaryKey": false, "notNull": true}, "pet_breed_id": {"name": "pet_breed_id", "type": "<PERSON><PERSON><PERSON>(64)", "primaryKey": false, "notNull": true}, "pet_is_fierce": {"name": "pet_is_fierce", "type": "boolean", "primaryKey": false, "notNull": true}, "pet_hair_color_code": {"name": "pet_hair_color_code", "type": "<PERSON><PERSON><PERSON>(64)", "primaryKey": false, "notNull": true}, "pet_gender": {"name": "pet_gender", "type": "<PERSON><PERSON><PERSON>(64)", "primaryKey": false, "notNull": true}, "pet_neuter_yn": {"name": "pet_neuter_yn", "type": "<PERSON><PERSON><PERSON>(64)", "primaryKey": false, "notNull": true}, "pet_vaccinate_yn": {"name": "pet_vaccinate_yn", "type": "<PERSON><PERSON><PERSON>(64)", "primaryKey": false, "notNull": true}, "pet_rabies_virus_yn": {"name": "pet_rabies_virus_yn", "type": "<PERSON><PERSON><PERSON>(64)", "primaryKey": false, "notNull": true}, "pet_size_type": {"name": "pet_size_type", "type": "<PERSON><PERSON><PERSON>(1)", "primaryKey": false, "notNull": true}, "pet_office": {"name": "pet_office", "type": "<PERSON><PERSON><PERSON>(64)", "primaryKey": false, "notNull": true}, "relationship_own": {"name": "relationship_own", "type": "integer", "primaryKey": false, "notNull": true}, "val_yn": {"name": "val_yn", "type": "<PERSON><PERSON><PERSON>(1)", "primaryKey": false, "notNull": true, "default": "'Y'"}, "reg_dt": {"name": "reg_dt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "upd_dt": {"name": "upd_dt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.tb_pets_owner": {"name": "tb_pets_owner", "schema": "", "columns": {"uid": {"name": "uid", "type": "integer", "primaryKey": true, "notNull": true, "identity": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "tb_pets_owner_uid_seq", "schema": "public", "increment": "1", "startWith": "1", "minValue": "1", "maxValue": "2147483647", "cache": "1", "cycle": false}}, "user_did": {"name": "user_did", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "own_code": {"name": "own_code", "type": "<PERSON><PERSON><PERSON>(64)", "primaryKey": false, "notNull": true}, "val_yn": {"name": "val_yn", "type": "<PERSON><PERSON><PERSON>(1)", "primaryKey": false, "notNull": true, "default": "'Y'"}, "reg_dt": {"name": "reg_dt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "upd_dt": {"name": "upd_dt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.tb_pets_qr_entry": {"name": "tb_pets_qr_entry", "schema": "", "columns": {"uid": {"name": "uid", "type": "integer", "primaryKey": true, "notNull": true, "identity": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "tb_pets_qr_entry_uid_seq", "schema": "public", "increment": "1", "startWith": "1", "minValue": "1", "maxValue": "2147483647", "cache": "1", "cycle": false}}, "user_did": {"name": "user_did", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "qrp_idx": {"name": "qrp_idx", "type": "integer", "primaryKey": false, "notNull": true}, "own_name": {"name": "own_name", "type": "<PERSON><PERSON><PERSON>(64)", "primaryKey": false, "notNull": true}, "pet_name": {"name": "pet_name", "type": "<PERSON><PERSON><PERSON>(64)", "primaryKey": false, "notNull": true}, "accompanying_count": {"name": "accompanying_count", "type": "integer", "primaryKey": false, "notNull": true}, "pet_size_type": {"name": "pet_size_type", "type": "<PERSON><PERSON><PERSON>(1)", "primaryKey": false, "notNull": true}, "qr_text": {"name": "qr_text", "type": "<PERSON><PERSON><PERSON>(128)", "primaryKey": false, "notNull": true}, "val_yn": {"name": "val_yn", "type": "<PERSON><PERSON><PERSON>(1)", "primaryKey": false, "notNull": true, "default": "'Y'"}, "reg_dt": {"name": "reg_dt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "upd_dt": {"name": "upd_dt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}