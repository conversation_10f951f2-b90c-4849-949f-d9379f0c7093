"use client";

import { Button } from "@workspace/ui/components/button";
import { Input } from "@workspace/ui/components/input";
import { Search } from "lucide-react";
import { useRouter } from "next/navigation";
import { useCallback, useState } from "react";

export const SearchBar = ({
  category,
  initialKeyword,
}: {
  category?: string;
  initialKeyword: string;
}) => {
  const router = useRouter();
  const [value, setValue] = useState(initialKeyword);

  const onRequestSearch = useCallback(
    (keyword: string) => {
      const trimmedKeyword = keyword.trim();
      if (trimmedKeyword !== initialKeyword) {
        const params = new URLSearchParams({ keyword: trimmedKeyword });
        const basePath = category ? `/products/${category}` : "/products";
        router.push(`${basePath}?${params.toString()}`);
      }
    },
    [initialKeyword, router, category],
  );

  return (
    <div className="w-full py-8">
      <div className="relative">
        <Input
          type="text"
          placeholder="상품을 검색해 주세요"
          value={value || ""}
          onChange={(e) => setValue(e.target.value)}
          onKeyUp={(e) => {
            if (e.key === "Enter") {
              onRequestSearch(value);
            }
          }}
          className="pr-12"
        />
        <Button
          id="searchButton"
          type="button"
          size="icon"
          variant="ghost"
          onClick={() => onRequestSearch(value)}
          className="absolute right-2 top-1/2 h-8 w-8 -translate-y-1/2"
        >
          <Search className="h-4 w-4" />
        </Button>
      </div>
    </div>
  );
};
