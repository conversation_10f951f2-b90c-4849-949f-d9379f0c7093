"use client";

import {
  useQueryPointBalance,
  useQueryPointBalanceByTenantCd,
} from "@/actions/point/point-query";
import {
  YearMonthPicker,
  YearMonthType,
} from "@workspace/ui/components/custom/year-month-select";
import { format, setYear } from "date-fns";
import { useState } from "react";
import { PointTitle, PointTitleCoin } from "../../components/common";
import { PointBalances } from "../../components/point-balance";
import { PointHistoryEmpty } from "./point-history-list";

export const PointHistoryAllContent = ({ tenantCd }: { tenantCd: string }) => {
  const { data } = useQueryPointBalance();
  const totalBalance = data?.reduce((prev, item) => prev + item.balance, 0);

  const now = new Date();
  const [yearMonth, setYearMonth] = useState({
    year: format(now, "yyyy"),
    month: format(now, "MM"),
  });

  return (
    <div className="flex h-full flex-col">
      <div className="flex flex-col px-[20px]">
        <PointTitle
          title="CITYCUBE 포인트"
          titlePrefix={<PointTitleCoin />}
          balance={totalBalance}
        />
        <PointBalances tenantCd={tenantCd} data={data} />
      </div>

      <div className="flex flex-1 flex-col bg-[#ffffff]">
        <div className="w-full items-center justify-between p-[20px]">
          <YearMonthPicker
            value={yearMonth}
            onChange={setYearMonth}
            yearFrom={new Date().getFullYear() - 5}
            className="flex-1"
          />
        </div>
        <PointHistoryEmpty />
      </div>
    </div>
  );
};

export const PointHistoryLocalContent = ({
  tenantCd,
}: {
  tenantCd: string;
}) => {
  const { data } = useQueryPointBalanceByTenantCd(tenantCd);

  const now = new Date();
  const [yearMonth, setYearMonth] = useState({
    year: format(now, "yyyy"),
    month: format(now, "MM"),
  });

  return (
    <div className="flex h-full flex-col">
      <div className="p-[20px]">
        <PointTitle title={data?.name} balance={data?.balance} />
      </div>
      <div className="flex flex-1 flex-col bg-[#ffffff]">
        <div className="w-full items-center justify-between p-[20px]">
          <YearMonthPicker
            value={yearMonth}
            onChange={setYearMonth}
            yearFrom={new Date().getFullYear() - 5}
            className="flex-1"
          />
        </div>
        <PointHistoryEmpty />
      </div>
    </div>
  );
};
