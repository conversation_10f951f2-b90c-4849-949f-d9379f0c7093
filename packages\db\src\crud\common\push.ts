import { eq, getMainDb } from "@workspace/db/drizzle";
import { pushTargetInfo } from "@workspace/db/schema/common/push";
import { firstOrNull } from "@workspace/utils/array";

export const readPushTargetInfo = async (userDid: string) => {
  const db = await getMainDb();
  const result = await db
    .select()
    .from(pushTargetInfo)
    .where(eq(pushTargetInfo.userDid, userDid))
    .limit(1)
    .then(firstOrNull);
  return result;
};
