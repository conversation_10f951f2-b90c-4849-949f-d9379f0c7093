"use client";

import {
  ColumnDef,
  getCoreRowModel,
  useReactTable,
} from "@tanstack/react-table";
import { <PERSON><PERSON> } from "@toss/utils";
import { TermsGroupAndData } from "@workspace/db/crud/common/terms";
import { Button } from "@workspace/ui/components/button";
import { Checkbox } from "@workspace/ui/components/checkbox";
import { DataTable } from "@workspace/ui/components/data-table";
import { format } from "date-fns";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useMemo } from "react";

const getColumnDefs = (tenantCd: string) => {
  const columnDefs: ColumnDef<TermsGroupAndData>[] = [
    {
      id: "select",
      header: ({ table }) => (
        <Checkbox
          checked={
            table.getIsAllPageRowsSelected() ||
            (table.getIsSomePageRowsSelected() && "indeterminate")
          }
          onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
          aria-label="Select all"
        />
      ),
      cell: ({ row }) => (
        <Checkbox
          checked={row.getIsSelected()}
          onCheckedChange={(value) => row.toggleSelected(!!value)}
          aria-label="Select row"
        />
      ),
      enableSorting: false,
      enableHiding: false,
    },
    {
      accessorKey: "termsGroup.termsGroupTitle",
      header: "약관그룹",
    },
    {
      accessorKey: "termsData.termsVer",
      header: "약관버전",
    },
    {
      accessorKey: "termsData.termsTitle",
      header: "약관 제목",
      cell: ({ cell, row }) => (
        <Button variant="link" asChild>
          <Link
            href={`/${tenantCd}/board/terms/${row.original.termsData.termsId}`}
          >
            {cell.renderValue<string>()}
          </Link>
        </Button>
      ),
    },
    {
      accessorKey: "termsData.valYn",
      header: "사용여부",
      cell: ({ cell }) => {
        const value = cell.getValue<boolean>();
        return <span>{value ? "O" : "X"}</span>;
      },
    },
    {
      accessorKey: "termsData.mustYn",
      header: "필수동의",
      cell: ({ cell }) => {
        const value = cell.getValue<boolean>();
        return <span>{value ? "O" : "X"}</span>;
      },
    },
    {
      accessorKey: "termsData.privacyYn",
      header: "개인정보동의",
      cell: ({ cell }) => {
        const value = cell.getValue<boolean>();
        return <span>{value ? "O" : "X"}</span>;
      },
    },
    {
      accessorKey: "termsData.seq",
      header: "순서",
    },
    {
      accessorKey: "termsData.regDt",
      header: "등록일시",
      cell: ({ cell }) => {
        const value = cell.getValue<Date>();
        return <span>{format(value, "yyyy.MM.dd HH:mm")}</span>;
      },
    },
    {
      accessorKey: "termsData.chgDt",
      header: "수정일시",
      cell: ({ cell }) => {
        const value = cell.getValue<Date>();
        return <span>{format(value, "yyyy.MM.dd HH:mm")}</span>;
      },
    },
  ];

  return columnDefs;
};

const TermsTable = ({
  tenantCd,
  data,
}: {
  tenantCd: string;
  data: TermsGroupAndData[];
}) => {
  const columns: ColumnDef<TermsGroupAndData>[] = useMemo(
    () => getColumnDefs(tenantCd),
    [tenantCd],
  );

  const table = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
  });

  return (
    <div className="rounded-md border">
      <DataTable table={table} />
    </div>
  );
};

export const TermsList = ({
  tenantCd,
  data,
}: {
  tenantCd: string;
  data: TermsGroupAndData[];
}) => {
  const router = useRouter();
  return (
    <div className="mt-8">
      <TermsTable tenantCd={tenantCd} data={data} />
    </div>
  );
};
