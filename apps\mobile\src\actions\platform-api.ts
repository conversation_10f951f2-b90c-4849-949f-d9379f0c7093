import { getSession } from "@workspace/utils/auth/session";
import axios from "axios";
import type { AxiosRequestHeaders, InternalAxiosRequestConfig } from "axios";

const injectBearerToken = async (
  config: InternalAxiosRequestConfig,
): Promise<InternalAxiosRequestConfig> => {
  const session = await getSession();
  if (session?.platformToken) {
    config.headers.Authorization = `Bearer ${session.platformToken}`;
  }
  return config;
};

export const platformSaasApi = axios.create({
  baseURL:
    process.env.PLATFORM_SAAS_URL ?? "https://cw-dev.data-alliance.com/saas",
});
platformSaasApi.interceptors.request.use(injectBearerToken);

export const platformUserApi = axios.create({
  baseURL:
    process.env.PLATFORM_USER_URL ?? "https://cw-dev.data-alliance.com/user",
});
platformUserApi.interceptors.request.use(injectBearerToken);

export const platformRewardApi = axios.create({
  baseURL:
    process.env.PLATFORM_REWARD_URL ??
    "https://cw-dev.data-alliance.com/reward",
});
platformRewardApi.interceptors.request.use(injectBearerToken);

export const platformPushApi = axios.create({
  baseURL:
    process.env.PLATFORM_PUSH_URL ?? "https://cw-dev.data-alliance.com/push",
});
platformPushApi.interceptors.request.use(injectBearerToken);
