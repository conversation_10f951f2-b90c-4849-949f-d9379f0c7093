"use client";

import { SUGGEST_CATEGORIES } from "@/app/utils/consts";
import { zodResolver } from "@hookform/resolvers/zod";
import { Button } from "@workspace/ui/components/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormMessage,
} from "@workspace/ui/components/form";
import { Input } from "@workspace/ui/components/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@workspace/ui/components/select";
import { Textarea } from "@workspace/ui/components/textarea";
import { useRouter } from "next/navigation";
import { useCallback, useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";
import {
  FormFiles,
  UploadedFile,
  UploadFilesForm,
  UploadFilesView,
} from "../components/form";
import { deleteSuggest, useMutationSuggest } from "../query";
import { WriteNotice } from "./write-notice";

const formSchema = z.object({
  category: z.string().min(1, "카테고리를 선택해주세요."),
  title: z.string().min(1, "제목을 입력해주세요."),
  content: z.string().min(1, "내용을 입력해주세요."),
});

type SuggestEditModel = {
  suggestUid: number;
  cateogry: string;
  title: string;
  content: string;
  files: UploadedFile[];
};

export const SuggestForm = ({ model }: { model?: SuggestEditModel }) => {
  const router = useRouter();
  const { mutateAsync } = useMutationSuggest();
  const [attachedFiles, setAttachedFiles] = useState<FormFiles>({
    activeFiles: [],
    pendingFiles: [],
  });
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      category: "",
      title: "",
      content: "",
    },
  });

  useEffect(() => {
    if (!model) return;
    form.reset({
      category: model.cateogry,
      title: model.title,
      content: model.content,
    });
    setAttachedFiles({
      activeFiles: model.files,
      pendingFiles: [],
    });
  }, [form, model]);

  const handleDelete = useCallback(async () => {
    if (!model) return;
    if (!confirm("정책 제안을 삭제하시겠습니까?")) return;
    try {
      const result = await deleteSuggest(model.suggestUid);
      if (result.ok) {
        router.replace("/suggest");
      }
    } catch (error) {
      console.error("삭제 중 오류 발생:", error);
    }
  }, [model, router]);

  const handleSubmit = useCallback(
    async (data: z.infer<typeof formSchema>) => {
      try {
        const formData = new FormData();
        formData.append("category", data.category);
        formData.append("title", data.title);
        formData.append("content", data.content);
        const { activeFiles, pendingFiles } = attachedFiles;
        activeFiles.forEach((fileData) => {
          formData.append("activeFiles", fileData.uid.toString());
        });
        pendingFiles.forEach((fileData) => {
          formData.append("pendingFiles", fileData.file);
        });
        const response = model
          ? await mutateAsync({
              suggestUid: model.suggestUid,
              formData,
            })
          : await mutateAsync({ formData });
        const { suggestUid } = await response.json();
        console.log(suggestUid);
        router.replace(`/suggest/${suggestUid}`);
      } catch (error) {
        console.error("업로드 중 오류 발생:", error);
      }
    },
    [attachedFiles, model, mutateAsync, router],
  );

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)}>
        <div className="bg-[#FAFBFF] p-[20px]">
          <FormField
            control={form.control}
            name="category"
            render={({ field }) => (
              <FormItem className="mb-[10px]">
                <Select
                  onValueChange={field.onChange}
                  defaultValue={field.value}
                >
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="카테고리를 선택해주세요" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {SUGGEST_CATEGORIES.map((category, index) => (
                      <SelectItem key={index} value={category}>
                        {category}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="title"
            render={({ field }) => (
              <FormItem className="mb-[10px]">
                <FormControl>
                  <Input {...field} placeholder="제목을 입력해주세요" />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="content"
            render={({ field }) => (
              <FormItem className="mb-[10px]">
                <FormControl>
                  <Textarea
                    {...field}
                    placeholder="내용을 입력해주세요"
                    className="resize-none"
                    rows={5}
                  />
                </FormControl>
                <FormMessage>
                  {form.formState.errors.content?.message}
                </FormMessage>
              </FormItem>
            )}
          />
          <UploadFilesView
            files={attachedFiles}
            onFilesChange={setAttachedFiles}
          />
          <UploadFilesForm
            files={attachedFiles}
            onFilesChange={setAttachedFiles}
            className="mb-[20px] flex justify-end"
          />
          <WriteNotice>
            정책 제안은 지역발전을 위한 소중한 의견을 수집하기 위함입니다.
            <br />
            모든 의견이 적용되지 않을 수 있습니다. 채택된 의견은 검토대상이
            되며, 반영 여부에 따라 관련 게시물이 추가로 고지되나, 미 채택된
            의견은 별도의 결과가 고지되지 않습니다.
            <br />
            작성한 게시물에 대한 법적 책임은 작성자에 귀속되며 게시글 작성시
            욕설, 특정성단에 대한 비방, 허위사실등의 내용을 포함할 경우 운영
            정책과 관련 법률에 따라 제재 될 수 있습니다.
          </WriteNotice>
        </div>

        <div className="mx-[20px] my-[12px] bg-[#FFFFFF]">
          {model ? (
            <div className="flex space-x-[10px]">
              <Button
                type="button"
                className="bg-primary/10 text-primary hover:bg-primary/20 h-[52px] w-full rounded-[10px] text-[16px] font-semibold"
                disabled={form.formState.isSubmitting}
                onClick={handleDelete}
              >
                삭제
              </Button>
              <Button
                type="submit"
                className="h-[52px] w-full rounded-[10px] text-[16px] font-semibold"
                disabled={form.formState.isSubmitting}
              >
                수정
              </Button>
            </div>
          ) : (
            <Button
              type="submit"
              className="h-[52px] w-full rounded-[10px]"
              disabled={form.formState.isSubmitting}
            >
              <span className="text-[16px] font-semibold">작성</span>
            </Button>
          )}
        </div>
      </form>
    </Form>
  );
};
