import { getSession } from "@/lib/session";
import { cookies } from "next/headers";
import { NextRequest, NextResponse } from "next/server";

const publicRoutes = ["/auth", "/"];

export default async function middleware(request: NextRequest) {
  const baseURL = request.nextUrl.origin;

  const path = request.nextUrl.pathname;
  const session = await getSession();
  if (!session && !publicRoutes.some((route) => path.startsWith(route))) {
    return NextResponse.redirect(new URL("/auth/login", request.nextUrl));
  }

  return NextResponse.next({
    headers: {
      ...request.headers,
      "x-base-url": baseURL,
    },
  });
}

export const config = {
  matcher: ["/((?!api|assets/|_next/static|_next/image|.*\\.png$).*)"],
};
