"use client";

import { SubmitButton } from "@/components/common/submit-button";
import { SwitchCase } from "@toss/react";
import {
  SurveyAnswers,
  SurveyItemOption,
  SurveyItemWithOption,
} from "@workspace/db/crud/living-lab/survey";
import { SURVEY_ITEM_TYPE } from "@workspace/db/schema/living-lab/survey";
import { Checkbox } from "@workspace/ui/components/checkbox";
import { Input } from "@workspace/ui/components/input";
import { Label } from "@workspace/ui/components/label";
import {
  RadioGroup,
  RadioGroupItem,
} from "@workspace/ui/components/radio-group";
import { Textarea } from "@workspace/ui/components/textarea";
import { getErrorMessage, isActionError } from "@workspace/utils/consts/errors";
import { useRouter } from "next/navigation";
import { ReactNode, useEffect } from "react";
import { useShallow } from "zustand/shallow";
import { useMutationSurveyAnswers, useQuerySurveyAnswer } from "../../query";
import { useSurveyStore } from "../../store-provider";
import { SurveyQuestion } from "./survey-items";

const SurveyItemSingleSelect = ({
  itemUid,
  options,
  disabled,
}: {
  itemUid: number;
  options: SurveyItemOption[];
  disabled: boolean;
}) => {
  const [selectedOptions, setOption] = useSurveyStore(
    useShallow((state) => [state.selectedOptions, state.setOption]),
  );

  return (
    <RadioGroup
      className="space-y-[10px]"
      value={selectedOptions.get(itemUid)?.toString()}
      onValueChange={(value) => setOption(itemUid, parseInt(value, 10))}
      disabled={disabled}
    >
      {options.map((option) => (
        <div key={option.uid} className="flex items-center space-x-[8px]">
          <RadioGroupItem
            value={option.uid.toString()}
            id={option.uid.toString()}
            className="h-[24px] w-[24px]"
          />
          <Label
            htmlFor={option.uid.toString()}
            className="text-[14px] font-medium"
          >
            {option.content}
          </Label>
        </div>
      ))}
    </RadioGroup>
  );
};

const SurveyItemMultipleSelect = ({
  itemUid,
  options,
  disabled,
}: {
  itemUid: number;
  options: SurveyItemOption[];
  disabled: boolean;
}) => {
  const [selectedOptions, updateOption] = useSurveyStore(
    useShallow((state) => [state.selectedOptions, state.updateOption]),
  );
  const value = selectedOptions.get(itemUid);

  return (
    <div className="flex flex-col space-y-[10px]">
      {options.map((option) => (
        <div key={option.uid} className="flex items-center space-x-[8px]">
          <Checkbox
            value={option.uid.toString()}
            id={option.uid.toString()}
            checked={Array.isArray(value) ? value.includes(option.uid) : false}
            disabled={disabled}
            onCheckedChange={(checked) =>
              updateOption(itemUid, option.uid, !!checked)
            }
            className="h-[24px] w-[24px]"
          />
          <Label
            htmlFor={option.uid.toString()}
            className="text-[14px] font-medium"
          >
            {option.content}
          </Label>
        </div>
      ))}
    </div>
  );
};

const SurveyItemText = ({
  itemUid,
  type,
  disabled,
}: {
  itemUid: number;
  type: "short" | "long";
  disabled: boolean;
}) => {
  const [selectedOptions, setOption] = useSurveyStore(
    useShallow((state) => [state.selectedOptions, state.setOption]),
  );
  const value = selectedOptions.get(itemUid) ?? "";
  return type === "short" ? (
    <Input
      type="text"
      value={value as string}
      onChange={(e) => {
        setOption(itemUid, e.target.value);
      }}
      disabled={disabled}
    />
  ) : (
    <Textarea
      value={value as string}
      onChange={(e) => {
        setOption(itemUid, e.target.value);
      }}
      rows={3}
      disabled={disabled}
    />
  );
};

export const SurveyItemComponent = ({
  index,
  item,
  disabled,
}: {
  index: number;
  item: SurveyItemWithOption;
  disabled: boolean;
}) => {
  return (
    <SurveyQuestion index={index} item={item}>
      <SwitchCase
        value={item.type}
        caseBy={{
          [SURVEY_ITEM_TYPE.SINGLE_SELECT]: (
            <SurveyItemSingleSelect
              itemUid={item.uid}
              options={item.options}
              disabled={disabled}
            />
          ),
          [SURVEY_ITEM_TYPE.MULTIPLE_SELECT]: (
            <SurveyItemMultipleSelect
              itemUid={item.uid}
              options={item.options}
              disabled={disabled}
            />
          ),
          [SURVEY_ITEM_TYPE.SHORT_TEXT]: (
            <SurveyItemText
              itemUid={item.uid}
              type="long"
              disabled={disabled}
            />
          ),
        }}
      />
    </SurveyQuestion>
  );
};

export const SurveyItemsForm = ({
  surveyUid,
  items,
}: {
  surveyUid: number;
  items: SurveyItemWithOption[];
}) => {
  const reset = useSurveyStore(useShallow((state) => state.reset));
  const { data } = useQuerySurveyAnswer(surveyUid);
  const { mutateAsync } = useMutationSurveyAnswers(surveyUid);
  const router = useRouter();

  useEffect(() => {
    if (data === undefined || isActionError(data)) return;
    reset(data);
  }, [data, items, reset]);

  const isAnswered = (isActionError(data) ? 0 : Array(data?.keys()).length) > 0;
  const isDisabled = isAnswered;
  return (
    <>
      <div className="mt-[10px]">
        {items.map((item, index) => (
          <SurveyItemComponent
            key={item.uid}
            index={index}
            item={item}
            disabled={isDisabled}
          />
        ))}
      </div>
      <SurveySubmitButton
        items={items}
        disabled={isDisabled}
        onPressSubmit={async (answers) => {
          const result = await mutateAsync({ answers });
          if (isActionError(result)) {
            alert(getErrorMessage(result));
          } else {
            alert("설문이 제출되었습니다.");
            router.replace("/survey");
          }
        }}
      >
        {isAnswered ? "제출완료" : "제출하기"}
      </SurveySubmitButton>
    </>
  );
};

const SurveySubmitButton = ({
  items,
  disabled,
  children,
  onPressSubmit,
}: {
  items: SurveyItemWithOption[];
  disabled: boolean;
  children: ReactNode;
  onPressSubmit: (data: SurveyAnswers) => void;
}) => {
  const selectedOptions = useSurveyStore(
    useShallow((state) => state.selectedOptions),
  );
  const isDisabled =
    disabled ||
    items.some((item) => {
      const value = selectedOptions.get(item.uid);
      return (
        item.isRequired &&
        (value === undefined ||
          value === "" ||
          (Array.isArray(value) && value.length === 0))
      );
    });

  Array.from(selectedOptions.values()).some(
    (value) => value === undefined || value === "",
  );

  return (
    <SubmitButton
      disabled={isDisabled}
      onClick={() => onPressSubmit(selectedOptions)}
    >
      {children}
    </SubmitButton>
  );
};
