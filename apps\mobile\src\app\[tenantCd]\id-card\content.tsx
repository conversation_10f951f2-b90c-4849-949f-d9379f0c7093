"use client";

import { SwitchCase } from "@toss/react";
import { UnderbarTabs } from "@workspace/ui/components/underbar-tabs";
import { usePathname, useRouter } from "next/navigation";
import { CitizenIdPage } from "./components/citizen-id-page";
import { PetPassPage } from "./components/pet-pass/pet-pass-page";

const categories = [
  { label: "모바일 시민증", value: "citizen" },
  { label: "반려동물 신분증", value: "pet" },
];

export const IdCardContent = ({
  tenantId,
  initialCategory = categories[0].value,
}: {
  tenantId: string;
  initialCategory: string;
}) => {
  const router = useRouter();
  const pathname = usePathname();
  const setCategory = (value: string) => {
    const searchParams = new URLSearchParams();
    searchParams.set("category", value);
    router.replace(`${pathname}?${searchParams.toString()}`);
  };

  return (
    <div className="flex h-full flex-1 flex-col">
      <UnderbarTabs
        tabClassName="w-full"
        onValueChange={setCategory}
        value={initialCategory}
        items={categories}
      />
      <div className="mt-[8px] flex-1">
        <SwitchCase
          caseBy={{
            citizen: <CitizenIdPage tenantCd={tenantId} />,
            pet: <PetPassPage />,
          }}
          value={initialCategory}
          defaultComponent={<CitizenIdPage tenantCd={tenantId} />}
        />
      </div>
    </div>
  );
};
