"use client";

import { createLinkBridgeProvider } from "@webview-bridge/react";
import { BridgeStore } from "@webview-bridge/web";
import { WebViewState } from "./types";

type AppBridge = BridgeStore<WebViewState>;

export const {
  BridgeProvider,
  useBridgeStore,
  useBridgeStatus,
  useBridgeLoose,
  useBridgeEventListener,
} = createLinkBridgeProvider<AppBridge>({
  throwOnError: true,
  onReady: async () => {
    console.log("bridge is ready");
  },
  initialBridge: {
    insets: null,
    paddingBottom: null,
  },
});
