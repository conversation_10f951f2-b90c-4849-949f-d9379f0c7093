{"name": "cw-apps", "private": true, "scripts": {"build": "turbo build", "dev": "turbo dev", "lint": "turbo lint", "format": "prettier --write \"**/*.{ts,tsx,md}\"", "ui": "pnpm --filter @workspace/ui ui", "db:generate": "pnpm --filter @workspace/db db:generate", "migrate": "pnpm --filter ./apps/setup migrate", "docker:build": "./docker-build.sh", "share-env": "tsx scripts/share-env.ts"}, "devDependencies": {"@ianvs/prettier-plugin-sort-imports": "^4.4.0", "prettier": "^3.3.3", "prettier-plugin-tailwindcss": "^0.6.8", "tsx": "^4.19.3", "turbo": "^2.2.3", "typescript": "catalog:"}, "packageManager": "pnpm@9.12.3", "engines": {"node": ">=20", "pnpm": "^9.12.3"}}