"use client";

import { PointBalanceType } from "@/actions/point/pont-action";
import { ButtonNone } from "@/components/button";
import { isMainTenantCd } from "@/consts/consts";
import { Skeleton } from "@workspace/ui/components/skeleton";
import { cn } from "@workspace/ui/lib/utils";
import { Routes, useNavigate } from "@workspace/webview-state";

export const PointBalancesSkeleton = () => {
  return (
    <div className="mb-[12px] rounded-[10px] bg-gradient-to-br from-[#256BFF80] to-[#0057FFD9] p-[20px] text-white">
      <div className="mb-[10px] flex flex-row items-center justify-between text-[16px]">
        <Skeleton className="h-[20px] w-[80px] rounded bg-white/30" />
        <Skeleton className="h-[20px] w-[60px] rounded bg-white/30" />
      </div>
      <div className="my-[8px] h-[1px] w-full bg-white" />
      <div className="w-full space-y-[4px]">
        {[...Array(2)].map((_, i) => (
          <div
            key={i}
            className="flex flex-row items-center justify-between text-[14px]"
          >
            <Skeleton className="h-[18px] w-[80px] rounded bg-white/30" />
            <Skeleton className="h-[18px] w-[40px] rounded bg-white/30" />
          </div>
        ))}
      </div>
    </div>
  );
};

export const PointBalances = ({
  tenantCd,
  data,
}: {
  tenantCd: string;
  data: PointBalanceType[] | undefined;
}) => {
  const totalBalance = data?.reduce((p, n) => p + n.balance, 0);

  if (!data) return <PointBalancesSkeleton />;

  return (
    <div className="mb-[12px] rounded-[10px] bg-gradient-to-br from-[#256BFF80] to-[#0057FFD9] p-[20px] text-white">
      <PointBalanceItem
        name="CITYCUBE무상포인트"
        value={totalBalance}
        isOtherTenant={false}
        className="text-[16px]"
      />
      {Array.isArray(data) && data.length > 0 && (
        <>
          <div className="my-[8px] h-[1px] w-full bg-white" />
          <div className="w-full">
            {data.map((item, index) => (
              <PointBalanceItem
                key={index}
                name={item.name}
                value={item.balance}
                isOtherTenant={
                  !isMainTenantCd(item.tenantCd) && tenantCd !== item.tenantCd
                }
                className="text-[14px]"
              />
            ))}
          </div>
        </>
      )}
    </div>
  );
};

export const PointBalanceItem = ({
  name,
  value,
  isOtherTenant,
  className,
}: {
  name: string;
  value: number | undefined;
  isOtherTenant: boolean;
  className?: string;
}) => {
  const { navigate } = useNavigate();
  const openCitizenId = () => {
    navigate(Routes.citizenId());
  };

  return (
    <div
      className={cn("flex flex-row items-center justify-between", className)}
    >
      <div className="font-medium">{name}</div>
      <div className="flex-1 text-end font-semibold">
        {value} <span className="font-normal"> P</span>
      </div>
      {isOtherTenant && (
        <ButtonNone onClick={openCitizenId} className="ml-[4px]">
          <div className="flex items-center justify-center rounded-[16px] border-[1px] border-[#256bff] bg-white p-[6px] text-[10px] font-medium text-[#256bff]">
            확인하러가기
          </div>
        </ButtonNone>
      )}
    </div>
  );
};
