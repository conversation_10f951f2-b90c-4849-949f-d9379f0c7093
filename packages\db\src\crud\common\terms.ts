import "server-only";
import {
  and,
  eq,
  getMainDb,
  getTenantDb,
  like,
  or,
} from "@workspace/db/drizzle";
import {
  termsAgree,
  termsData,
  termsGroup,
} from "@workspace/db/schema/common/terms";
import { betweenDate } from "@workspace/db/utils";

export type TermsGroup = typeof termsGroup.$inferSelect;
export type TermsData = typeof termsData.$inferSelect;
export type TermsDataInsert = typeof termsData.$inferInsert;

export type TermsGroupAndData = {
  termsGroup: TermsGroup;
  termsData: TermsData;
};

export type TermsSearchFilter = {
  searchType?: string;
  keyword?: string;
  mustYn?: string;
  valYn?: string;
  regDt?: [Date?, Date?];
  chgDt?: [Date?, Date?];
};

const createTermsSearchFilter = (filters: TermsSearchFilter | undefined) => {
  const { searchType, keyword, mustYn, valYn, regDt, chgDt } = filters ?? {};
  const keywordFilter = keyword
    ? searchType === "all"
      ? or(
          like(termsData.termsTitle, `%${keyword}%`),
          like(termsData.termsConts, `%${keyword}%`),
        )
      : searchType === "title"
        ? like(termsData.termsTitle, `%${keyword}%`)
        : searchType === "contents"
          ? like(termsData.termsConts, `%${keyword}%`)
          : undefined
    : undefined;

  return and(
    keywordFilter,
    mustYn ? eq(termsData.mustYn, mustYn === "Y" ? true : false) : undefined,
    valYn ? eq(termsData.valYn, valYn === "Y" ? true : false) : undefined,
    regDt ? betweenDate(termsData.regDt, regDt) : undefined,
    chgDt ? betweenDate(termsData.chgDt, chgDt) : undefined,
  );
};

export const readTermsGroups = async (
  tenantCd: string,
): Promise<(typeof termsGroup.$inferSelect)[]> => {
  const tenantDb = await getTenantDb(tenantCd);
  const result = await tenantDb
    .select()
    .from(termsGroup)
    .where(eq(termsGroup.valYn, true))
    .orderBy(termsGroup.termsCd, termsGroup.termsVer);

  return result;
};

export const readTermsDatas = async (
  tenantCd: string,
  searchFilter: TermsSearchFilter,
): Promise<TermsGroupAndData[]> => {
  const tenantDb = await getTenantDb(tenantCd);
  const filters = createTermsSearchFilter(searchFilter);
  const query = tenantDb
    .select({ termsGroup, termsData })
    .from(termsData)
    .innerJoin(
      termsGroup,
      and(
        eq(termsGroup.termsCd, termsData.termsCd),
        eq(termsGroup.termsVer, termsData.termsVer),
        eq(termsGroup.valYn, true),
      ),
    )
    .where(filters)
    .orderBy(termsGroup.termsCd, termsData.seq, termsData.termsId);

  const result = await query;
  return result;
};

export const readTermsDataById = async (
  tenantCd: string,
  termsId: number,
): Promise<TermsGroupAndData | null> => {
  const tenantDb = await getTenantDb(tenantCd);
  const result = await tenantDb
    .select({ termsGroup, termsData })
    .from(termsData)
    .innerJoin(
      termsGroup,
      and(
        eq(termsGroup.termsCd, termsData.termsCd),
        eq(termsGroup.termsVer, termsData.termsVer),
        eq(termsGroup.valYn, true),
      ),
    )
    .where(eq(termsData.termsId, termsId))
    .limit(1);

  return result[0] || null;
};

export const readTermsAgreeData = async (userDid: string) => {
  const db = await getMainDb();
  const result = await db
    .select({ termsAgree, termsData })
    .from(termsAgree)
    .leftJoin(
      termsData,
      and(
        eq(termsData.termsCd, termsAgree.termsCd),
        eq(termsData.termsId, termsAgree.termsId),
      ),
    )
    .where(eq(termsAgree.userDid, userDid))
    .orderBy(termsAgree.termsCd, termsAgree.termsId);
  return result;
};
