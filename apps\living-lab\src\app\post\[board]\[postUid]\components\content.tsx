import { PostContent } from "@/components/common/post-contents";
import { formatAsDate } from "@/lib/datetime";
import {
  increasePostHitCount,
  readPost,
} from "@workspace/db/crud/living-lab/post";
import { notFound } from "next/navigation";

export const BoardDetailContent = async ({
  tenantId,
  board,
  postUid,
}: {
  tenantId: string;
  board: string;
  postUid: number;
}) => {
  const content = await readPost(tenantId, board, postUid);
  if (!content) return notFound();
  await increasePostHitCount(tenantId, board, postUid);

  const badges = [content.category];
  return (
    <PostContent
      badges={badges}
      title={content.title}
      name={content.name}
      depr={content.depr}
      regDate={content.updDate}
    >
      {content.content}
      {content.regDate !== content.updDate && (
        <div className="mt-[20px] text-[12px]">
          <span className="mr-[8px] text-[#808C98]">최초 등록일</span>
          {formatAsDate(content.regDate)}
        </div>
      )}
    </PostContent>
  );
};
