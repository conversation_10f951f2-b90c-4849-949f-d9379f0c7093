import "server-only";
import {
  and,
  count,
  countDistinct,
  desc,
  eq,
  getTenantDb,
  gte,
  lte,
  min,
  ne,
  or,
} from "@workspace/db/drizzle";
import {
  SURVEY_ITEM_TYPE,
  SURVEY_STATE,
  surveyAnswers,
  surveyItemOptions,
  surveyItems,
  surveys,
} from "@workspace/db/schema/living-lab/survey";
import { betweenYear, now } from "@workspace/db/utils";
import {
  ActionError,
  actionError,
  ErrorCode,
} from "@workspace/utils/consts/errors";
import dayjs from "dayjs";

export type Survey = typeof surveys.$inferSelect;
export type SurveyItem = typeof surveyItems.$inferSelect;
export type SurveyItemOption = typeof surveyItemOptions.$inferSelect;
export type SurveyItemWithOption = SurveyItem & { options: SurveyItemOption[] };

export type SurveyAnswers = Map<number, number[] | string>;

export const readSurveyMinYear = async (tenantId: string) => {
  const tenantDb = await getTenantDb(tenantId);
  const result = await tenantDb
    .select({ minDate: min(surveys.regDate) })
    .from(surveys)
    .where(eq(surveys.isValid, "Y"));
  return Number.parseInt(dayjs(result[0].minDate ?? new Date()).format("YYYY"));
};

export const readSurveys = async (
  tenantId: string,
  category: string | undefined,
  year: number | undefined,
  isSurveyClosed: boolean,
) => {
  const tenantDb = await getTenantDb(tenantId);
  const result = await tenantDb
    .select()
    .from(surveys)
    .where(
      and(
        category ? eq(surveys.category, category) : undefined,
        betweenYear(surveys.regDate, year),
        isSurveyClosed
          ? or(
              ne(surveys.state, SURVEY_STATE.IN_PROGRESS),
              gte(surveys.startDate, now()),
              lte(surveys.endDate, now()),
            )
          : and(
              eq(surveys.state, SURVEY_STATE.IN_PROGRESS),
              lte(surveys.startDate, now()),
              gte(surveys.endDate, now()),
            ),
        eq(surveys.isValid, "Y"),
      ),
    )
    .orderBy(desc(surveys.uid));

  return result;
};

export const readSurvey = async (
  tenantId: string,
  surveyUid: number,
  filterValid: boolean = true,
) => {
  const tenantDb = await getTenantDb(tenantId);
  const result = await tenantDb
    .select()
    .from(surveys)
    .where(
      and(
        eq(surveys.uid, surveyUid),
        filterValid ? eq(surveys.isValid, "Y") : undefined,
      ),
    );
  return result.length > 0 ? result[0] : null;
};

export const readSurveyItems = async (
  tenantId: string,
  surveyUid: number,
  filterValid: boolean = true,
) => {
  const tenantDb = await getTenantDb(tenantId);
  const result = await tenantDb
    .select({ item: surveyItems, itemOptions: surveyItemOptions })
    .from(surveyItems)
    .leftJoin(
      surveyItemOptions,
      and(
        eq(surveyItemOptions.itemUid, surveyItems.uid),
        filterValid ? eq(surveyItemOptions.isValid, "Y") : undefined,
      ),
    )
    .where(
      and(
        eq(surveyItems.surveyUid, surveyUid),
        filterValid ? eq(surveyItems.isValid, "Y") : undefined,
      ),
    )
    .orderBy(surveyItems.order);

  const itemsMap = result.reduce((acc, row) => {
    const item = acc.get(row.item.uid) ?? {
      ...row.item,
      options: [],
    };
    if (row.itemOptions) {
      item.options.push(row.itemOptions);
    }
    acc.set(row.item.uid, item);
    return acc;
  }, new Map<number, SurveyItemWithOption>());

  return Array.from(itemsMap.values());
};

export const addSurveyAnswers = async (
  tenantId: string,
  userDid: string,
  surveyUid: number,
  surveyItems: SurveyItemWithOption[],
  answers: SurveyAnswers,
): Promise<boolean | ActionError> => {
  const tenantDb = await getTenantDb(tenantId);

  return await tenantDb.transaction(async (tx) => {
    const existAnswers = await tx.$count(
      surveyAnswers,
      and(
        eq(surveyAnswers.userDid, userDid),
        eq(surveyAnswers.surveyUid, surveyUid),
        eq(surveyAnswers.isValid, "Y"),
      ),
    );
    if (existAnswers > 0) {
      return actionError(ErrorCode.SURVEY_ANSWERED_ALREADY);
    }

    const addSelect = async (item: SurveyItemWithOption, select: number) => {
      const option = item.options.find((o) => o.uid === select);
      if (!option) return false;

      await tx.insert(surveyAnswers).values({
        userDid,
        surveyUid,
        itemUid: item.uid,
        select: select,
      });
      return true;
    };

    for (const item of surveyItems) {
      const answer = answers.get(item.uid);
      if (
        (item.isRequired && answer === undefined) ||
        answer === "" ||
        (Array.isArray(answer) && answer.length === 0)
      ) {
        return actionError(ErrorCode.FORBIDDEN);
      }
      switch (item.type) {
        case SURVEY_ITEM_TYPE.SHORT_TEXT:
        case SURVEY_ITEM_TYPE.LONG_TEXT:
          if (typeof answer !== "string") {
            return actionError(ErrorCode.FORBIDDEN);
          }
          await tx.insert(surveyAnswers).values({
            userDid,
            surveyUid,
            itemUid: item.uid,
            answer,
          });
          break;
        case SURVEY_ITEM_TYPE.SINGLE_SELECT:
          if (!Array.isArray(answer)) {
            return actionError(ErrorCode.FORBIDDEN);
          }
          if (!(await addSelect(item, answer[0]))) {
            return actionError(ErrorCode.FORBIDDEN);
          }
          break;
        case SURVEY_ITEM_TYPE.MULTIPLE_SELECT:
          if (!Array.isArray(answer)) {
            return actionError(ErrorCode.FORBIDDEN);
          }
          for (const optionUid of answer) {
            if (!(await addSelect(item, optionUid))) {
              return actionError(ErrorCode.FORBIDDEN);
            }
          }
          break;
        default:
          return actionError(ErrorCode.FORBIDDEN);
      }
    }

    return true;
  });
};

export const readSurveyAnswers = async (
  tenantId: string,
  userDid: string,
  surveyUid: number,
) => {
  const tenantDb = await getTenantDb(tenantId);
  const result = await tenantDb
    .select({
      itemUid: surveyAnswers.itemUid,
      answer: surveyAnswers.answer,
      select: surveyAnswers.select,
    })
    .from(surveyAnswers)
    .where(
      and(
        eq(surveyAnswers.userDid, userDid),
        eq(surveyAnswers.surveyUid, surveyUid),
        eq(surveyAnswers.isValid, "Y"),
      ),
    );
  return result;
};

export const readSurveyRespondentCount = async (
  tenantId: string,
  surveyUid: number,
) => {
  const tenantDb = await getTenantDb(tenantId);
  const result = await tenantDb
    .select({ count: countDistinct(surveyAnswers.userDid) })
    .from(surveyAnswers)
    .where(
      and(
        eq(surveyAnswers.surveyUid, surveyUid),
        eq(surveyAnswers.isValid, "Y"),
      ),
    );
  return result[0].count;
};

export const readSurveyResultSummary = async (
  tenantId: string,
  surveyUid: number,
) => {
  const tenantDb = await getTenantDb(tenantId);
  const result = await tenantDb
    .select({
      itemUid: surveyAnswers.itemUid,
      select: surveyAnswers.select,
      count: count(),
    })
    .from(surveyAnswers)
    .where(
      and(
        eq(surveyAnswers.surveyUid, surveyUid),
        eq(surveyAnswers.isValid, "Y"),
      ),
    )
    .groupBy(surveyAnswers.itemUid, surveyAnswers.select);
  return result;
};

export const readSurveyTextAnswers = async (
  tenantId: string,
  surveyUid: number,
  itemUid: number,
) => {
  const tenantDb = await getTenantDb(tenantId);
  const result = await tenantDb
    .selectDistinct({ answer: surveyAnswers.answer })
    .from(surveyAnswers)
    .where(
      and(
        eq(surveyAnswers.surveyUid, surveyUid),
        eq(surveyAnswers.itemUid, itemUid),
        eq(surveyAnswers.isValid, "Y"),
      ),
    );
  return result;
};
