"use client";

import { ImageFiles } from "@/app/[tenantCd]/components/image-files";
import { convertNewlineToJSX } from "@toss/react";
import { Suggest, SuggestFile } from "@workspace/db/crud/living-lab/suggest";
import { Button } from "@workspace/ui/components/button";
import { Card, CardContent } from "@workspace/ui/components/card";
import dayjs from "dayjs";
import { ThumbsDown, ThumbsUp } from "iconoir-react";
import { useRouter } from "next/navigation";
import { useCallback } from "react";
import { submitSuggestAdmit } from "../../../actions/suggest";

export const SuggestContent = ({
  baseURL,
  tenantId,
  suggest,
}: {
  baseURL: string;
  tenantId: string;
  suggest: Suggest & { files: SuggestFile[] };
}) => {
  const router = useRouter();
  const handleAdopt = async () => {
    const isAdopted = suggest.isAdopted === "Y" ? false : true;
    if (
      !confirm(isAdopted ? "채택하시겠습니까?" : "채택을 취소하시겠습니까?")
    ) {
      return;
    }
    try {
      submitSuggestAdmit(tenantId, suggest.uid, isAdopted);
      router.refresh();
    } catch (e) {
      console.error(e);
    }
  };

  return (
    <Card className={suggest.isValid === "Y" ? "" : "border-red-500"}>
      <CardContent className="p-6">
        <div className="flex flex-col gap-4">
          <div className="flex items-center gap-2">
            <div className="text-primary w-24">제목</div>
            <div className="flex-1">{suggest.title}</div>

            <div className="flex items-center justify-end gap-4">
              <div className="flex gap-4">
                <div className="flex">
                  <ThumbsUp />
                  {suggest.upCount}
                </div>
                <div className="flex">
                  <ThumbsDown />
                  {suggest.downCount}
                </div>
              </div>
              <Button
                variant="outline"
                onClick={handleAdopt}
                className="w-[80px]"
              >
                {suggest.isAdopted === "Y" ? "채택 취소" : "채택"}
              </Button>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <div className="text-primary w-24">분류</div>
            <div className="flex-1">{suggest.category}</div>
            <div className="text-primary w-24">작성자</div>
            <div className="flex-1">{suggest.name}</div>
            <div className="text-primary w-24">작성일시</div>
            <div>{dayjs(suggest.regDate).format("YYYY-MM-DD HH:mm")}</div>
          </div>
          <div className="flex items-center gap-2 border-t pt-4">
            {convertNewlineToJSX(suggest.content)}
          </div>
          <div className="py-[20px]">
            <ImageFiles baseURL={baseURL} files={suggest.files} />
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
