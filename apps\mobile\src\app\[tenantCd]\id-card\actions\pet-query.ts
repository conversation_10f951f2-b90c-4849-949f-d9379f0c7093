"use client";

import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { useBridgeStore } from "@workspace/webview-state";
import {
  fetchAPMS,
  fetchDeletePet,
  fetchGetEntryPass as fetchGetQrEntry,
  fetchHairColors,
  fetchPets,
  fetchQrEntriesFromDb,
  fetchRegistPet,
} from "./pet-actions";
import {
  PetPassEntryRequest as PetPassQrEntryRequest,
  PetPassRegistPetRequest,
  PetType,
} from "./pet-types";

const petsKeys = {
  all: ["pets"] as const,
  pets: (fetchFrom: "db" | "api") =>
    [...petsKeys.all, "pets", fetchFrom] as const,
  qrEntries: () => [...petsKeys.all, "qrEntries"] as const,
  petColors: (breedCode: string) =>
    [...petsKeys.all, "petColors", { breedCode }] as const,
};

export const getPetType = (petBreedCode: string) => {
  return petBreedCode.startsWith("D") ? PetType.DOG : PetType.CAT;
};

export const useQueryPets = (fetchFrom: "db" | "api" = "api") => {
  return useQuery({
    queryKey: petsKeys.pets(fetchFrom),
    queryFn: async () => {
      return await fetchPets(fetchFrom);
    },
  });
};

export const useQueryPetHairColors = ({ breedCode }: { breedCode: string }) => {
  return useQuery({
    queryKey: petsKeys.petColors(breedCode),
    queryFn: async () => {
      return await fetchHairColors({ breedCode });
    },
  });
};

export const useMutationCheckAPMS = () => {
  return useMutation({
    mutationFn: async ({
      ownName,
      petRegNo,
    }: {
      ownName: string;
      petRegNo: string;
    }) => {
      const result = await fetchAPMS({
        ownName: ownName,
        petRegNo: petRegNo,
      });
      return result;
    },
  });
};

export const useMutationRegistPet = () => {
  return useMutation({
    mutationFn: async (model: PetPassRegistPetRequest) => {
      const result = await fetchRegistPet(model);
      return result;
    },
  });
};

export const useMutationDeletePet = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (model: { qrpIdx: number }) => {
      const result = await fetchDeletePet(model);
      return result;
    },
    onSettled: async () => {
      await queryClient.cancelQueries({ queryKey: petsKeys.all });
      await queryClient.invalidateQueries({ queryKey: petsKeys.all });
    },
  });
};

export const useQueryQrEntries = () => {
  return useQuery({
    queryKey: petsKeys.qrEntries(),
    queryFn: async () => {
      const result = await fetchQrEntriesFromDb();
      return result;
    },
  });
};

export const useMutationQrEntry = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (model: PetPassQrEntryRequest) => {
      const result = await fetchGetQrEntry(model);
      return result;
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: petsKeys.qrEntries() });
    },
  });
};
