import { getSurveyResults } from "@/actions/survey";
import { SwitchCase } from "@toss/react";
import {
  SurveyItemOption,
  SurveyItemWithOption,
} from "@workspace/db/crud/living-lab/survey";
import { SURVEY_ITEM_TYPE } from "@workspace/db/schema/living-lab/survey";
import { isActionError } from "@workspace/utils/consts/errors";
import { notFound } from "next/navigation";
import {
  SurveyItemResultText,
  SurveyResultCloseButton,
} from "./survey-item-text";
import { SurveyQuestion } from "./survey-items";

type SurveyResult = { select: number | null; count: number };

const SurveyItemResultSelect = ({
  options,
  surveyCount,
  results,
}: {
  options: SurveyItemOption[];
  surveyCount: number;
  results: SurveyResult[];
}) => {
  const maxCount = Math.max(...results.map((r) => r.count));

  return (
    <div>
      {options.map((option) => {
        const count = results.find((r) => r.select === option.uid)?.count || 0;
        const percent =
          surveyCount === 0 ? 0 : ((count / surveyCount) * 100).toFixed(1);
        const highlighted =
          maxCount === count
            ? "text-primary font-medium"
            : "font-normal text-[10px]";
        return (
          <div
            key={option.uid}
            className="flex flex-row justify-between py-[5px] text-[12px]"
          >
            <div className={highlighted}>{option.content}</div>

            <div className="flex flex-row">
              <span className={highlighted}>{count}</span>명
              <div className="ml-[5px] min-w-[40px] text-right">
                <span className={highlighted}>{percent}</span>%
              </div>
            </div>
          </div>
        );
      })}
    </div>
  );
};

const SurveyItemResultComponent = ({
  surveyUid,
  index,
  item,
  results,
  count,
}: {
  surveyUid: number;
  index: number;
  item: SurveyItemWithOption;
  results: SurveyResult[];
  count: number;
}) => {
  return (
    <SurveyQuestion index={index} item={item}>
      <SwitchCase
        value={item.type}
        caseBy={{
          [SURVEY_ITEM_TYPE.SINGLE_SELECT]: (
            <SurveyItemResultSelect
              options={item.options}
              results={results}
              surveyCount={count}
            />
          ),
          [SURVEY_ITEM_TYPE.MULTIPLE_SELECT]: (
            <SurveyItemResultSelect
              options={item.options}
              results={results}
              surveyCount={count}
            />
          ),
          [SURVEY_ITEM_TYPE.SHORT_TEXT]: (
            <SurveyItemResultText surveyUid={surveyUid} itemUid={item.uid} />
          ),
        }}
      />
    </SurveyQuestion>
  );
};

export const SurveyItemsResult = async ({
  surveyUid,
  items,
}: {
  surveyUid: number;
  items: SurveyItemWithOption[];
}) => {
  const results = await getSurveyResults(surveyUid);
  if (isActionError(results)) return notFound();

  return (
    <>
      <div className="mt-[10px]">
        {items.map((item, index) => (
          <SurveyItemResultComponent
            key={item.uid}
            surveyUid={surveyUid}
            index={index}
            item={item}
            count={results.count}
            results={results.answers.filter((r) => r.itemUid === item.uid)}
          />
        ))}
      </div>
      <SurveyResultCloseButton />
    </>
  );
};
