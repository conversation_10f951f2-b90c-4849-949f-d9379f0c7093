import { fetchTenants } from "@/actions/saas";
import { getSession } from "@/lib/session";
import { redirect } from "next/navigation";
import { SignUpForm } from "./signup-form";

export default async function SignUpPage() {
  const session = await getSession();
  if (session) {
    redirect("/");
  }

  const tenants = await fetchTenants();
  const tenantFiltered = [
    {
      tenantCd: "main",
      tenantNm: "소속 없음",
      operStts: "Operational",
    },
    ...tenants.filter((tenant) => tenant.tenantCd !== "main"),
  ];

  return <SignUpForm tenants={tenantFiltered} />;
}
