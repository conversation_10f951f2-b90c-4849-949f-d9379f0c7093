import { UserAndVc } from "@workspace/db/crud/common/user";
import { differenceInDays, format, subDays } from "date-fns";
import { UserDetailItem } from "./user-detail-common";

export const UserDetailProfile = async ({
  userData,
  isMainTenant,
}: {
  userData: UserAndVc;
  isMainTenant: boolean;
}) => {
  const { user, vc } = userData;
  const expireDDay = vc?.expireDt
    ? differenceInDays(vc.expireDt, Date.now())
    : null;

  return (
    <div className="grid grid-cols-1 gap-4 pb-4 md:grid-cols-2">
      <UserDetailItem label="생년월일" value={user.brdt} />
      {!isMainTenant ? (
        <>
          <UserDetailItem
            label="시민인증"
            value={
              vc?.dscdVal === false ? `인증됨 (D-${expireDDay})` : "미인증"
            }
          />
          <UserDetailItem
            label="시민인증일"
            value={vc?.issuedDt ? format(vc.issuedDt, "yyyy.MM.dd") : "-"}
          />
        </>
      ) : null}
      <UserDetailItem
        label="가입일"
        value={format(user.joinDt, "yyyy.MM.dd")}
      />
      <UserDetailItem
        label="최근로그인"
        value={user.lastLgnDt ? format(user.lastLgnDt, "yyyy.MM.dd") : "-"}
      />
      <UserDetailItem label="상태" value={user.userStts} />
    </div>
  );
};
