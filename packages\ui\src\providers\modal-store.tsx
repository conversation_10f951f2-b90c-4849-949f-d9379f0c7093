import { ReactNode } from "react";
import { createStore } from "zustand";
import { DialogContent } from "../components/custom/dialog";
import { DialogHeader, DialogTitle } from "../components/dialog";
import { cn } from "../lib/utils";

export type ModalOptions = {
  className?: string;
  closeButton?: boolean;
  onClose?: () => void;
};

type ModalContent = {
  children: ReactNode;
} & ModalOptions;

type ModalState = {
  nextId: number;
  modals: {
    id: number;
    isVisible: boolean;
    content: ModalContent;
  }[];
};

type ModalActions = {
  showModal: (model: {
    title?: ReactNode;
    children: ReactNode;
    options?: ModalOptions;
  }) => number;
  hideModal: (id: number) => void;
  trimModal: () => void;
};

export type ModalStore = ModalState & ModalActions;

const initialState = {
  nextId: 1,
  modals: [],
};

export const createModalStore = () =>
  createStore<ModalStore>((set, get) => ({
    ...initialState,
    showModal: ({ title, children, options }) => {
      const { nextId, modals, trimModal } = get();
      trimModal();
      set({
        modals: [
          ...modals,
          {
            id: nextId,
            isVisible: true,
            content: {
              children: (
                <DialogContent closeButton={options?.closeButton}>
                  <DialogHeader className={cn(title ? undefined : "hidden")}>
                    <DialogTitle className="text-left text-[16px] font-medium">
                      {title}
                    </DialogTitle>
                  </DialogHeader>
                  {children}
                </DialogContent>
              ),
              ...options,
            },
          },
        ],
        nextId: nextId + 1,
      });
      return nextId;
    },
    hideModal: (modalId) => {
      const { modals } = get();
      const modal = modals.find(
        (modal) => modal.id === modalId && modal.isVisible,
      );
      if (!modal) return;
      set({
        modals: modals.map((modal) =>
          modal.id === modalId ? { ...modal, isVisible: false } : modal,
        ),
      });
      window.history.back();
      setTimeout(() => {
        const { content } = modal;
        content.onClose?.();
      }, 200);
    },
    trimModal: () => {
      const { modals } = get();
      set({ modals: modals.filter((modal) => modal.isVisible) });
    },
  }));
