"use server";

import { platformUserApi } from "../platform-api";

export type BoardEntity /* NoticesEntity*/ = {
  id: number;
  ttl: string;
  cls: string;
  useYn: boolean;
  rgtrNm: string;
  dtlCn: string;
  regDT: number;
};

export const fetchBoardContent = async ({
  board,
  id,
}: {
  board: string;
  id: number;
}) => {
  const { data } = await platformUserApi.get<BoardEntity>(
    `/api/board/${board}/${id}`,
  );
  return data;
};
