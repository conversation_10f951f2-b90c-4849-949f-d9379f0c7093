import { icons } from "@/assets/icons";
import { getSession, getTenant } from "@workspace/utils/auth/session";
import Image, { StaticImageData } from "next/image";
import Link from "next/link";
import { Suspense } from "react";
import { HomeHeader } from "./components/header";
import { NoticeItems, NoticeItemsSkeleton } from "./components/notice-items";

type MenuItem = {
  icon: StaticImageData;
  text: string;
  href: string;
};

const menuItems: {
  [key: string]: MenuItem[];
} = {
  community: [
    { icon: icons.suggest, text: "정책제안", href: "/suggest" },
    { icon: icons.voting, text: "주민투표", href: "/poll" },
  ],
  discuss: [
    { icon: icons.discussion, text: "토론하기", href: "/survey" },
    { icon: icons.decision, text: "반영안내", href: "/post/result" },
  ],
};

const MenuSection = ({
  titleIcon,
  titleText,
  items,
}: {
  titleIcon: StaticImageData;
  titleText: string;
  items: MenuItem[];
}) => {
  return (
    <section className="mb-[20px] rounded-lg bg-white p-4 shadow-lg">
      <h3 className="mb-4 flex items-center text-[16px] font-bold">
        <Image src={titleIcon} className="mr-[10px] h-[30px] w-[30px]" alt="" />
        {titleText}
      </h3>
      <div className="grid grid-cols-2 space-x-[6px]">
        {items.map((item, i) => (
          <Link
            key={i}
            className="flex w-full flex-col place-items-center items-center justify-center rounded-lg border p-2"
            href={item.href}
          >
            <Image
              src={item.icon}
              className="mb-[8px] h-[30px] w-[30px]"
              alt=""
            />
            <span className="text-[12px]">{item.text}</span>
          </Link>
        ))}
      </div>
    </section>
  );
};

export default async function Home() {
  const session = await getSession();
  if (!session) throw new Error("세션 정보가 없습니다.");
  const { tenantCd, tenantNm } = session;

  return (
    <main className="flex min-h-screen flex-col bg-[#FAFBFF]">
      <HomeHeader />
      <div className="px-[20px] py-[10px]">
        <h2 className="mb-4 text-xl font-bold">{session.tenantNm}</h2>

        <div className="mb-6 flex items-center justify-between rounded-lg bg-blue-50 p-4">
          {/* TODO: 배너 불러오기 */}
          <div className="flex items-center space-x-2">
            <span>🎊</span>
            <span>윌컴 미션하고, 포인트 받아가세</span>
          </div>
          <Image
            src={icons.arrowRightSmall}
            className="ml-[10px] h-[18px] w-[18px]"
            alt="배너"
          />
        </div>

        <MenuSection
          titleIcon={icons.community}
          titleText="주민참여"
          items={menuItems.community}
        />
        <MenuSection
          titleIcon={icons.talk}
          titleText="정책토론"
          items={menuItems.discuss}
        />

        <section className="rounded-lg bg-white p-4 shadow-lg">
          <div className="mb-4 flex items-center justify-between">
            <Link href="/post/notice">
              <h3 className="mb-[10px] flex items-center text-[16px] font-bold">
                공지사항
                <Image
                  src={icons.arrowRightSmall}
                  className="ml-[10px] h-[18px] w-[18px]"
                  alt="공지사항 더보기"
                />
              </h3>
            </Link>
          </div>
          <Suspense fallback={<NoticeItemsSkeleton />}>
            <NoticeItems tenantCd={tenantCd} />
          </Suspense>
        </section>
      </div>
    </main>
  );
}
