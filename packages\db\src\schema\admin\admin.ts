import {
  index,
  integer,
  jsonb,
  pgTable,
  serial,
  timestamp,
  unique,
  varchar,
} from "drizzle-orm/pg-core";

export enum ADMIN_ROLE {
  SUPERVISOR = "SUPERVISOR",
  MANAGER = "MANAGER",
  SETTLE = "SETTLE",
  UNAUTHORIZED = "UNAUTHORIZED",
  BLOCKED = "BLOCKED",
}

const adminRoleEnums = [
  ADMIN_ROLE.SUPERVISOR,
  ADMIN_ROLE.MANAGER,
  ADMIN_ROLE.SETTLE,
  ADMIN_ROLE.UNAUTHORIZED,
  ADMIN_ROLE.BLOCKED,
] as const;

export enum ADMIN_ACTION {
  AUTH_SIGN_UP = "AUTH_SIGN_UP",
  AUTH_LOGIN = "AUTH_LOGIN",
  AUTH_LOGOUT = "AUTH_LOGOUT",
}

export const adminInfos = pgTable("tb_admin_info", {
  uid: integer("id").primaryKey().generatedByDefaultAsIdentity(),
  userId: varchar("user_id", { length: 255 }).notNull().unique(),
  userPwd: varchar("user_pwd", { length: 255 }).notNull(),
  userNm: varchar("user_nm", { length: 100 }).notNull(),
  userEmail: varchar("user_email", { length: 255 }).notNull(),
  userPhone: varchar("user_phone", { length: 20 }).notNull(),
  userDid: varchar("user_did", { length: 255 }),
  userDepr: varchar("user_depr", { length: 255 }),
  isValid: varchar("val_yn", { length: 1, enum: ["Y", "N"] })
    .notNull()
    .default("N"),
  lastLoginDt: timestamp("last_login_dt").defaultNow().notNull(),
  regDt: timestamp("reg_dt").defaultNow().notNull(),
  updDt: timestamp("upd_dt")
    .defaultNow()
    .notNull()
    .$onUpdate(() => new Date()),
});

export const adminRoles = pgTable(
  "tb_admin_role",
  {
    uid: serial("id").primaryKey(),
    adminUid: integer("admin_uid")
      .references(() => adminInfos.uid)
      .notNull(),
    tenantCd: varchar("tenant_cd", { length: 20 }),
    userRole: varchar("user_role", { length: 20, enum: adminRoleEnums })
      .notNull()
      .default(ADMIN_ROLE.UNAUTHORIZED),
    isValid: varchar("val_yn", { length: 1, enum: ["Y", "N"] })
      .notNull()
      .default("Y"),
    validFrom: timestamp("valid_from").defaultNow().notNull(),
    validTo: timestamp("valid_to"),
    regDt: timestamp("reg_dt").defaultNow().notNull(),
    updDt: timestamp("upd_dt")
      .defaultNow()
      .notNull()
      .$onUpdate(() => new Date()),
  },
  (table) => [
    index("idx_admin_role_tenant").on(table.tenantCd, table.adminUid),
  ],
);

export const adminLogs = pgTable(
  "tb_admin_logs",
  {
    uid: serial("id").primaryKey(),
    tenantCd: varchar("tenant_cd", { length: 20 }).notNull(),
    adminUid: integer("admin_uid")
      .references(() => adminInfos.uid)
      .notNull(),
    action: varchar("action", { length: 50 }).notNull(),
    description: varchar("description", { length: 255 }),
    metadata: jsonb("metadata"),
    ipAddress: varchar("ip_address", { length: 45 }),
    userAgent: varchar("user_agent", { length: 255 }),
    regDt: timestamp("reg_dt").defaultNow().notNull(),
    updDt: timestamp("upd_dt")
      .defaultNow()
      .notNull()
      .$onUpdate(() => new Date()),
  },
  (table) => [
    index("idx_admin_logs_admin").on(table.adminUid),
    index("idx_admin_logs_action").on(table.action),
    index("idx_admin_logs_reg_dt").on(table.regDt),
    index("idx_admin_logs_upd_dt").on(table.updDt),
  ],
);
