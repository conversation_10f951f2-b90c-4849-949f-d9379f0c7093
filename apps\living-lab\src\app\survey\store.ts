import { SurveyAnswers } from "@workspace/db/crud/living-lab/survey";
import { createStore } from "zustand";

type SurveyState = {
  selectedOptions: SurveyAnswers;
};

type SurveyActions = {
  setOption: (itemUid: number, optionUid: number | string) => void;
  updateOption: (itemUid: number, optionUid: number, checked: boolean) => void;
  reset: (answers: SurveyAnswers) => void;
};

export type SurveyStore = SurveyState & SurveyActions;

const initialState: SurveyState = {
  selectedOptions: new Map(),
};

export const createSurveyStore = () =>
  createStore<SurveyStore>((set) => ({
    ...initialState,
    setOption: (itemUid, optionUid) => {
      set((state) => {
        const selectedOptions = new Map(state.selectedOptions);
        selectedOptions.set(
          itemUid,
          typeof optionUid === "string" ? optionUid : [optionUid],
        );
        return { selectedOptions };
      });
    },
    updateOption: (itemUid, optionUid, checked) => {
      set((state) => {
        const selectedOptions = new Map(state.selectedOptions);
        const value = selectedOptions.get(itemUid);
        if (Array.isArray(value)) {
          selectedOptions.set(
            itemUid,
            checked
              ? [...value, optionUid]
              : value.filter((uid) => uid !== optionUid),
          );
        } else {
          selectedOptions.set(itemUid, checked ? [optionUid] : []);
        }
        return { selectedOptions };
      });
    },
    reset: (answers) => {
      set({ selectedOptions: answers });
    },
  }));
