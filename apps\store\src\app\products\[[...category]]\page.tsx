import { fetchProductsCount } from "@/actions/product-action";
import { Breadcrumb } from "@/components/common/breadcrumb";
import { PointInfo } from "@/components/common/point-info";
import { SearchBar } from "@/components/common/search-bar";
import { MainContainer } from "@/components/main-container";
import { ProductList } from "@/components/products/product-list";
import { ProductViewType } from "@/components/products/product-view-type";
import { SortOrder, SortOrderItem } from "@/components/products/sort-order";
import { checkCategory } from "@/types/category";
import { Skeleton } from "@workspace/ui/components/skeleton";
import { redirect } from "next/navigation";
import { Suspense } from "react";

const sortOrderList: SortOrderItem<string>[] = [
  { title: "인기상품순", value: "pop" },
  { title: "낮은가격순", value: "priceLow" },
  { title: "높은가격순", value: "priceHigh" },
];

const ProductsContent = async ({
  searchTag,
  keyword,
  sortOrder,
}: {
  searchTag: string[];
  keyword: string;
  sortOrder: string;
}) => {
  const productsCount = await fetchProductsCount({
    searchTag,
    keyword,
    sortOrder,
  });

  return (
    <div>
      <div className="mb-4 flex items-center justify-between">
        <span className="text-sm text-gray-600">
          <span className="text-primary font-semibold">{productsCount}</span>
          개의 상품이 있습니다.
        </span>
        <ProductViewType />
      </div>
      <ProductList
        count={productsCount}
        searchTag={searchTag}
        keyword={keyword}
        sortOrder={sortOrder}
      />
    </div>
  );
};

export default async function ProductsPage({
  params,
  searchParams,
}: Readonly<{
  params: Promise<{ category: string[] | undefined }>;
  searchParams: Promise<{
    keyword: string | undefined;
    order: string | undefined;
  }>;
}>) {
  const { category = [] } = await params;
  const { keyword = "", order: sortOrder = "pop" } = await searchParams;

  const searchTag = category.map((cat) => decodeURIComponent(cat));
  const categoryText = searchTag?.join("/");

  if (searchTag) {
    searchTag.some((cat) => {
      console.log("category", cat);
      if (!checkCategory(cat)) {
        redirect("/");
      }
    });
  }

  return (
    <MainContainer className="py-[20px]" gradient={false}>
      <PointInfo mileage={undefined} />
      <SearchBar category={categoryText} initialKeyword={keyword} />
      <Breadcrumb title={categoryText} searchTag={keyword} />
      <SortOrder items={sortOrderList} keyName="order" value={sortOrder} />
      <Suspense fallback={<Skeleton className="h-4 w-32" />}>
        <ProductsContent
          searchTag={searchTag}
          keyword={keyword}
          sortOrder={sortOrder}
        />
      </Suspense>
    </MainContainer>
  );
}
