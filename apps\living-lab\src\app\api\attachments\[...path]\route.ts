import { createReadStream, existsSync } from "fs";
import { join } from "path";
import { getSession } from "@workspace/utils/auth/session";
import { NextRequest, NextResponse } from "next/server";

export async function GET(
  _request: NextRequest,
  { params }: { params: Promise<{ path: string }> },
) {
  const session = await getSession();
  if (!session) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  const { path } = await params;
  const fullPath = join(process.env.ATTACHMENT_PATH!, ...path);
  if (!fullPath.startsWith(process.env.ATTACHMENT_PATH!)) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }
  if (existsSync(fullPath)) {
    try {
      const fileStream = createReadStream(fullPath);
      return new NextResponse(fileStream as unknown as ReadableStream, {
        headers: { "Content-Type": "image" },
      });
    } catch (e) {
      console.error(e);
    }
  }
  return NextResponse.json({ error: "Not Found" }, { status: 404 });
}
