import { Button, ButtonProps } from "@workspace/ui/components/button";
import { cn } from "@workspace/ui/lib/utils";
import { ReactNode } from "react";

export const PrimaryButton = ({
  className,
  children,
  ...props
}: {
  className?: string;
  children: ReactNode;
} & ButtonProps) => {
  return (
    <Button
      variant="default"
      asChild
      {...props}
      className={cn("rounded-[10px]", className)}
    >
      <button
        className={cn(
          "flex h-[40px] w-full gap-0 p-[10px] transition-transform active:scale-[0.97]",
        )}
      >
        {children}
      </button>
    </Button>
  );
};

export const SecondaryButton = ({
  className,
  children,
  ...props
}: {
  className?: string;
  children: ReactNode;
} & ButtonProps) => {
  return (
    <Button
      variant="secondary"
      asChild
      {...props}
      className={cn("rounded-[10px]", className)}
    >
      <button
        className={cn(
          "flex h-[40px] w-full gap-0 p-[10px] transition-transform active:scale-[0.97]",
        )}
      >
        {children}
      </button>
    </Button>
  );
};

export const OutlineButton = ({
  className,
  children,
  ...props
}: {
  className?: string;
  children: ReactNode;
} & ButtonProps) => {
  return (
    <Button variant="outline" asChild {...props}>
      <div
        className={cn(
          "flex w-full gap-0 rounded-[10px] p-[10px] transition-transform active:scale-[0.97]",
          className,
        )}
      >
        {children}
      </div>
    </Button>
  );
};

export const ButtonNone = ({
  className,
  children,
  ...props
}: {
  className?: string;
  children: ReactNode;
} & ButtonProps) => {
  return (
    <Button
      variant="none"
      asChild
      {...props}
      className={cn("transition-transform active:scale-[0.97]", className)}
    >
      {children}
    </Button>
  );
};
