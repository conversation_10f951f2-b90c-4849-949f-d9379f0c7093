import { getTenantDb, migrate } from "@workspace/db/drizzle";

const tenantIds = ["gyept"];

const sleep = (ms: number) => new Promise((resolve) => setTimeout(resolve, ms));

const runMigrate = async (tenantId: string, serviceName: string) => {
  console.log(`[${serviceName}] Running migrations: ${tenantId}`);
  const tenantDb = await getTenantDb(tenantId);
  await migrate(tenantDb, {
    migrationsFolder: `${__dirname}/migrate/${serviceName}`,
    migrationsTable: `__${serviceName}_migrations`,
  });
};

const runMigrations = async (tenantIds: string[]) => {
  for (const tenantId of tenantIds) {
    console.log(`* Starting migration for tenant: ${tenantId}`);
    try {
      // 테넌트 별로 운영되는 서비스
      await runMigrate(tenantId, "living-lab");
      console.log(`* Migrations completed for tenant: ${tenantId}`);
    } catch (error) {
      console.error(`* Migrations failed for tenant: ${tenantId}\n${error}`);
      process.exit(1);
    }
  }
};

const runMigrationsForCommon = async () => {
  console.log(`* Starting migration for common service`);
  try {
    // 공통 서비스
    await runMigrate("citycube", "admin");
    await runMigrate("citycube", "pet-pass");
    await runMigrate("citycube", "store");
  } catch (error) {
    console.error(`* Migrations failed for common: ${error}`);
    process.exit(1);
  }
  console.log(`* Migrations completed for common service`);
};

(async () => {
  await runMigrationsForCommon();
  await runMigrations(tenantIds);
  console.log("Migration process completed.");
  process.exit(0);
})();
