import { NextRequest, NextResponse } from "next/server";
import { fetchUserInfo } from "../api/platform-api";
import { encryptJWE, encryptJWT } from "./jwt-utils";
import { decryptPlatformJWT } from "./platform-auth";
import { getPlatformTokenData } from "./session";
import { Session, SESSION_KEY } from "./types";

const AUTHORIZATION_KEY = "Authorization";
const TENANT_ID_KEY = "X-Tenant-Id";

export const authMiddleware = async (request: NextRequest) => {
  const baseURL = request.nextUrl.origin;
  const response = NextResponse.next({
    headers: {
      ...request.headers,
      "x-base-url": baseURL,
    },
  });

  const authorization = request.headers.get(AUTHORIZATION_KEY);
  const tenantCd = request.headers.get(TENANT_ID_KEY);

  if (!authorization) return response;

  console.log("Request:", request.url);
  console.log("Authorization:", authorization);
  console.log("Tenant ID:", tenantCd);

  const platformToken = authorization.split(" ")[1];
  if (!platformToken) return response;

  try {
    const payload = await getPlatformTokenData(platformToken);
    if (!payload.user_name) {
      return response;
    }
    const result = await fetchUserInfo(platformToken);
    const tenantInfo = result.tenants.find(
      (tenant) => tenant.tenantCd === tenantCd,
    );
    const session: Session = {
      ...result,
      platformToken,
      tenantCd: tenantInfo?.tenantCd || "main",
      tenantNm: tenantInfo?.tenantNm || "",
      roles: [], // TODO: Fetch roles from the API or somehow...
    };
    const appToken = await encryptJWE(session);
    response.cookies.set(SESSION_KEY, appToken, {
      httpOnly: true,
      sameSite: "lax",
      secure: process.env.NODE_ENV === "production",
      maxAge: 60 * 60 * 24,
      path: "/",
    });
  } catch (e) {
    const error = e as Error;
    console.error("Error checking session:", error);
    return NextResponse.json(
      { message: error.message || "Invalid token" },
      { status: 401 },
    );
  }

  return response;
};
