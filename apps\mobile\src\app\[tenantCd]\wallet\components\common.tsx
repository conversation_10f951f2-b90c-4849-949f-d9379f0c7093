import { commaizeNumber } from "@toss/utils";
import { cn } from "@workspace/ui/lib/utils";
import Image from "next/image";
import { ReactNode } from "react";

export const UserName = ({ userNm: userNm }: { userNm: string }) => {
  const tracking = userNm.length > 5 ? "tracking-[2px]" : "tracking-[10px]";
  return (
    <div className={cn("flex-1 text-end text-[22px] font-semibold", tracking)}>
      {userNm}
    </div>
  );
};

export const PointTitleCoin = () => (
  <Image
    src="/assets/wallet/point-coin.png"
    width={26}
    height={26}
    alt="포인트"
    className="mr-[8px]"
  />
);

export const PointTitle = ({
  title,
  titlePrefix,
  balance,
}: {
  title?: string;
  titlePrefix?: ReactNode;
  balance: number | undefined;
}) => {
  return (
    <div className="mt-[10px] flex items-center justify-between p-[12px]">
      <div className="flex items-center">
        {titlePrefix}
        <span className="text-[18px] font-semibold">{title}</span>
      </div>
      <span className="text-[26px] font-bold">
        {balance && commaizeNumber(balance)}
        <span className="text-[18px] font-medium"> P</span>
      </span>
    </div>
  );
};
