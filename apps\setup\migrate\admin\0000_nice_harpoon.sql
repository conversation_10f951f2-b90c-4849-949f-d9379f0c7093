CREATE TABLE "tb_admin_info" (
	"id" integer PRIMARY KEY GENERATED BY DEFAULT AS IDENTITY (sequence name "tb_admin_info_id_seq" INCREMENT BY 1 MINVALUE 1 MAXVALUE 2147483647 START WITH 1 CACHE 1),
	"user_id" varchar(255) NOT NULL,
	"user_pwd" varchar(255) NOT NULL,
	"user_nm" varchar(100) NOT NULL,
	"user_email" varchar(255) NOT NULL,
	"user_phone" varchar(20) NOT NULL,
	"user_did" varchar(255),
	"user_depr" varchar(255),
	"val_yn" varchar(1) DEFAULT 'N' NOT NULL,
	"last_login_dt" timestamp DEFAULT now() NOT NULL,
	"reg_dt" timestamp DEFAULT now() NOT NULL,
	"upd_dt" timestamp DEFAULT now() NOT NULL,
	CONSTRAINT "tb_admin_info_user_id_unique" UNIQUE("user_id")
);
--> statement-breakpoint
CREATE TABLE "tb_admin_logs" (
	"id" serial PRIMARY KEY NOT NULL,
	"tenant_cd" varchar(20) NOT NULL,
	"admin_uid" integer NOT NULL,
	"action" varchar(50) NOT NULL,
	"description" varchar(255),
	"metadata" jsonb,
	"ip_address" varchar(45),
	"user_agent" varchar(255),
	"reg_dt" timestamp DEFAULT now() NOT NULL,
	"upd_dt" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "tb_admin_role" (
	"id" serial PRIMARY KEY NOT NULL,
	"admin_uid" integer NOT NULL,
	"tenant_cd" varchar(20),
	"user_role" varchar(20) DEFAULT 'UNAUTHORIZED' NOT NULL,
	"val_yn" varchar(1) DEFAULT 'Y' NOT NULL,
	"valid_from" timestamp DEFAULT now() NOT NULL,
	"valid_to" timestamp,
	"reg_dt" timestamp DEFAULT now() NOT NULL,
	"upd_dt" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
ALTER TABLE "tb_admin_logs" ADD CONSTRAINT "tb_admin_logs_admin_uid_tb_admin_info_id_fk" FOREIGN KEY ("admin_uid") REFERENCES "public"."tb_admin_info"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "tb_admin_role" ADD CONSTRAINT "tb_admin_role_admin_uid_tb_admin_info_id_fk" FOREIGN KEY ("admin_uid") REFERENCES "public"."tb_admin_info"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
CREATE INDEX "idx_admin_logs_admin" ON "tb_admin_logs" USING btree ("admin_uid");--> statement-breakpoint
CREATE INDEX "idx_admin_logs_action" ON "tb_admin_logs" USING btree ("action");--> statement-breakpoint
CREATE INDEX "idx_admin_logs_reg_dt" ON "tb_admin_logs" USING btree ("reg_dt");--> statement-breakpoint
CREATE INDEX "idx_admin_logs_upd_dt" ON "tb_admin_logs" USING btree ("upd_dt");--> statement-breakpoint
CREATE INDEX "idx_admin_role_tenant" ON "tb_admin_role" USING btree ("tenant_cd","admin_uid");