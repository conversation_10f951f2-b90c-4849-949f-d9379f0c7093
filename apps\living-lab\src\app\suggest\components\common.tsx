import Guide from "@/components/common/guide";
import { convertNewlineToJSX } from "@toss/react";
import { getImageURL } from "@workspace/utils/files";

export type GuideTypes =
  | "suggest"
  | "poll"
  | "pollGeneral"
  | "pollYesNo"
  | "pollMultiple"
  | "pollSign"
  | "survey"
  | "notice"
  | "result";

export const GuidePanel = ({ type }: { type: GuideTypes }) => {
  const guideMessage = {
    suggest: "우리 지역의 발전을 위해\n소중한 의견을 제시해주세요!",
    poll: "우리 지역의 발전을 위해\n소중한 의견을 투표해주세요!",
    pollGeneral:
      "투표는 익명으로 진행 됩니다.\n제시된 의견 중 한가지를 선택해 주세요.",
    pollYesNo:
      "투표는 익명으로 진행 됩니다.\n찬성 또는 반대 중 한가지를 선택해 주세요.",
    pollMultiple:
      "투표는 익명으로 진행 됩니다.\n제시된 의견 중 원하는 내용을 선택해 주세요.\n중복투표 가능합니다.",
    pollSign: "서명 및 본인 인증을 완료해주세요.\n인증 후 투표가 완료됩니다.",
    survey:
      "제안된 정책에 대한 설문조사를 실시합니다.\n우리 지역의 발전을 위해 다양한 의견과 생각을 공유해 주세요.",
    notice: "주요 공지사항 안내입니다.",
    result: "우리지역의 발전을 위해 \n채택된 정책입니다.",
  };

  return (
    <div className="flex flex-col border-b-[1px] border-[#F5F5F5] px-[20px] py-[10px]">
      <Guide>{convertNewlineToJSX(guideMessage[type])}</Guide>
    </div>
  );
};

export const ImageFiles = ({
  files,
  baseURL,
}: {
  files: { uri: string; originalName: string }[];
  baseURL: string;
}) => {
  if (files.length < 1) return;
  return (
    <div>
      {files.map((file, index) => (
        <div key={index} className="mb-[10px]">
          {
            // eslint-disable-next-line @next/next/no-img-element
            <img
              src={getImageURL(baseURL, file.uri)}
              alt={file.originalName}
              className="max-h-[320px] max-w-full border-[2px]"
            />
          }
        </div>
      ))}
    </div>
  );
};
