import { ReactNode } from "react";

export const UserDetailItem = ({
  label,
  value,
}: {
  label?: ReactNode;
  value?: ReactNode;
}) => {
  return (
    <div className="flex flex-col gap-2">
      <span className="text-xs text-gray-400">{label}</span>
      <span className="text-gray text-base font-medium">{value}</span>
    </div>
  );
};

export const UserDetailSubject = ({ children }: { children: ReactNode }) => {
  return <h2 className="mb-4 text-lg font-semibold">{children}</h2>;
};
