export class PetPassError extends Error {
  errorData: PetPassErrorResponse;
  constructor(errorData: PetPassErrorResponse) {
    super(errorData.resultMsg);
    this.errorData = errorData;

    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, PetPassError);
    }
  }
}

export enum PetType {
  CAT = "C",
  DOG = "D",
}

export enum PetSizeType {
  SMALL = "S",
  MEDIUM = "M",
  LARGE = "L",
}

export type PetPassResponse = {
  statusCode: number;
  statusMsg: string;
  resultCode: string;
  resultMsg: string;
};

export type PetPassErrorResponse = {
  statusCode: Exclude<number, 200>;
  errorDetails: string;
} & PetPassResponse;

export type PetPassCommonResponse<
  T extends object,
  U extends object,
> = PetPassResponse & {
  statusCode: 200;
  request: T;
  response: U;
};

export type PetPassOwnerResponse = PetPassCommonResponse<
  never,
  {
    ownCi: string;
    ownCode: string;
    ownName: string;
    ownPhone: string;
    ownEmail: string;
    ownBirth: string;
    ownGender: string;
    ownDeletion: boolean;
    ownDelDatetime: string | null;
    insDatetime: string;
    updDatetime: string;
  }
>;

export type PetPassRegistOwnerRequest = {
  ownCi: string;
  ownCode: string;
  ownName: string;
  ownPhone: string;
  ownEmail: string;
  ownBirth: string;
  ownGender: string;
};

export type PetPassRegistOwnerDto = PetPassRegistOwnerRequest & {
  ownDeletion: boolean;
  ownDelDatetime: string | null;
  insDatetime: string;
  updDatetime: string;
};

export type PetPassRegistOwnerResponse = PetPassCommonResponse<
  PetPassRegistOwnerRequest,
  PetPassRegistOwnerDto
>;

export type PetPassPetDto = {
  qrpIdx: number;
  ownName: string;
  petRegNo: string;
  petName: string;
  petBirth: string;
  petWeight: string;
  petType: "D" | "C";
  petBreed: string;
  petBreedCode: string;
  petBreedId: string;
  petIsFierce: boolean;
  petHairColorCode: string;
  petGender: "M" | "F";
  petNeuterYn: "Y" | "N";
  petVaccinateYn: "Y" | "N";
  petRabiesVirusYn: "Y" | "N";
  petSizeType: PetSizeType;
  petOffice: string;
  relationshipOwn: number;
  isVerified: boolean;
  qrpDeletion: boolean;
  insDatetime: string;
};

export type PetPassPetsResponse = PetPassCommonResponse<never, PetPassPetDto[]>;

export type PetPassRegistPetRequest = {
  ownName: string;
  petRegNo: string;
  petBirth: string;
  petWeight: string;
  petName: string;
  petType: string;
  petBreed: string;
  petBreedCode: string;
  petBreedId: string;
  petIsFierce: boolean;
  petHairColorCode: string;
  petGender: string;
  petNeuterYn: "Y" | "N";
  petVaccinateYn: string;
  petRabiesVirusYn: string;
  petSizeType: PetSizeType;
  petOffice: string;
  relationshipOwn: number;
  isVerified: boolean;
};

export type PetPassEntryRequest = {
  qrpIds: number[];
  accompanyingCount: number;
};

export type PetPassEntryDto = {
  ownName: string;
  petName: string;
  accompanyingCount: number;
  petSizeType: PetSizeType;
  qrText: string;
};

export type PetPassRegistPetResponse = PetPassCommonResponse<
  PetPassRegistPetRequest,
  PetPassPetDto
>;

export type PetPassDeletePetResponse = PetPassCommonResponse<
  never,
  PetPassPetDto
>;

export type PetPassEntryResponse = PetPassCommonResponse<
  PetPassEntryRequest,
  PetPassEntryDto[]
>;

export type PetPassAPMSRequest = {
  petRegNo: string;
  ownName: string;
};

export type PetPassAPMSDto = {
  petRegNo: string;
  rfidCd: string;
  petName: string;
  petGender: string;
  petBreedName: string;
  petNeuterYn: string;
  officeName: string;
  officeTel: string;
};

export type PetPassAPMSResponse = PetPassCommonResponse<
  PetPassAPMSRequest,
  PetPassAPMSDto
>;

export type PetPassBreedDto = {
  petBreedCode: string;
  petBreedName: string;
  isFierce: "0" | "1";
};

export type PetPassBreedsResponse = PetPassCommonResponse<
  never,
  PetPassBreedDto[]
>;

export type PetPassHairColorDto = {
  id: number;
  petHairColorCode: string;
  petBreedCode: string;
  petHairColorName: string;
  delYn: string;
};
export type PetPassHairColorsResponse = PetPassCommonResponse<
  never,
  PetPassHairColorDto[]
>;

export function isPetPassSucceed(
  response: PetPassCommonResponse<object, object> | PetPassErrorResponse,
): response is PetPassCommonResponse<object, object> {
  return response.statusCode === 200;
}
