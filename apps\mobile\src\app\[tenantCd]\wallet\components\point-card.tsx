"use client";

import { useQueryPointBalance } from "@/actions/point/point-query";
import { commaizeNumber } from "@toss/utils";
import { Skeleton } from "@workspace/ui/components/skeleton";
import { cn } from "@workspace/ui/lib/utils";
import Image from "next/image";
import { PointBalances } from "./point-balance";
import { PointMenus } from "./point-menus";

export const PointCard = ({
  tenantCd,
  className,
}: {
  tenantCd: string;
  className?: string;
}) => {
  const { data } = useQueryPointBalance();
  const totalBalance = data?.reduce((prev, item) => prev + item.balance, 0);

  return (
    <div
      className={cn("rounded-t-[16px] bg-white p-[20px] shadow-lg", className)}
    >
      <div className="mb-[20px] flex flex-row items-center">
        <Image
          src="/assets/wallet/point-coin.png"
          width={26}
          height={26}
          alt="포인트"
          className="mr-[8px]"
        />
        <span className="line-clamp-1 text-[18px] font-bold">
          CITYCUBE 포인트
        </span>
        <div className="flex flex-1 items-end justify-end text-[22px] font-medium">
          <span className="max-w-[150px] overflow-hidden whitespace-nowrap text-[26px] font-semibold">
            {totalBalance !== undefined ? (
              commaizeNumber(totalBalance)
            ) : (
              <Skeleton className="h-[24px] w-[80px] bg-white/20" />
            )}
          </span>
          <span className="ml-[4px]">P</span>
        </div>
      </div>
      <PointBalances tenantCd={tenantCd} data={data} />
      <PointMenus />
    </div>
  );
};
