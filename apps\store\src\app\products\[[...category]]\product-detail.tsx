import { fetchProductDetail } from "@/actions/product-action";
import { OrderButton } from "@/app/product/[id]/purchase-button";
import { BottomButton } from "@/components/common/bottom-button";
import { SummaryItem } from "@/components/common/summary";

export const ProductDetail = async ({
  id,
  sourceType,
  sourceId,
}: {
  id: number;
  sourceType: string;
  sourceId: string;
}) => {
  const productDetail = await fetchProductDetail(sourceType, sourceId);
  if (!productDetail.data) {
    return (
      <div className="flex h-[200px] items-center justify-center text-gray-500">
        <p className="text-sm">상품 정보 확인 중 오류가 발생하였습니다.</p>
      </div>
    );
  }
  const detail = productDetail.data;

  return (
    <div>
      <div className="mb-[16px] flex flex-col gap-[8px] border-b border-gray-200 py-[8px]">
        {detail.notes.map((note, index) => (
          <SummaryItem key={index} title={note.title}>
            {note.content}
          </SummaryItem>
        ))}
      </div>
      <div
        className="prose prose-sm mb-[160px] max-w-none text-[14px]"
        dangerouslySetInnerHTML={{ __html: detail.content }}
      />
      <OrderButton id={id} />
    </div>
  );
};
