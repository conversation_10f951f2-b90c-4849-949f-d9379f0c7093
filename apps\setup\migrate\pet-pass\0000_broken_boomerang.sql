CREATE TABLE "tb_pets_info" (
	"uid" integer PRIMARY KEY GENERATED BY DEFAULT AS IDENTITY (sequence name "tb_pets_info_uid_seq" INCREMENT BY 1 MINVALUE 1 MAXVALUE 2147483647 START WITH 1 CACHE 1),
	"user_did" varchar(255) NOT NULL,
	"qrp_idx" integer NOT NULL,
	"own_name" varchar(64) NOT NULL,
	"pet_reg_no" varchar(15) NOT NULL,
	"pet_name" varchar(64) NOT NULL,
	"pet_birth" varchar(64) NOT NULL,
	"pet_weight" varchar(64) NOT NULL,
	"pet_type" varchar(1) NOT NULL,
	"pet_breed" varchar(64) NOT NULL,
	"pet_breed_code" varchar(64) NOT NULL,
	"pet_breed_id" varchar(64) NOT NULL,
	"pet_is_fierce" boolean NOT NULL,
	"pet_hair_color_code" varchar(64) NOT NULL,
	"pet_gender" varchar(64) NOT NULL,
	"pet_neuter_yn" varchar(64) NOT NULL,
	"pet_vaccinate_yn" varchar(64) NOT NULL,
	"pet_rabies_virus_yn" varchar(64) NOT NULL,
	"pet_size_type" varchar(1) NOT NULL,
	"pet_office" varchar(64) NOT NULL,
	"relationship_own" integer NOT NULL,
	"val_yn" varchar(1) DEFAULT 'Y' NOT NULL,
	"reg_dt" timestamp DEFAULT now() NOT NULL,
	"upd_dt" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "tb_pets_owner" (
	"uid" integer PRIMARY KEY GENERATED BY DEFAULT AS IDENTITY (sequence name "tb_pets_owner_uid_seq" INCREMENT BY 1 MINVALUE 1 MAXVALUE 2147483647 START WITH 1 CACHE 1),
	"user_did" varchar(255) NOT NULL,
	"own_code" varchar(64) NOT NULL,
	"val_yn" varchar(1) DEFAULT 'Y' NOT NULL,
	"reg_dt" timestamp DEFAULT now() NOT NULL,
	"upd_dt" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "tb_pets_qr_entry" (
	"uid" integer PRIMARY KEY GENERATED BY DEFAULT AS IDENTITY (sequence name "tb_pets_qr_entry_uid_seq" INCREMENT BY 1 MINVALUE 1 MAXVALUE 2147483647 START WITH 1 CACHE 1),
	"user_did" varchar(255) NOT NULL,
	"qrp_idx" integer NOT NULL,
	"own_name" varchar(64) NOT NULL,
	"pet_name" varchar(64) NOT NULL,
	"accompanying_count" integer NOT NULL,
	"pet_size_type" varchar(1) NOT NULL,
	"qr_text" varchar(128) NOT NULL,
	"val_yn" varchar(1) DEFAULT 'Y' NOT NULL,
	"reg_dt" timestamp DEFAULT now() NOT NULL,
	"upd_dt" timestamp DEFAULT now() NOT NULL
);
