"use client";

import { useQueryPedometerRecordsRange } from "@/actions/pedometer/pedometer-query";
import { getWeekDayName } from "@workspace/utils/datetime";
import { subDays } from "date-fns";
import { PedometerChart } from "./pedometer-chart";

export const PedometerWeekly = () => {
  const endDt = new Date();
  const startDt = subDays(endDt, 6);
  const { data } = useQueryPedometerRecordsRange(startDt, endDt);

  const chartData: { key: string; steps: number }[] = data
    ? data.map((record) => {
        // yyyy-MM-dd to Date
        const date = new Date(record.date);
        return {
          key: getWeekDayName(date),
          steps: record.stepCnt,
        };
      })
    : [];

  return (
    <section className="flex flex-1 flex-col rounded-lg bg-white p-[16px] shadow-lg">
      <div className="text-[16px] font-semibold">최근 기록 (주간)</div>
      <div className="flex-1 items-center justify-center p-[16px]">
        <PedometerChart data={chartData} />
      </div>
    </section>
  );
};
