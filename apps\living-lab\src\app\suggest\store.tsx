import { createStore, StateCreator } from "zustand";
import { UploadedFile } from "./components/form";

type CommentEditModel = {
  commentUid: number;
  content: string;
  files: UploadedFile[];
};

type CommentFormSlice = {
  open: boolean;
  mode: "add" | "edit";
  suggestUid: number | null;
  parentUid: number | null;
  model: CommentEditModel | null;

  addComment: (suggestUid: number, parentUid: number | null) => void;
  editComment: (suggestUid: number, model: CommentEditModel) => void;
  setOpen: (visible: boolean) => void;
};

const createCommentFormSlice: StateCreator<CommentFormSlice> = (set) => ({
  open: false,
  mode: "add",
  suggestUid: null,
  parentUid: null,
  model: null,
  addComment: (suggestUid: number, parentUid: number | null) => {
    set({
      open: true,
      mode: "add",
      suggestUid,
      parentUid,
      model: { commentUid: 0, content: "", files: [] },
    });
  },
  editComment: (suggestUid: number, model: CommentEditModel) => {
    set({
      open: true,
      mode: "edit",
      suggestUid,
      model: model,
    });
  },
  setOpen: (open: boolean) => {
    set({ open });
  },
});

export type SuggestStore = CommentFormSlice;

export const createSuggestStore = () =>
  createStore<SuggestStore>()((...a) => ({
    ...createCommentFormSlice(...a),
  }));
