import {
  increateSuggestVote,
  readSuggestVote,
  readSuggestVoteByUser,
} from "@workspace/db/crud/living-lab/suggest";
import { getSession } from "@workspace/utils/auth/session";
import { NextRequest, NextResponse } from "next/server";

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ suggestUid: number }> },
) {
  const session = await getSession();
  if (!session) {
    return NextResponse.json(new Error("Unauthorized"), { status: 401 });
  }
  const { suggestUid } = await params;

  const vote = await readSuggestVoteByUser(
    session.tenantCd,
    session.userDid,
    suggestUid,
  );

  return NextResponse.json({ vote });
}

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ suggestUid: number }> },
) {
  const session = await getSession();
  if (!session) {
    return NextResponse.json(new Error("Unauthorized"), { status: 401 });
  }

  const { suggestUid } = await params;
  const { vote } = await request.json();

  await increateSuggestVote(
    session.tenantCd,
    session.userDid,
    suggestUid,
    vote === 0 ? 0 : vote < 1 ? -1 : 1,
  );
  const { up, down } = await readSuggestVote(session.tenantCd, suggestUid);

  return NextResponse.json({ vote, up, down });
}
