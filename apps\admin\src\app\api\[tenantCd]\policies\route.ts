import {
  getPolicyList,
  readPolicyCount,
} from "@workspace/db/crud/common/points";
import { NextRequest, NextResponse } from "next/server";

export async function GET(
  request: NextRequest,
  { params }: { params: { tenantCd: string } }
) {
  try {
    const { tenantCd } = await params;
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get("page") || "1", 10);
    const pageSize = parseInt(searchParams.get("pageSize") || "10", 10);

    const [policies, count] = await Promise.all([
      getPolicyList(tenantCd, page, pageSize),
      readPolicyCount(tenantCd),
    ]);

    return NextResponse.json({
      data: policies,
      count,
      page,
      pageSize,
    });
  } catch (error) {
    console.error("Failed to fetch policies:", error);
    return NextResponse.json(
      { error: "Failed to fetch policies" },
      { status: 500 }
    );
  }
}
