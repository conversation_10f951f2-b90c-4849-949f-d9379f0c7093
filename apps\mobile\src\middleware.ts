import { authMiddleware } from "@workspace/utils/auth/middleware";
import { headers } from "next/headers";
import { NextRequest } from "next/server";

export async function middleware(request: NextRequest) {
  return authMiddleware(request);
}

export const getBaseURL = async () => {
  const headerList = await headers();
  return headerList.get("x-base-url") as string;
};

export const config = {
  matcher: ["/((?!api|_next/static|_next/image|favicon.ico|assets).*)"],
};
