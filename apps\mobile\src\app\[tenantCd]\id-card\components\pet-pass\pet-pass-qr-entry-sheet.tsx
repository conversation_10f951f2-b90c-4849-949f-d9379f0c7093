"use client";

import { <PERSON>B<PERSON>on, SecondaryButton } from "@/components/button";
import { PetPassQrEntry } from "@workspace/db/crud/pet-pass/pet-pass";
import { SheetContent, SheetTitle } from "@workspace/ui/components/sheet";
import Image from "next/image";
import { useMemo, useState } from "react";
import QRCode from "react-qr-code";
import { PetPassCarousel } from "./pet-pass-carousel";

const PetQrEntryItem = ({ data }: { data: PetPassQrEntry }) => {
  return (
    <div>
      <div className="flex flex-row py-[10px]">
        <Image
          src={"/assets/id-card/pet-pass/profile.png"}
          width={66}
          height={66}
          alt="프로필 사진"
        />
        <div className="flex flex-1 flex-col justify-center pl-[20px]">
          <div className="text-[18px] font-semibold">{data.petName}</div>
          <div className="text-[14px] text-[#808C98]">
            보호자 {data.ownName}
          </div>
        </div>
      </div>
      <div className="flex items-center justify-center py-[30px]">
        <QRCode value={data.qrText} size={173} />
      </div>
      <div className="mt-[20px] grid grid-cols-2 gap-x-[20px] gap-y-[10px]">
        <div>
          <div className="text-[14px] text-[#808c98]">동반인원수</div>
          <div className="text-[16px] font-medium text-[#000]">
            {data.accompanyingCount}명
          </div>
        </div>
        <div>
          <div className="text-[14px] text-[#808c98]">반려동물크기</div>
          <div className="text-[16px] font-medium text-[#000]">
            {data.petSizeType === "S" ? "소형" : "여자아이"}
          </div>
        </div>
      </div>
    </div>
  );
};

export const PetQrEntrySheet = ({
  data,
  onModify,
  onClose,
}: {
  data: PetPassQrEntry[];
  onModify: () => void;
  onClose: () => void;
}) => {
  const [count, setCount] = useState<[number, number]>([0, 0]);
  const handleCarouselValue = (index: number, maxIndex: number) => {
    setCount([index, maxIndex]);
  };

  const children = useMemo(
    () => [
      ...data.map((item, index) => <PetQrEntryItem key={index} data={item} />),
    ],
    [data],
  );

  return (
    <SheetContent side="bottom" className="rounded-t-[16px]">
      <SheetTitle className="text-[20px] font-medium">
        반려동물 QR 신분증
      </SheetTitle>
      <PetPassCarousel onSelectSnap={handleCarouselValue}>
        {children}
      </PetPassCarousel>
      <div className="flex w-full items-center justify-center pt-[20px]">
        <div className="text-[14px] text-[#808C98]">
          {count[0] + 1} / {count[1]}
        </div>
      </div>
      <div className="flex w-full items-center justify-center gap-[10px] pt-[20px]">
        <SecondaryButton onClick={onModify}>출입정보 수정</SecondaryButton>
        <PrimaryButton onClick={onClose}>확인</PrimaryButton>
      </div>
    </SheetContent>
  );
};
