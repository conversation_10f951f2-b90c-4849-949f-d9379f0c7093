{"name": "admin", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack --port 3000", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@hookform/resolvers": "catalog:", "@tanstack/react-query": "^5.59.19", "@tanstack/react-query-devtools": "^5.59.20", "@tanstack/react-table": "^8.20.6", "@toss/react": "^1.8.1", "@toss/utils": "^1.6.1", "@workspace/db": "workspace:*", "@workspace/ui": "workspace:*", "@workspace/utils": "workspace:*", "bcrypt-ts": "^7.1.0", "class-variance-authority": "^0.7.0", "date-fns": "^4.1.0", "dayjs": "^1.11.13", "iconoir-react": "^7.10.1", "jose": "^5.9.6", "lucide-react": "^0.454.0", "next": "15.3.3", "react": "catalog:react19", "react-dom": "catalog:react19", "react-hook-form": "catalog:", "recharts": "^2.15.1", "zod": "catalog:", "zustand": "^5.0.1"}, "devDependencies": {"@types/node": "^20", "@types/react": "catalog:react19", "@types/react-dom": "catalog:react19", "@workspace/eslint-config": "workspace:*", "@workspace/typescript-config": "workspace:*", "autoprefixer": "^10.4.20", "eslint": "catalog:", "postcss": "catalog:", "tailwindcss": "catalog:", "typescript": "catalog:"}}