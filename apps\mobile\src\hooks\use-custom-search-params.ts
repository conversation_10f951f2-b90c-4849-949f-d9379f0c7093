"use client";

import { usePathname, useRouter, useSearchParams } from "next/navigation";

type ParamsType = { [key: string]: string };

export const useCustomSearchParams = (): [
  URLSearchParams,
  (newParams: ParamsType, replace?: boolean) => void,
] => {
  const router = useRouter();
  const pathname = usePathname();
  const currentSearchParams = useSearchParams();

  const setNewParams = (newParams: ParamsType) => {
    const searchParams = new URLSearchParams(currentSearchParams);
    for (const [key, value] of Object.entries(newParams)) {
      if (value) searchParams.set(key, value);
      else searchParams.delete(key);
    }
    return searchParams.toString();
  };

  const setSearchParams = (newParams: ParamsType, replace: boolean = false) => {
    if (replace) {
      router.replace(`${pathname}?${setNewParams(newParams)}`);
    } else {
      return router.push(`${pathname}?${setNewParams(newParams)}`);
    }
  };

  return [currentSearchParams, setSearchParams];
};
