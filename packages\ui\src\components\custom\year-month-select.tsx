import { cn } from "@workspace/ui/lib/utils";
import { SimpleSelect } from "./simple-select";

const YearPicker = ({
  value,
  onChange,
}: {
  value: string;
  onChange: (newValue: string) => void;
  yearFrom: number;
}) => {
  const years = Array.from({ length: 5 }, (_, i) => {
    const year = new Date().getFullYear() - i;
    return { name: `${year}년`, value: year.toString() };
  });

  return (
    <SimpleSelect
      value={value}
      defaultValue={value}
      options={years}
      onChange={onChange}
    />
  );
};

const MonthPicker = ({
  value,
  onChange,
}: {
  value: string;
  onChange: (newValue: string) => void;
}) => {
  const months = Array.from({ length: 12 }, (_, i) => {
    const month = i + 1;
    return { name: `${month}월`, value: month.toString().padStart(2, "0") };
  });

  return (
    <SimpleSelect
      value={value}
      defaultValue={value}
      options={months}
      onChange={onChange}
    />
  );
};

export type YearMonthType = {
  year: string;
  month: string;
};

export const YearMonthPicker = ({
  value,
  yearFrom,
  onChange,
  className,
}: {
  value: YearMonthType;
  yearFrom: number;
  onChange: (newValue: YearMonthType) => void;
  className?: string;
}) => {
  return (
    <div className={cn("flex space-x-[10px]", className)}>
      <YearPicker
        value={value.year}
        yearFrom={yearFrom}
        onChange={(newValue) => onChange({ ...value, year: newValue })}
      />
      <MonthPicker
        value={value.month}
        onChange={(newValue) => onChange({ ...value, month: newValue })}
      />
    </div>
  );
};
