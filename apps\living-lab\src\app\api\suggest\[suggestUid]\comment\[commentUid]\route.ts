import { savePendingFiles } from "@/app/api/common/form";
import {
  readSuggestCommentById,
  readSuggestComments,
  updateSuggestComment,
} from "@workspace/db/crud/living-lab/suggest";
import { getSession } from "@workspace/utils/auth/session";
import { NextRequest, NextResponse } from "next/server";

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ suggestUid: number; commentUid: number }> },
) {
  const session = await getSession();
  if (!session) {
    return NextResponse.json(new Error("Unauthorized"), { status: 401 });
  }
  const { suggestUid, commentUid } = await params;

  const result = await readSuggestComments(
    session.tenantCd,
    suggestUid,
    commentUid,
  );
  return NextResponse.json(result);
}

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ suggestUid: number; commentUid: number }> },
) {
  const session = await getSession();
  if (!session) {
    return NextResponse.json(new Error("Unauthorized"), { status: 401 });
  }
  const { suggestUid, commentUid } = await params;
  const currentData = await readSuggestCommentById(
    session.tenantCd,
    commentUid,
  );
  if (!currentData) {
    return NextResponse.json(new Error("Not Found"), { status: 404 });
  }

  // TODO: PERMISSION CHECK
  if (currentData.userDid !== session.userDid) {
    return NextResponse.json(new Error("Forbidden"), { status: 403 });
  }

  try {
    const formData = await request.formData();
    const content = formData.get("content")?.toString();
    const activeFiles = formData
      .getAll("activeFiles")
      .map((item) => ({ uid: Number(item.toString()) }));
    const pendingFiles = formData.getAll("pendingFiles");

    if (!content) {
      return NextResponse.json({ error: "내용 오류" }, { status: 400 });
    }
    const savedFiles = await savePendingFiles(pendingFiles);
    const result = await updateSuggestComment(
      session.tenantCd,
      commentUid,
      {
        suggestUid: suggestUid,
        userDid: session.userDid,
        name: session.userNm,
        content,
      },
      savedFiles,
      activeFiles,
    );
    return NextResponse.json({ commentUid: result });
  } catch (error) {
    const errorMessage =
      error instanceof Error ? error.message : "Unknown error";
    return NextResponse.json({ error: errorMessage }, { status: 500 });
  }
}
