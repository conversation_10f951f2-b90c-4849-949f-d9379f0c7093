import {
  readTermsDatas,
  TermsSearchFilter,
} from "@workspace/db/crud/common/terms";
import { AppContent } from "../../components/container";
import { TermsList } from "./components/terms-list";
import { TermsSearchForm } from "./components/terms-search-form";

const parseSearchParams = (search: Record<string, any>): TermsSearchFilter => {
  if (!search) return {};
  const getString = (
    value: string | string[] | undefined,
  ): string | undefined => {
    if (Array.isArray(value)) return value[0];
    return value;
  };

  return {
    searchType: getString(search.searchType),
    keyword: getString(search.keyword),
    valYn: getString(search.valYn),
    mustYn: getString(search.mustYn),
    regDt: [
      search.regDtFrom ? new Date(search.regDtFrom) : undefined,
      search.regDtTo ? new Date(search.regDtTo) : undefined,
    ],
    chgDt: [
      search.chgDtFrom ? new Date(search.chgDtFrom) : undefined,
      search.chgDtTo ? new Date(search.chgDtTo) : undefined,
    ],
  };
};

const TermsManageContent = async ({
  tenantCd,
  searchParams,
}: {
  tenantCd: string;
  searchParams: TermsSearchFilter;
}) => {
  const terms = await readTermsDatas(tenantCd, searchParams);
  return <TermsList tenantCd={tenantCd} data={terms} />;
};

export default async function BoardTermsPage({
  params,
  searchParams,
}: Readonly<{
  params: Promise<{ tenantCd: string }>;
  searchParams: Promise<{ [key: string]: string | string[] | undefined }>;
}>) {
  const { tenantCd } = await params;
  const initialParams = parseSearchParams(await searchParams);
  return (
    <AppContent title="약관 및 정책">
      <TermsSearchForm initialParams={initialParams} />
      <TermsManageContent tenantCd={tenantCd} searchParams={initialParams} />
    </AppContent>
  );
}
