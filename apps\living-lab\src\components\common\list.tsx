import { icons } from "@/assets/icons";
import Image from "next/image";
import { ReactNode } from "react";

export const EmptyList = ({ children }: { children: ReactNode }) => {
  return (
    <div className="mt-[60px] flex h-[120px] flex-col items-center justify-center">
      <Image src={icons.alert} className="mb-[10px] h-[30px] w-[30px]" alt="" />
      <div className="text-center text-[14px] font-medium text-[#808C98]">
        {children}
      </div>
    </div>
  );
};
