"use client";

import { Button } from "@workspace/ui/components/button";
import { cn } from "@workspace/ui/lib/utils";
import { useRouter, useSearchParams } from "next/navigation";

export type SortOrderItem<T> = {
  title: string;
  value: T;
};

export const SortOrder = <T,>({
  items,
  value,
  keyName,
}: {
  items: SortOrderItem<T>[];
  value: T;
  keyName: string;
}) => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const onChange = (newValue: T) => {
    const params = new URLSearchParams(searchParams.toString());
    if (newValue) {
      params.set(keyName, newValue.toString());
    } else {
      params.delete(keyName);
    }
    router.replace(`?${params.toString()}`);
  };

  return (
    <div className="mb-3 flex">
      {items.map((item, index) => (
        <Button
          key={index}
          variant="outline"
          onClick={() => onChange(item.value)}
          className={cn(
            "h-10 flex-1 rounded-none border text-sm",
            index === 0 && "rounded-l-md",
            index === items.length - 1 && "rounded-r-md",
            value === item.value
              ? "border-[#256bff] bg-[#ecf2fd] text-[#256bff]"
              : "border-[#d6d6d6] text-[#5c5c5c]",
          )}
        >
          {item.title}
        </Button>
      ))}
    </div>
  );
};
