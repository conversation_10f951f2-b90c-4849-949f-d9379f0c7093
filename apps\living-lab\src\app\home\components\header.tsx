"use client";

import { icons } from "@/assets/icons";
import Header from "@/components/common/header";
import Image from "next/image";
import Link from "next/link";

export const HomeHeader = () => {
  return (
    <Header
      title="리빙랩"
      className="bg-[#FAFBFF]"
      rightSection={
        <div className="flex items-center space-x-[6px]">
          <Link className="flex items-center" href="/notice">
            <Image
              src={icons.notice}
              className="h-[30px] w-[30px]"
              alt="알림"
            />
          </Link>
          <Link className="flex items-center" href="/help">
            <Image
              src={icons.help}
              className="h-[30px] w-[30px]"
              alt="도움말"
            />
          </Link>
        </div>
      }
      onBackPress={() => {
        window.close();
      }}
    />
  );
};
