import { useQuery } from "@tanstack/react-query";
import { format } from "date-fns";
import {
  fetchPedometerRecordsMonthly,
  fetchPedometerRecordsRange,
} from "./pedometer-action";

const pedometerKeys = {
  all: ["pedometerKeys"] as const,
  pedometerRange: (startDt: string, endDt: string) =>
    [...pedometerKeys.all, "pedometerRange", { startDt, endDt }] as const,
  pedometerMonthly: (startDt: string, endDt: string) =>
    [...pedometerKeys.all, "pedometerMonthly", { startDt, endDt }] as const,
};

export const useQueryPedometerRecordsRange = (
  startDate: Date,
  endDate: Date,
) => {
  const startDt = format(startDate, "yyyy-MM-dd");
  const endDt = format(endDate, "yyyy-MM-dd");

  return useQuery({
    queryKey: pedometerKeys.pedometerRange(startDt, endDt),
    queryFn: async () => {
      return await fetchPedometerRecordsRange({ startDt, endDt });
    },
  });
};

export const useQueryPedometerRecordsMonthly = (
  startDate: Date,
  endDate: Date,
) => {
  const startDt = format(startDate, "yyyy-MM-dd");
  const endDt = format(endDate, "yyyy-MM-dd");

  return useQuery({
    queryKey: pedometerKeys.pedometerMonthly(startDt, endDt),
    queryFn: async () => {
      return await fetchPedometerRecordsMonthly({ startDt, endDt });
    },
  });
};
