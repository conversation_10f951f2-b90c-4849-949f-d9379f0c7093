"use client";

import { MAIN_TENANT_CD } from "@/lib/tenant";
import {
  ColumnDef,
  getCoreRowModel,
  useReactTable,
} from "@tanstack/react-table";
import { Masker } from "@toss/utils";
import { User, UserAndVc } from "@workspace/db/crud/common/user";
import { Button } from "@workspace/ui/components/button";
import { Checkbox } from "@workspace/ui/components/checkbox";
import { DataTable } from "@workspace/ui/components/data-table";
import { format } from "date-fns";
import Link from "next/link";
import { useMemo } from "react";

const getColumnDefs = (tenantCd: string) => {
  const columnDefs: ColumnDef<UserAndVc>[] = [
    {
      id: "select",
      header: ({ table }) => (
        <Checkbox
          checked={
            table.getIsAllPageRowsSelected() ||
            (table.getIsSomePageRowsSelected() && "indeterminate")
          }
          onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
          aria-label="Select all"
        />
      ),
      cell: ({ row }) => (
        <Checkbox
          checked={row.getIsSelected()}
          onCheckedChange={(value) => row.toggleSelected(!!value)}
          aria-label="Select row"
        />
      ),
      enableSorting: false,
      enableHiding: false,
    },
    {
      accessorKey: "user.userDid",
      header: "DID",
      cell: ({ cell, row }) => (
        <Button variant="link" asChild>
          <Link href={`/${tenantCd}/users/manage/${row.original.user.userDid}`}>
            {cell.renderValue<string>().slice(-8)}
          </Link>
        </Button>
      ),
    },
    {
      accessorKey: "user.userNm",
      header: "사용자명",
      cell: ({ getValue }) => {
        const value = getValue() as string;
        return Masker.maskName(value);
      },
    },
    {
      accessorKey: "user.telnoEnc",
      header: "연락처",
      cell: ({ getValue }) => {
        const value = getValue() as string;
        return Masker.maskPhoneNumber(value);
      },
    },
    {
      accessorKey: "user.brdt",
      header: "생년월일",
    },
    {
      accessorKey: "user.gender",
      header: "성별",
      cell: ({ getValue }) => {
        const value = getValue();
        return value === "M" ? "남" : value === "F" ? "여" : value;
      },
    },
    {
      accessorKey: "user.joinDt",
      header: "가입일시",
      cell: ({ getValue }) => {
        const value = getValue() as Date;
        return format(value, "yyyy.MM.dd HH:mm");
      },
    },
    {
      accessorKey: "user.lastLgnDt",
      header: "최근로그인",
      cell: ({ getValue }) => {
        const value = getValue() as Date | undefined;
        return value ? format(value, "yyyy.MM.dd HH:mm") : "";
      },
    },
    {
      accessorKey: "user.mblOt",
      header: "사용기기",
      cell: ({ getValue }) => {
        const value = getValue() as string | undefined;
        return value ? (value === "android" ? "Android" : "iOS") : "";
      },
    },
  ];

  if (tenantCd !== MAIN_TENANT_CD) {
    const vcColumnDefs: ColumnDef<UserAndVc>[] = [
      {
        accessorKey: "vc.dscdVal",
        header: "시민인증",
        cell: ({ getValue }) => {
          const value = getValue() as boolean | undefined;
          return value === false ? "O" : "";
        },
      },
      {
        accessorKey: "vc.issuedDt",
        header: "인증일",
        cell: ({ getValue }) => {
          const value = getValue() as Date | undefined;
          return value ? format(value, "yyyy.MM.dd") : "";
        },
      },
    ];
    columnDefs.push(...vcColumnDefs);
  }

  return columnDefs;
};

export const UserTable = ({
  tenantCd,
  data,
}: {
  tenantCd: string;
  data: UserAndVc[];
}) => {
  const columns: ColumnDef<UserAndVc>[] = useMemo(
    () => getColumnDefs(tenantCd),
    [tenantCd],
  );

  const table = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
  });

  return (
    <div className="rounded-md border">
      <DataTable table={table} />
    </div>
  );
};
