import { icons } from "@/assets/icons";
import Image from "next/image";
import { ReactNode } from "react";

export const WriteNotice = ({ children }: { children: ReactNode }) => {
  return (
    <div className="m-[10px]">
      <div className="mb-[10px] flex items-center space-x-[2px]">
        <Image
          src={icons.alert}
          className="h-[20px] w-[20px] stroke-[#373E44]"
          alt=""
        />
        <span className="text-[16px] font-medium text-[#373E44]">
          작성 유의사항
        </span>
      </div>
      <p className="text-sm leading-[20px] text-[#636F7A]">{children}</p>
    </div>
  );
};
