import { sql } from "drizzle-orm";
import {
  bigint,
  integer,
  pgTable,
  smallint,
  timestamp,
  varchar,
} from "drizzle-orm/pg-core";

export const orders = pgTable("tb_store_order", {
  id: bigint("id", { mode: "number" })
    .primaryKey()
    .generatedByDefaultAsIdentity(),
  userDid: varchar("user_did", { length: 255 }).notNull(),
  status: smallint("status").notNull(),
  title: varchar("title", { length: 64 }).default("").notNull(),
  price: integer("price").notNull(),
  time: timestamp("time", { mode: "string" })
    .default(sql`NOW()`)
    .notNull(),
  receiverName: varchar("receiverName", { length: 32 }).notNull(),
  receiverPhone: varchar("receiverPhone", { length: 32 }).notNull(),
  receiverAddress: varchar("receiverAddress", { length: 100 }),
});

export const orderDetails = pgTable("tb_store_order_detail", {
  id: bigint("id", { mode: "number" })
    .primaryKey()
    .generatedByDefaultAsIdentity(),
  orderId: bigint("order_id", { mode: "number" }).notNull(),
  productId: bigint("product_id", { mode: "number" }).notNull(),
  price: integer("price").notNull(),
  count: integer("count").default(1).notNull(),
});

export const payments = pgTable("tb_store_payment", {
  id: bigint("id", { mode: "number" })
    .primaryKey()
    .generatedByDefaultAsIdentity(),
  userDid: varchar("user_did", { length: 255 }).notNull(),
  orderId: bigint("order_id", { mode: "number" }).notNull(),
  amount: integer("amount").notNull(),
  time: timestamp("time")
    .default(sql`NOW()`)
    .notNull(),
  status: smallint("status").notNull(),
});

export const deliveries = pgTable("tb_store_delivery", {
  id: bigint("id", { mode: "number" })
    .primaryKey()
    .generatedByDefaultAsIdentity(),
  orderDetailId: bigint("order_detail_id", { mode: "number" }).notNull(),
  time: timestamp("time")
    .default(sql`NOW()`)
    .notNull(),
  count: integer("count").default(1).notNull(),
  deliveryService: varchar("delivery_service", { length: 32 }), // e.g. "giftishow"
  transactionId: varchar("transaction_id", { length: 64 }),
  trackingCode: varchar("tracking_code", { length: 64 }),
  status: smallint("status").notNull(),
});
