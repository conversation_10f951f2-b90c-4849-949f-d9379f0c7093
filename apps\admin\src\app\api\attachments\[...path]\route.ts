import { createReadStream, existsSync } from "fs";
import { join } from "path";
import { getSession } from "@/lib/session";
import { NextRequest, NextResponse } from "next/server";

export async function GET(
  _request: NextRequest,
  { params }: { params: Promise<{ path: string }> },
) {
  const session = await getSession();
  const { path } = await params;
  if (!session) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  const fullPath = join(process.env.ATTACHMENT_PATH!, ...path);
  console.log(fullPath);
  console.log("AA", process.env.ATTACHMENT_PATH!);
  if (!fullPath.startsWith(process.env.ATTACHMENT_PATH!)) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }
  if (existsSync(fullPath)) {
    try {
      const fileStream = createReadStream(fullPath);
      return new NextResponse(fileStream as unknown as ReadableStream);
    } catch (e) {
      console.error(e);
    }
  }
  return NextResponse.json({ error: "Not Found" }, { status: 404 });
}
