"use client";

import { But<PERSON> } from "@workspace/ui/components/button";
import { Card } from "@workspace/ui/components/card";
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@workspace/ui/components/form";
import { Input } from "@workspace/ui/components/input";
import { Label } from "@workspace/ui/components/label";
import { Textarea } from "@workspace/ui/components/textarea";
import { ChevronDown, ChevronUp, Plus, Trash2 } from "lucide-react";
import { useState } from "react";
import { Control, useFieldArray } from "react-hook-form";
import { z } from "zod";
import { formSchema } from "./terms-form";

type FormValues = z.infer<typeof formSchema>;

interface TermsSummaryFormProps {
  control: Control<FormValues>;
}

export const TermsSummaryForm = ({ control }: TermsSummaryFormProps) => {
  const { fields, append, remove } = useFieldArray({
    control,
    name: "termsSmry",
  });

  const addTermsSmryItem = () => {
    const newItem = {
      seqNum: fields.length + 1,
      title: "",
      content: [""],
    };
    append(newItem);
  };

  return (
    <div className="mt-6">
      <div className="mb-4 flex items-center justify-between">
        <Label className="text-base font-medium">약관 요약</Label>
        <Button
          type="button"
          variant="outline"
          size="sm"
          onClick={addTermsSmryItem}
          className="flex items-center gap-1"
        >
          <Plus className="h-4 w-4" />
          요약 항목 추가
        </Button>
      </div>

      <div className="space-y-4">
        {fields.map((field, index) => (
          <Card key={field.id} className="p-4">
            <div className="flex items-start gap-4">
              <div className="flex-1 space-y-3">
                <FormField
                  control={control}
                  name={`termsSmry.${index}.title`}
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>제목</FormLabel>
                      <FormControl>
                        <Input {...field} placeholder="예: 핵심" />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={control}
                  name={`termsSmry.${index}.content`}
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>내용</FormLabel>
                      <FormControl>
                        <Textarea
                          value={field.value.join("\n")}
                          onChange={(e) => {
                            const lines = e.target.value.split("\n");
                            field.onChange(lines);
                          }}
                          placeholder="약관 요약 내용을 입력해주세요."
                          rows={3}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={() => remove(index)}
                className="mt-8"
              >
                <Trash2 className="h-4 w-4" />
              </Button>
            </div>
          </Card>
        ))}
      </div>
    </div>
  );
};
