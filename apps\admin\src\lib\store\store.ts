import { createStore } from "zustand";
import { SessionPayload } from "../session";

type GlobalState = {
  session: SessionPayload | null;
};

type GlobalActions = {
  setSession: (session: SessionPayload | null) => void;
};

export type GlobalStore = GlobalState & GlobalActions;

const initialState: GlobalState = {
  session: null,
};

export const createGlobalStore = () =>
  createStore<GlobalStore>((set) => ({
    ...initialState,
    setSession: (session) => set({ session }),
  }));
