import { Menu, MenuItem } from "@/consts/menu-items";
import { But<PERSON> } from "@workspace/ui/components/button";
import Image from "next/image";
import { ListTitle } from "./common";

const FeatureItem = ({ title, imageUrl, description, onPress }: MenuItem) => {
  return (
    <Button asChild variant="none" onClick={onPress}>
      <div className="flex flex-row justify-between gap-[20px] rounded-[10px] bg-white py-[10px] transition-transform active:scale-[0.97]">
        <div className="flex items-center justify-center pl-[18px]">
          <Image src={imageUrl} width={40} height={40} alt={title} />
        </div>
        <div className="flex-1">
          <div className="mb-[8px] flex items-center justify-between">
            <span className="text-[16px] font-semibold">{title}</span>
          </div>
          <div className="line-clamp-2 flex-1 place-items-start text-ellipsis text-[12px] text-[#636F7A]">
            {description}
          </div>
        </div>
        <div className="flex items-center justify-center pr-[10px]">
          <Image
            src="/assets/menu/small-page-arrow-right.svg"
            width={18}
            height={18}
            alt=""
          />
        </div>
      </div>
    </Button>
  );
};
export const FeatureList = ({ data }: { data: Menu | null }) => {
  if (!data) return null;
  return (
    <div className="mt-[20px]">
      <ListTitle>{data?.title}</ListTitle>
      <div className="flex flex-col gap-[12px]">
        {data?.items.map((item, index) => (
          <FeatureItem
            key={index}
            title={item.title}
            imageUrl={item.imageUrl}
            description={item.description}
            onPress={item.onPress}
          />
        ))}
      </div>
    </div>
  );
};
