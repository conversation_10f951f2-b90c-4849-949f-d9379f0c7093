"use client";

import { Button } from "@workspace/ui/components/button";
import { useRouter, useSearchParams } from "next/navigation";

export type ViewTypeItem<T> = {
  url: string;
  urlSelected: string;
  value: T;
};

export const ViewType = <T,>({
  items,
  value,
  onChange,
}: {
  items: ViewTypeItem<T>[];
  value: T;
  onChange?: (newValue: T) => void;
}) => {
  return (
    <div className="flex gap-2">
      {items.map((item, index) => (
        <Button
          variant="ghost"
          size="sm"
          key={index}
          onClick={() => onChange?.(item.value)}
        >
          <img
            src={value === item.value ? item.urlSelected : item.url}
            alt=""
          />
        </Button>
      ))}
    </div>
  );
};
