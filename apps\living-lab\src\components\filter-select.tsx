import {
  SelectType,
  SimpleSelect,
} from "@workspace/ui/components/custom/simple-select";
import { useMemo } from "react";

const ALL_ITEMS_VALUE = "-";

export const FilterSelect = ({
  category,
  categories,
  showYears,
  year,
  selectableYears,
  count,
  handleCategoryChange,
  handleYearChange,
}: {
  category: string | undefined;
  categories: SelectType[];
  showYears: boolean;
  year: number;
  selectableYears: number[];
  count: number;
  handleCategoryChange: (value: string | undefined) => void;
  handleYearChange: (value: number) => void;
}) => {
  const categoriesData = useMemo(
    () => [
      { name: "전체", value: ALL_ITEMS_VALUE },
      ...categories.map(({ name, value }) => ({ name, value })),
    ],
    [categories],
  );

  const years = useMemo(
    () =>
      selectableYears.map((year) => ({
        name: `${year}년`,
        value: year.toString(),
      })),
    [selectableYears],
  );

  const onCategoryChange = (value: string) => {
    handleCategoryChange(value === ALL_ITEMS_VALUE ? undefined : value);
  };
  const onYearChange = (value: string) => {
    handleYearChange(parseInt(value));
  };

  return (
    <div className="mx-[20px] flex flex-row items-center justify-between space-x-[10px] border-b-[1px] border-[#F5F5F5] py-[20px] text-[14px]">
      <SimpleSelect
        value={category}
        defaultValue={ALL_ITEMS_VALUE}
        options={categoriesData}
        onChange={onCategoryChange}
      />
      {showYears && (
        <SimpleSelect
          value={year.toString()}
          defaultValue={ALL_ITEMS_VALUE}
          options={years}
          onChange={onYearChange}
        />
      )}
      <div className="whitespace-nowrap text-[12px]">
        {count} <span className="text-[#808C98]">건</span>
      </div>
    </div>
  );
};
