export type ProductDetail = {
  content: string;
  isValid: boolean;
  notes: { title: string; content: string }[];
};

export interface ProductApiService {
  readonly sourceType: string;
  readonly deliver: string;
  readonly deliverMethod: string;

  syncProducts: () => Promise<void>;
  readProductDetail: (sourceId: string) => Promise<ProductDetail>;
  readStockAvailability: (
    sourceId: string,
    price: number,
    pendingOrdersTotal: number,
  ) => Promise<boolean>;
}
