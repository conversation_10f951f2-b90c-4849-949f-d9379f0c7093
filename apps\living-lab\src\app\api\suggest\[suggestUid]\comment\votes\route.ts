import { readSuggestCommentVoteIdsByUser } from "@workspace/db/crud/living-lab/suggest";
import { getSession } from "@workspace/utils/auth/session";
import { NextRequest, NextResponse } from "next/server";

export async function GET(
  _request: NextRequest,
  {
    params,
  }: {
    params: Promise<{
      uid: number;
      commentUid: number;
    }>;
  },
) {
  const session = await getSession();
  if (!session) {
    return NextResponse.json(new Error("Unauthorized"), { status: 401 });
  }
  const { uid } = await params;

  const result = await readSuggestCommentVoteIdsByUser(
    session.tenantCd,
    session.userDid,
    uid,
  );
  return NextResponse.json(result);
}
