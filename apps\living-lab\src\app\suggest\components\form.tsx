import { icons } from "@/assets/icons";
import { useGlobalStore } from "@/lib/store/store-provider";
import { Button } from "@workspace/ui/components/button";
import {
  compressImage,
  getFileHash,
  getImageURL,
  getRandomID,
} from "@workspace/utils/files";
import { X } from "lucide-react";
import Image from "next/image";
import { Dispatch, SetStateAction, useRef } from "react";
import { useShallow } from "zustand/shallow";

type UploadFileType = "camera" | "gallery";

export interface UploadedFile {
  uid: number;
  uri: string;
  originalName: string;
}

interface PendingFile {
  id: string;
  file: File;
  type: UploadFileType;
  preview: string;
}

export type FormFiles = {
  activeFiles: UploadedFile[];
  pendingFiles: PendingFile[];
};

export const UploadedFilesView = ({
  files,
  onFilesChange,
}: {
  files: UploadedFile[];
  onFilesChange: Dispatch<SetStateAction<UploadedFile[]>>;
}) => {
  const baseURL = useGlobalStore(useShallow((state) => state.baseURL));

  return (
    <>
      {files.length > 0 && (
        <>
          {files.map((file) => (
            <div key={file.uid} className="relative">
              {
                // eslint-disable-next-line @next/next/no-img-element
                <img
                  src={getImageURL(baseURL, file.uri)}
                  alt={file.originalName}
                  className="h-20 w-20 object-cover"
                />
              }
              <button
                type="button"
                className="absolute -right-2 -top-1 flex h-5 w-5 items-center justify-center rounded-full bg-red-500 text-white"
                onClick={() => {
                  onFilesChange((prev) =>
                    prev.filter((f) => f.uid !== file.uid),
                  );
                }}
              >
                <X className="h-[16px] w-[16px]" />
              </button>
            </div>
          ))}
        </>
      )}
    </>
  );
};

const FileItem = ({
  src,
  alt,
  onPressDelete,
}: {
  src: string;
  alt: string;
  onPressDelete: () => void;
}) => {
  return (
    <div className="relative">
      {
        // eslint-disable-next-line @next/next/no-img-element
        <img
          src={src}
          alt={alt}
          className="h-20 w-20 border-[1px] object-cover"
        />
      }
      <button
        type="button"
        className="absolute -right-2 -top-1 flex h-5 w-5 items-center justify-center rounded-full bg-red-500 text-white"
        onClick={onPressDelete}
      >
        <X className="h-[16px] w-[16px]" />
      </button>
    </div>
  );
};

export const UploadFilesView = ({
  files,
  onFilesChange,
}: {
  files: FormFiles;
  onFilesChange: Dispatch<SetStateAction<FormFiles>>;
}) => {
  const { activeFiles, pendingFiles } = files;
  const baseURL = useGlobalStore(useShallow((state) => state.baseURL));
  return (
    <div className="flex flex-wrap space-x-2">
      {activeFiles.length > 0 &&
        activeFiles.map((file) => (
          <FileItem
            key={file.uid}
            src={getImageURL(baseURL, file.uri)}
            alt={file.originalName}
            onPressDelete={() => {
              onFilesChange((prev) => ({
                ...prev,
                activeFiles: prev.activeFiles.filter((f) => f.uid !== file.uid),
              }));
            }}
          />
        ))}
      {pendingFiles.length > 0 &&
        pendingFiles.map((file) => (
          <FileItem
            key={file.id}
            src={file.preview}
            alt={file.file.name}
            onPressDelete={() => {
              onFilesChange((prev) => ({
                ...prev,
                pendingFiles: prev.pendingFiles.filter((f) => f.id !== file.id),
              }));
            }}
          />
        ))}
    </div>
  );
};

export const UploadFilesForm = ({
  files,
  onFilesChange,
  className,
}: {
  files: FormFiles;
  onFilesChange: Dispatch<SetStateAction<FormFiles>>;
  className?: string;
}) => {
  const cameraInputRef = useRef<HTMLInputElement>(null);
  const galleryInputRef = useRef<HTMLInputElement>(null);

  const handleFileUpload = async (
    e: React.ChangeEvent<HTMLInputElement>,
    type: UploadFileType,
  ) => {
    const uploadedFile = e.target.files;
    if (!uploadedFile?.length) return;

    // 기존 파일들의 해시 계산
    const existingHashes = await Promise.all(
      files.pendingFiles.map((file) => getFileHash(file.file)),
    );

    const newFiles: PendingFile[] = [];
    for (const file of Array.from(uploadedFile)) {
      const newFileHash = await getFileHash(file);
      if (existingHashes.includes(newFileHash)) {
        console.warn(`중복 파일: ${file.name}`);
      } else {
        const processedFile = await compressImage(file);
        if (!processedFile || !processedFile.type.startsWith("image/")) {
          console.error("이미지 파일만 등록 가능합니다.");
          continue;
        }
        newFiles.push({
          id: getRandomID(),
          file: processedFile,
          type,
          preview: URL.createObjectURL(processedFile),
        });
      }
    }
    e.target.value = "";
    onFilesChange((prev) => ({
      activeFiles: prev.activeFiles,
      pendingFiles: [...prev.pendingFiles.concat(newFiles)],
    }));
  };

  return (
    <div className={className}>
      <input
        type="file"
        accept="image/*"
        capture="environment"
        ref={cameraInputRef}
        className="hidden"
        onChange={(e) => handleFileUpload(e, "camera")}
      />
      <input
        type="file"
        accept="image/*"
        ref={galleryInputRef}
        className="hidden"
        onChange={(e) => handleFileUpload(e, "gallery")}
      />
      <Button
        type="button"
        variant="ghost"
        size="icon"
        onClick={() => cameraInputRef.current?.click()}
      >
        <Image
          src={icons.camera}
          className="h-[24px] w-[24px]"
          alt="사진 찍기"
        />
      </Button>
      <Button
        type="button"
        variant="ghost"
        size="icon"
        onClick={() => galleryInputRef.current?.click()}
      >
        <Image
          src={icons.gallery}
          className="h-[24px] w-[24px]"
          alt="사진 첨부"
        />
      </Button>
    </div>
  );
};
