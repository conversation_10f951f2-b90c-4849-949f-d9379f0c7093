import { ReactNode } from "react";

export interface TenantPageProps {
  params: Promise<{ tenantCd: string }>;
}
interface TenantLayoutProps extends TenantPageProps {
  children: ReactNode;
}

export default async function TenantLayout({
  params,
  children,
}: Readonly<TenantLayoutProps>) {
  const { tenantCd } = await params;
  return <div className={`h-full w-full theme-${tenantCd}`}>{children}</div>;
}
