"use server";

import { getSession } from "@/lib/session";
import { deleteSuggestComment } from "@workspace/db/crud/living-lab/suggest";
import { updateSuggestAdmit } from "@workspace/db/crud/living-lab/suggest-admin";
import { actionError, ErrorCode } from "@workspace/utils/consts/errors";

export const submitSuggestAdmit = async (
  tenantCd: string,
  suggestUid: number,
  admit: boolean,
) => {
  const session = await getSession(tenantCd);
  if (!session) return actionError(ErrorCode.UNAUTHORIZED);

  await updateSuggestAdmit(tenantCd, suggestUid, admit);
  return admit;
};

export const removeSuggestComment = async (
  tenantCd: string,
  suggestUid: number,
  parentUid: number | null,
  commentUid: number,
) => {
  const session = await getSession(tenantCd);
  if (!session) return actionError(ErrorCode.UNAUTHORIZED);

  await deleteSuggestComment(tenantCd, suggestUid, parentUid, commentUid);
  return true;
};
