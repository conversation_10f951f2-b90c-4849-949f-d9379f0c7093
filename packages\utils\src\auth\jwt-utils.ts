import crypto from "crypto";
import { compactDecrypt, CompactEncrypt, jwtVerify, SignJWT } from "jose";

const secretKey = "YqQSMqKdv30ve6GK81AGALX1qTWtUyBvIeC0WDQXymU=";
const encodeKey = new Uint8Array(Buffer.from(secretKey, "base64"));

export const encryptJWT = async (payload: any) => {
  return new SignJWT(payload)
    .setProtectedHeader({ alg: "HS256" })
    .setIssuedAt()
    .setExpirationTime("7d")
    .sign(encodeKey);
};

export const decryptJWT = async (token: string | undefined = "") => {
  try {
    const { payload } = await jwtVerify(token, encodeKey, {
      algorithms: ["HS256"],
    });
    return payload;
  } catch (error) {
    console.log("Failed to verify session", error);
  }
};

export const encryptJWE = async (
  payload: any,
  secretKeySalt: string = "",
): Promise<string> => {
  const key = secretKeySalt
    ? new Uint8Array(Buffer.from(secretKeySalt + secretKey, "base64"))
    : encodeKey;

  const encodedPayload = new TextEncoder().encode(JSON.stringify(payload));
  const jwe = await new CompactEncrypt(encodedPayload)
    .setProtectedHeader({ alg: "dir", enc: "A256GCM" })
    .encrypt(key);
  return jwe;
};

export const decryptJWE = async (
  jwe: string,
  secretKeySalt: string = "",
): Promise<any> => {
  const key = secretKeySalt
    ? new Uint8Array(Buffer.from(secretKeySalt + secretKey, "base64"))
    : encodeKey;
  const { plaintext } = await compactDecrypt(jwe, key);
  return JSON.parse(new TextDecoder().decode(plaintext));
};
