"use client";

import { SessionPayload } from "@/lib/session";
import { createTenantPath } from "@/lib/tenant";
import { useRouter } from "next/navigation";
import { useEffect } from "react";

export const RedirectToDashboard = ({
  session,
}: {
  session: SessionPayload;
}) => {
  const router = useRouter();
  useEffect(() => {
    const tenantCd = localStorage.getItem("tenantCd");
    const data = session.tenants.some((tenant) => tenant.tenantCd === tenantCd)
      ? session.tenants.find((tenant) => tenant.tenantCd === tenantCd)
      : session.tenants[0];
    router.replace(createTenantPath(data?.tenantCd || "main", "dashboard"));
  }, [router]);

  return (
    <div className="flex min-h-screen items-center justify-center">
      <div className="text-center">
        <div className="mx-auto h-4 w-4 animate-spin rounded-full border-b-2 border-gray-600"></div>
      </div>
    </div>
  );
};
