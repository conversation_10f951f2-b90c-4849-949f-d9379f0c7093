"use client";

import assert from "assert";
import { BottomButton } from "@/components/common/bottom-button";
import { zodResolver } from "@hookform/resolvers/zod";
import { <PERSON><PERSON> } from "@workspace/ui/components/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormMessage,
} from "@workspace/ui/components/form";
import {
  Sheet,
  SheetContent,
  SheetHeader,
  SheetTitle,
} from "@workspace/ui/components/sheet";
import { Textarea } from "@workspace/ui/components/textarea";
import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { useShallow } from "zustand/shallow";
import {
  FormFiles,
  UploadFilesForm,
  UploadFilesView,
} from "../components/form";
import { useMutationComment } from "../query";
import { useSuggestStore } from "../store-provider";

const formSchema = z.object({
  content: z.string().min(1, "내용을 입력해주세요."),
});

const CommentFormContent = () => {
  const [mode, suggestUid, parentUid, model, setOpen] = useSuggestStore(
    useShallow((state) => [
      state.mode,
      state.suggestUid,
      state.parentUid,
      state.model,
      state.setOpen,
    ]),
  );
  const { mutateAsync } = useMutationComment();

  const [uploadFiles, setUploadFiles] = useState<FormFiles>({
    activeFiles: [],
    pendingFiles: [],
  });

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: { content: "" },
  });

  useEffect(() => {
    form.reset({ content: model?.content ?? "" });
    setUploadFiles({
      pendingFiles: [],
      activeFiles: model?.files ?? [],
    });
  }, [form, model]);

  const handleSubmit = async (data: z.infer<typeof formSchema>) => {
    if (!suggestUid) return;
    try {
      const formData = new FormData();
      formData.append("content", data.content);
      if (parentUid) {
        formData.append("parentUid", parentUid.toString());
      }
      const { activeFiles, pendingFiles } = uploadFiles;
      activeFiles.forEach((fileData) => {
        formData.append("activeFiles", fileData.uid.toString());
      });
      pendingFiles.forEach((fileData) => {
        formData.append("pendingFiles", fileData.file);
      });

      try {
        if (mode === "edit") {
          assert(model, "model is required");
          await mutateAsync({
            suggestUid,
            parentUid,
            commentUid: model.commentUid,
            formData,
          });
        } else {
          await mutateAsync({ suggestUid, parentUid, formData });
        }
        setOpen(false);
      } catch (e) {
        console.error("업로드 중 오류 발생:", e);
      }
    } catch (error) {
      console.error("업로드 중 오류 발생:", error);
    }
  };

  return (
    <SheetContent
      side="bottom"
      onPointerDownOutside={(e) => e.preventDefault()}
      onInteractOutside={(e) => e.preventDefault()}
      className="w-full rounded-t-[18px] border-t-[1px] bg-[#ffffff] px-[20px] pb-[20px] pt-[10px] shadow-2xl"
    >
      <SheetHeader>
        <SheetTitle className="mb-[20px] flex items-center justify-between text-[20px] font-medium">
          {parentUid ? "댓댓글" : "댓글"}
          {mode === "edit" ? " 수정" : null}
        </SheetTitle>
      </SheetHeader>
      <div>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)}>
            <FormField
              control={form.control}
              name="content"
              render={({ field }) => (
                <FormItem className="mb-[10px]">
                  <FormControl>
                    <Textarea
                      {...field}
                      placeholder="내용을 입력해주세요"
                      className="resize-none"
                      rows={3}
                    />
                  </FormControl>
                  <FormMessage>
                    {form.formState.errors.content?.message}
                  </FormMessage>
                </FormItem>
              )}
            />
            <div className="mb-[4px] mt-2">
              <UploadFilesView
                files={uploadFiles}
                onFilesChange={setUploadFiles}
              />
            </div>
            <div className="flex items-center justify-between">
              <UploadFilesForm
                files={uploadFiles}
                onFilesChange={setUploadFiles}
              />
              <Button
                type="submit"
                className="h-[32px] rounded-[10px]"
                disabled={form.formState.isSubmitting}
              >
                작성완료
              </Button>
            </div>
          </form>
        </Form>
      </div>
    </SheetContent>
  );
};

export const WriteComment = ({ suggestUid }: { suggestUid: number }) => {
  const [open, setOpen, addComment] = useSuggestStore(
    useShallow((state) => [state.open, state.setOpen, state.addComment]),
  );

  return (
    <>
      <BottomButton onClick={() => addComment(suggestUid, null)}>
        댓글쓰기
      </BottomButton>
      <Sheet open={open} onOpenChange={setOpen} modal={true}>
        <CommentFormContent />
      </Sheet>
    </>
  );
};
