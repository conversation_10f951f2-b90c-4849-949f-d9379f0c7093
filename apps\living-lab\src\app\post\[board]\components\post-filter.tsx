"use client";

import { FilterSelect } from "@/components/filter-select";
import { SelectType } from "@workspace/ui/components/custom/simple-select";
import dayjs from "dayjs";
import { usePathname, useRouter } from "next/navigation";
import { useCallback } from "react";

export const PostFilter = ({
  category,
  year,
  selectableCategories: categories,
  selectableYears,
  count,
}: {
  category: string | undefined;
  year: number;
  selectableCategories: SelectType[];
  selectableYears: number[];
  count: number;
}) => {
  const router = useRouter();
  const pathname = usePathname();

  const categoryValue = category
    ? categories.find((item) => item.value === category)?.value
    : undefined;
  const yearValue = selectableYears.includes(year) ? year : dayjs().year();

  const handleFilter = useCallback(
    (categoryValue: string | undefined, yearValue: number | undefined) => {
      const params = new URLSearchParams();
      if (categoryValue) params.set("category", categoryValue);
      if (yearValue) params.set("year", yearValue.toString());
      router.replace(`${pathname}?${params.toString()}`);
    },
    [pathname, router],
  );

  const handleCategoryChange = (newCategory: string | undefined) => {
    handleFilter(newCategory, year);
  };
  const handleYearChange = (newYear: number) => {
    handleFilter(category, newYear);
  };

  return (
    <div>
      <FilterSelect
        category={categoryValue}
        categories={categories}
        showYears={true}
        year={yearValue}
        selectableYears={selectableYears}
        count={count}
        handleCategoryChange={handleCategoryChange}
        handleYearChange={handleYearChange}
      />
    </div>
  );
};
