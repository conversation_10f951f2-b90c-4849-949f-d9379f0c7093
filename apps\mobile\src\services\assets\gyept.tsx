import { Routes, useNavigate } from "@workspace/webview-state";
import { Assets } from "./types";

export default function Gyept(): Assets {
  const { navigate } = useNavigate();
  return {
    home: {
      idCard: {
        backgroundImage: "/assets/gyept/home-id-card-bg.png",
        logoImage: {
          src: "/assets/gyept/home-id-card-logo.png",
          width: 86,
          height: 30,
        },
        pointTextFgColor: "#009DA5",
        pointTextBgColor: "#ffffffb3",
        pointButtonBgColor: "#0c8489c9",
      },
      localService: {
        title: "로컬 서비스",
        items: [
          {
            title: "에너지음",
            imageUrl: "/assets/gyept/menu/green-g1.png",
            description: "빌딩 에너지 절약하기",
            buttonText: "이용하기",
            onPress: () => navigate(Routes.gyeptG1()),
          },
          {
            title: "이노베이션센터",
            imageUrl: "/assets/gyept/menu/green-innovation.png",
            description: "시민 참여형 혁신거점",
            buttonText: "이용하기",
            onPress: () => navigate(Routes.gyeptG7()),
          },
          {
            title: "도시숲 시민활동",
            imageUrl: "/assets/gyept/menu/green-forest.png",
            description: "내 손으로 나무 키우기",
            buttonText: "이용하기",
          },
          {
            title: "페트랑",
            imageUrl: "/assets/gyept/menu/green-g5.png",
            description: "재활용 활동 하기",
            buttonText: "이용하기",
            onPress: () => navigate(Routes.gyeptG4()),
          },
        ],
      },
    },
    wallet: {
      idCard: {
        logoImage: {
          src: "/assets/gyept/wallet-id-card-logo.png",
          width: 100,
          height: 35,
        },
      },
    },
  };
}
