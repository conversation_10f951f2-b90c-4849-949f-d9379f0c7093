"use client";

import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
import {
  TermsDataInsert,
  TermsGroup,
  TermsGroupAndData,
} from "@workspace/db/crud/common/terms";
import { <PERSON><PERSON> } from "@workspace/ui/components/button";
import { Card } from "@workspace/ui/components/card";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@workspace/ui/components/form";
import { Input } from "@workspace/ui/components/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@workspace/ui/components/select";
import { Textarea } from "@workspace/ui/components/textarea";
import { useRouter } from "next/navigation";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { upsertTerms } from "../../actions";
import { TermsSummaryForm } from "./terms-summary-form";

export const formSchema = z.object({
  seq: z.number(),
  termsCd: z.number(),
  termsTitle: z.string().min(1, "제목을 입력해주세요."),
  termsVer: z.string().min(1, "버전을 입력해주세요."),
  termsSmry: z.array(
    z.object({
      seqNum: z.number(),
      title: z.string().min(1, "요약 제목을 입력해주세요."),
      content: z
        .array(z.string())
        .refine((content) => content.some((item) => item.trim().length > 0), {
          message: "내용을 입력해주세요.",
        }),
    }),
  ),
  termsConts: z.string().min(1, "내용을 입력해주세요."),
  mustYn: z.string(),
  valYn: z.string(),
  privacyYn: z.string(),
});

export const TermsForm = ({
  tenantCd,
  initialData,
  termsGroups,
}: {
  tenantCd: string;
  initialData: TermsGroupAndData | null;
  termsGroups: TermsGroup[];
}) => {
  const router = useRouter();
  const termsData = initialData?.termsData;
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      seq: termsData?.seq ?? 0,
      termsCd: termsData?.termsCd ?? 0,
      termsTitle: termsData?.termsTitle ?? "",
      termsVer: termsData?.termsVer ?? "",
      termsSmry: termsData?.termsSmry ? JSON.parse(termsData?.termsSmry) : [],
      termsConts: termsData?.termsConts ?? "",
      mustYn: termsData?.mustYn ? "Y" : "N",
      valYn: termsData?.valYn ? "Y" : "N",
      privacyYn: termsData?.privacyYn ? "Y" : "N",
    },
  });

  const handleSubmit = form.handleSubmit(async (data) => {
    const model: TermsDataInsert = {
      seq: data.seq,
      termsCd: data.termsCd,
      termsTitle: data.termsTitle,
      termsVer: data.termsVer,
      termsSmry: JSON.stringify(data.termsSmry),
      termsConts: data.termsConts,
      mustYn: data.mustYn === "Y",
      valYn: data.valYn === "Y",
      privacyYn: data.privacyYn === "Y",
      regDt: new Date(),
      chgDt: new Date(),
    };
    await upsertTerms(tenantCd, termsData?.termsId, model);
    alert("저장되었습니다.");
    router.replace(`/${tenantCd}/board/terms`);
  });

  return (
    <Card className="p-4">
      <Form {...form}>
        <form onSubmit={handleSubmit}>
          <div className="flex flex-wrap gap-4">
            <div className="flex min-w-[300px] flex-1 items-center gap-2">
              <FormField
                control={form.control}
                name="termsCd"
                render={({ field }) => (
                  <FormItem className="w-36">
                    <FormLabel>약관그룹</FormLabel>
                    <FormMessage />
                    <Select
                      onValueChange={(value) => field.onChange(Number(value))}
                      defaultValue={field.value.toString()}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {termsGroups.map((group) => (
                          <SelectItem
                            key={group.termsCd}
                            value={group.termsCd.toString()}
                          >
                            {group.termsGroupTitle}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="termsTitle"
                render={({ field }) => (
                  <FormItem className="flex-1">
                    <FormLabel>제목</FormLabel>
                    <FormControl>
                      <Input {...field} type="text" />
                    </FormControl>
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="termsVer"
                render={({ field }) => (
                  <FormItem className="w-24">
                    <FormLabel>약관버전</FormLabel>
                    <FormControl>
                      <Input {...field} type="text" />
                    </FormControl>
                  </FormItem>
                )}
              />
            </div>
            <FormField
              control={form.control}
              name="valYn"
              render={({ field }) => (
                <FormItem className="w-24">
                  <FormLabel>사용여부</FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="Y">사용</SelectItem>
                      <SelectItem value="N">미사용</SelectItem>
                    </SelectContent>
                  </Select>
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="mustYn"
              render={({ field }) => (
                <FormItem className="w-24">
                  <FormLabel>필수동의</FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="Y">예</SelectItem>
                      <SelectItem value="N">아니오</SelectItem>
                    </SelectContent>
                  </Select>
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="privacyYn"
              render={({ field }) => (
                <FormItem className="w-24">
                  <FormLabel>개인정보</FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="Y">예</SelectItem>
                      <SelectItem value="N">아니오</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="seq"
              render={({ field }) => (
                <FormItem className="w-20">
                  <FormLabel>순서</FormLabel>
                  <FormControl>
                    <Input
                      {...field}
                      type="number"
                      onChange={(e) => field.onChange(Number(e.target.value))}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
          <TermsSummaryForm control={form.control} />
          <div className="mt-6">
            <FormField
              control={form.control}
              name="termsConts"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-base font-medium">
                    약관 내용
                  </FormLabel>
                  <FormControl>
                    <Textarea
                      {...field}
                      placeholder="약관의 전체 내용을 입력해주세요."
                      rows={10}
                      className="resize-vertical"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          <div className="mt-6 flex justify-end gap-2">
            <Button
              type="submit"
              className="px-6 py-2"
              disabled={form.formState.isSubmitting}
            >
              {form.formState.isSubmitting ? "저장 중..." : "저장"}
            </Button>
          </div>
        </form>
      </Form>
    </Card>
  );
};
