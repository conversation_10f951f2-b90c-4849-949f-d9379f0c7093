import { SURVEY_STATE } from "@workspace/db/schema/living-lab/survey";
import dayjs, { Dayjs } from "dayjs";

export enum SURVEY_STATE_NAME {
  IN_PROGRESS = "진행중",
  COUNTING = "집계중",
  CLOSED = "설문 완료",
  EXPIRED = "기한 만료",
}

export const isSurveyClosed = (state: string, period: Dayjs[]) => {
  const now = dayjs();
  return (
    state === SURVEY_STATE.CLOSED ||
    state === SURVEY_STATE.COUNTING ||
    (state === SURVEY_STATE.IN_PROGRESS &&
      (now.isBefore(period[0]) || now.isAfter(period[1])))
  );
};

export const getSurveyStateName = (state: string, period: Dayjs[]) => {
  const isClosed = isSurveyClosed(state, period);

  return state === SURVEY_STATE.IN_PROGRESS
    ? isClosed
      ? SURVEY_STATE_NAME.EXPIRED
      : SURVEY_STATE_NAME.IN_PROGRESS
    : state === SURVEY_STATE.COUNTING
      ? SURVEY_STATE_NAME.COUNTING
      : state === SURVEY_STATE.CLOSED
        ? SURVEY_STATE_NAME.CLOSED
        : "-";
};
