import { MainContainer } from "@/components/main-container";
import { getSession } from "@workspace/utils/auth/session";
import { notFound } from "next/navigation";
import { WalletContent } from "./components/wallet-content";

interface WalletPageProps {
  params: Promise<{ tenantCd: string }>;
}

export default async function WalletPage({
  params,
}: Readonly<WalletPageProps>) {
  const { tenantCd } = await params;

  const session = await getSession();
  if (!session) return notFound();

  return (
    <MainContainer paddingTop paddingBottom containerClassName="px-[0px]">
      <WalletContent tenantCd={tenantCd} userNm={session.userNm} />
    </MainContainer>
  );
}
