"use client";

import {
  <PERSON>tonNone,
  PrimaryButton,
  SecondaryButton,
} from "@/components/button";
import { isMainTenantCd } from "@/consts/consts";
import { Routes, useNavigate } from "@workspace/webview-state";
import Image from "next/image";
import { useCallback } from "react";

const WalletExportPointHistory = ({ tenantCd }: { tenantCd: string }) => {
  const { navigate } = useNavigate();

  return (
    <div className="mb-4 rounded-[10px] bg-white p-4 shadow-lg">
      <div className="mb-4 flex items-center">
        <Image
          src="/assets/wallet/point-manage.svg"
          width={24}
          height={24}
          alt=""
          className="mr-[8px]"
        />
        <span className="text-[16px] font-semibold">포인트 내역</span>
      </div>
      <div className="flex gap-2">
        <div className="flex-1">
          <PrimaryButton
            onClick={() => navigate(Routes.walletPointHistory({ type: "all" }))}
          >
            <Image
              src={"/assets/wallet/point-history-all.png"}
              width={24}
              height={24}
              alt="포인트 내역 전체"
            />
            전체
          </PrimaryButton>
        </div>
        {!isMainTenantCd(tenantCd) && (
          <div className="flex-1">
            <SecondaryButton
              onClick={() =>
                navigate(Routes.walletPointHistory({ type: "local" }))
              }
            >
              <Image
                src={"/assets/wallet/point-history-local.png"}
                width={24}
                height={24}
                alt="포인트 내역 지역특화"
              />
              지역특화
            </SecondaryButton>
          </div>
        )}
      </div>
    </div>
  );
};

const WalletExportItems = () => {
  const { navigate } = useNavigate();
  const handleExport = () => {
    navigate(Routes.walletPointExportKonai());
  };

  return (
    <ButtonNone onClick={handleExport}>
      <div className="flex items-center justify-between rounded-[14px] bg-[#015CA9] p-[20px] text-white">
        <div>
          <div className="mb-[12px] text-[20px] font-bold">지역화폐</div>
          <div className="text-[12px]">내보내기</div>
        </div>
        <Image
          src="/assets/wallet/point-export-gmoney.png"
          alt="경기지역화폐"
          width={52}
          height={52}
        />
      </div>
    </ButtonNone>
  );
};

export const WalletExportMenu = ({ tenantCd }: { tenantCd: string }) => {
  const isMainTenant = isMainTenantCd(tenantCd);
  return (
    <div>
      <WalletExportPointHistory tenantCd={tenantCd} />
      {!isMainTenant && <WalletExportItems />}
    </div>
  );
};
