import {
  POLL_CATEGORY,
  POLL_STATE,
} from "@workspace/db/schema/living-lab/poll";
import dayjs, { Dayjs } from "dayjs";

export const POLL_CATEGORIES = [
  { name: "일반투표", value: POLL_CATEGORY.GENERAL },
  { name: "찬반투표", value: POLL_CATEGORY.YES_NO },
  { name: "중복투표", value: POLL_CATEGORY.MULTIPLE },
];

export enum POLL_STATE_NAME {
  VOTING = "투표중",
  COUNTING = "집계중",
  CLOSED = "투표 완료",
  EXPIRED = "기한 만료",
}

export const getPollCategoryName = (category: string) =>
  POLL_CATEGORIES.find((v) => v.value === category)?.name;

export const isPollClosed = (state: string, period: Dayjs[]) => {
  const now = dayjs();

  return (
    state === POLL_STATE.CLOSED ||
    state === POLL_STATE.COUNTING ||
    (state === POLL_STATE.VOTING &&
      (now.isBefore(period[0]) || now.isAfter(period[1])))
  );
};

export const getPollStateName = (state: string, period: Dayjs[]) => {
  const isClosed = isPollClosed(state, period);

  return state === POLL_STATE.VOTING
    ? isClosed
      ? POLL_STATE_NAME.EXPIRED
      : POLL_STATE_NAME.VOTING
    : state === POLL_STATE.COUNTING
      ? POLL_STATE_NAME.COUNTING
      : state === POLL_STATE.CLOSED
        ? POLL_STATE_NAME.CLOSED
        : "-";
};
