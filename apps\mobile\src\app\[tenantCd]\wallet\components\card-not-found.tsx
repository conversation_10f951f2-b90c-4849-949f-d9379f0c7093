"use client";

import { ButtonNone } from "@/components/button";
import { cn } from "@workspace/ui/lib/utils";
import { Routes, useBridgeStore, useNavigate } from "@workspace/webview-state";
import { AppInsetsType } from "@workspace/webview-state/types";
import { id } from "date-fns/locale";
import Image from "next/image";
import {
  Children,
  isValidElement,
  ReactNode,
  useEffect,
  useState,
} from "react";
import { UserName } from "./common";

const Container = ({
  title,
  children,
  onClick,
  className,
}: {
  title: string;
  children?: ReactNode;
  onClick?: () => void;
  className?: string;
}) => {
  return (
    <ButtonNone onClick={onClick}>
      <div
        className={cn(
          "h-[170px] w-full rounded-t-[16px] p-[20px] shadow-lg transition-all",
          className,
        )}
      >
        <div className="flex flex-row">
          <Image
            src="/assets/wallet/id-card-add.png"
            width={30}
            height={30}
            alt="추가"
          />
          <div className="flex flex-1 justify-end text-[18px] font-semibold text-white">
            {title}
          </div>
        </div>
        {children}
      </div>
    </ButtonNone>
  );
};

export const IdCardNotFound = ({ userNm }: { userNm: string }) => {
  const { navigate } = useNavigate();
  const handleClick = () => {
    navigate(Routes.citizenId());
  };
  return (
    <Container
      title="내 지역 인증하기"
      onClick={handleClick}
      className="bg-gradient-to-r from-[#2197D7] to-[#76CFFF]"
    >
      <div className="mt-[10px] w-full justify-end text-right font-medium">
        <UserName userNm={userNm} />
      </div>
    </Container>
  );
};

export const PetCardNotFound = () => {
  const { navigate } = useNavigate();
  const handleClick = () => {
    navigate(Routes.idCard({ category: "pet" }));
  };
  return (
    <Container
      title="반려동물 신분증"
      className="bg-gradient-to-r from-[#22C8D0] to-[#2EDAE3]"
      onClick={handleClick}
    />
  );
};

export const NotFoundContainer = ({
  children,
  heights,
}: {
  children: ReactNode[] | ReactNode;
  heights: number[];
}) => {
  const [paddings, setPaddings] = useState<{ insets: AppInsetsType | null }>({
    insets: null,
  });
  const [insets] = useBridgeStore((state) => [state.insets]);
  useEffect(() => {
    if (insets) setPaddings({ insets: insets });
  }, [insets]);

  const newChildren = Children.toArray(children);
  let totalHeight = 0;
  return (
    <div
      className="fixed bottom-0 z-50 w-full"
      style={{ bottom: paddings.insets?.bottom ?? undefined }}
    >
      <div className="relative">
        {newChildren.map((child, index) => {
          totalHeight += heights[index] || 0;
          const component = (
            <div
              key={index}
              className="absolute w-full transition-all"
              style={{
                bottom: `${totalHeight}px`,
                zIndex: newChildren.length - index,
              }}
            >
              {child}
            </div>
          );
          return component;
        })}
      </div>
    </div>
  );
};

export const NotFound = ({
  userNm,
  idCardNotFound = false,
  petCardNotFound = false,
}: {
  userNm: string;
  petCardNotFound?: boolean;
  idCardNotFound?: boolean;
}) => {
  if (!idCardNotFound && !petCardNotFound) return null;

  const heights = [];
  if (idCardNotFound && petCardNotFound) {
    heights.push(-30, 100);
  } else if (idCardNotFound) {
    heights.push(0);
  } else if (petCardNotFound) {
    heights.push(-30);
  }

  const height = heights.reduce((acc, height) => acc + height, 100) + "px";

  return (
    <div style={{ height }}>
      <NotFoundContainer heights={heights}>
        {petCardNotFound && <PetCardNotFound />}
        {idCardNotFound && <IdCardNotFound userNm={userNm} />}
      </NotFoundContainer>
    </div>
  );
};
