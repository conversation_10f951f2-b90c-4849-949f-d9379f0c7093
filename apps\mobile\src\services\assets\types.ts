import { MenuItem } from "@/consts/menu-items";

export type ImageAssetType = {
  src: string;
  width: number;
  height: number;
  alt?: string;
};

export type Assets = {
  home: {
    idCard: {
      backgroundImage: string;
      logoImage?: ImageAssetType;
      pointTextFgColor: string;
      pointTextBgColor: string;
      pointButtonBgColor: string;
    };
    localService:
      | {
          title: string;
          items: MenuItem[];
        }
      | undefined;
  };
  wallet: {
    idCard: {
      logoImage?: ImageAssetType;
    };
  };
};
