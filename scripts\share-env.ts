import fs from "fs";
import path from "path";

const rootEnvPath = path.resolve(__dirname, "..", ".env");

// 앱 디렉토리 목록
const apps = [
  "apps/admin",
  "apps/living-lab",
  "apps/mobile",
  "apps/setup",
  "apps/store",
];

apps.forEach((app) => {
  const appEnvPath = path.resolve(__dirname, "..", app, ".env");
  fs.copyFileSync(rootEnvPath, appEnvPath);
  console.log(`Copied .env to ${appEnvPath}`);
});
