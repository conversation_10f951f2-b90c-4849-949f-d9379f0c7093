import Header from "@/components/common/header";
import { getSession } from "@workspace/utils/auth/session";
import { redirect, RedirectType } from "next/navigation";
import { Suspense } from "react";
import {
  SurveyDetailContent,
  SurveyDetailContentSkeleton,
} from "./components/content";

export default async function SurveyContentPage({
  params,
}: {
  params: Promise<{ surveyUid: string }>;
}) {
  const session = await getSession();
  if (!session) redirect("/", RedirectType.replace);
  const { tenantCd } = session;

  const { surveyUid: uid } = await params;
  const surveyUid = Number.parseInt(uid);
  if (Number.isNaN(surveyUid)) {
    redirect("/survey", RedirectType.replace);
  }

  return (
    <main className="flex max-h-screen min-h-screen flex-col bg-[#FFFFFF]">
      <Header title="설문상세보기" parent="/survey" />
      <div className="flex flex-1 overflow-y-hidden">
        <div className="flex flex-1 flex-col overflow-y-auto">
          <Suspense fallback={<SurveyDetailContentSkeleton />}>
            <SurveyDetailContent tenantCd={tenantCd} surveyUid={surveyUid} />
          </Suspense>
        </div>
      </div>
    </main>
  );
}
