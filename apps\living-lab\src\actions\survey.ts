"use server";

import {
  addSurveyAnswers,
  readSurvey,
  readSurveyAnswers,
  readSurveyItems,
  readSurveyRespondentCount,
  readSurveyResultSummary,
  readSurveyTextAnswers,
  SurveyAnswers,
} from "@workspace/db/crud/living-lab/survey";
import { getSession } from "@workspace/utils/auth/session";
import { actionError, ErrorCode } from "@workspace/utils/consts/errors";
import dayjs from "dayjs";
import { isSurveyClosed } from "../app/survey/consts";

export const getSurveyAnswers = async (surveyUid: number) => {
  const session = await getSession();
  if (!session) return actionError(ErrorCode.UNAUTHORIZED);

  const answerItems = await readSurveyAnswers(
    session.tenantCd,
    session.userDid,
    surveyUid,
  );

  return answerItems;
};

export const submitSurveyAnswers = async (
  surveyUid: number,
  answers: SurveyAnswers,
) => {
  const session = await getSession();
  if (!session) return actionError(ErrorCode.UNAUTHORIZED);

  const survey = await readSurvey(session.tenantCd, surveyUid);
  if (!survey) return actionError(ErrorCode.INVALID_ID);
  if (
    isSurveyClosed(survey.state, [
      dayjs(survey.startDate),
      dayjs(survey.endDate),
    ])
  )
    return actionError(ErrorCode.SURVEY_CLOSED);

  const surveyItems = await readSurveyItems(session.tenantCd, surveyUid);
  if (!surveyItems) return actionError(ErrorCode.INVALID_ID);

  return await addSurveyAnswers(
    session.tenantCd,
    session.userDid,
    surveyUid,
    surveyItems,
    answers,
  );
};

export const getSurveyInfo = async (surveyUid: number) => {
  const session = await getSession();
  if (!session) return actionError(ErrorCode.UNAUTHORIZED);

  const count = await readSurveyRespondentCount(session.tenantCd, surveyUid);
  return {
    tenantName: session.tenantNm,
    count: count,
  };
};

export const getSurveyResults = async (surveyUid: number) => {
  const session = await getSession();
  if (!session) return actionError(ErrorCode.UNAUTHORIZED);

  const answers = await readSurveyResultSummary(session.tenantCd, surveyUid);
  if (!answers) return actionError(ErrorCode.INVALID_ID);

  const count = await readSurveyRespondentCount(session.tenantCd, surveyUid);
  return { answers, count };
};

export const getSurveyTextAnswers = async (
  surveyUid: number,
  itemUid: number,
) => {
  const session = await getSession();
  if (!session) return actionError(ErrorCode.UNAUTHORIZED);

  const answers = await readSurveyTextAnswers(
    session.tenantCd,
    surveyUid,
    itemUid,
  );
  if (!answers) return actionError(ErrorCode.INVALID_ID);

  return answers;
};
