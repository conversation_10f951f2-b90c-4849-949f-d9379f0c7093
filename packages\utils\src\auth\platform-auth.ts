import * as jose from "jose";

const platformKey =
  "-----BEGIN CERTIFICATE-----\n" +
  "MIIDFTCCAf2gAwIBAgIEd5LX2TANBgkqhkiG9w0BAQsFADA6MQswCQYDVQQGEwJL\n" +
  "UjENMAsGA1UEChMEeHh4eDENMAsGA1UECxMEeHh4eDENMAsGA1UEAxMEeHh4eDAg\n" +
  "Fw0yMTExMDUwNjQyMDhaGA8yMTIxMTAxMjA2NDIwOFowOjELMAkGA1UEBhMCS1Ix\n" +
  "DTALBgNVBAoTBHh4eHgxDTALBgNVBAsTBHh4eHgxDTALBgNVBAMTBHh4eHgwggEi\n" +
  "MA0GCSqGSIb3DQEBAQUAA4IBDwAwggEKAoIBAQCEMUoWM7MCJh9Xp9hu1zblQCNn\n" +
  "nBokvWTyzbzG49HpAAg6ZZttCiFveTiGOpKCc4E+Gg/SH/SrlGhTD9Jy4xvbw53I\n" +
  "z8CpPB8mjBSrtc2rgW9C7FHXNDSqG5hatSRdFJLrFHQAIBPenmq8KnvG2JGsFUgN\n" +
  "W5n9/QMqHoHUqF9QWi3zoBQ7Vo9UZp5TVe5lZoBK2A4RxzCTgjf1oYt9XPatEA+V\n" +
  "yoUcSDlxrmKU8xOybNsyWwoykc3SEhy7NGtCKOFwwmzALNyZLjs0OB72NKpfzM5T\n" +
  "nGIu91t5+KeZ03DJtZ5ISHdRQxdR4n5qsaYocUyKMPJVz05l80263VcpadFrAgMB\n" +
  "AAGjITAfMB0GA1UdDgQWBBRwQoTQF6cm4yTxxDpYCvnjxb7BqjANBgkqhkiG9w0B\n" +
  "AQsFAAOCAQEASjr6caQIL31W3b6suAOh4kLfmRLFT+zyW6hCynVS/dnZmLiiAG5F\n" +
  "1tWPEMIKKDUwtWcSf6pbRqk/Rw/9j+JeChNEMr2nIfbsWAL0vo7ofO5sDE7/Sf4i\n" +
  "LqfYVfuuNw5fAQJzOWYTB+CE2W9tPZF/bf+ouXIealGZT1YiHAD2lLhHWkRwyJZS\n" +
  "ZgSwZafsWYCdRH5ndyBAp8biAW9jaPMM0BlroygUCDJH/gNRImdp2+7Iyu4Umosk\n" +
  "yXhUFyeqZdfFq/mK/BXFu9/gg+TriQjAaTXx9ss7vVibMUCnfjO0v6hT4OeFOUqn\n" +
  "rVSMCqx3EPP7oMZxK9KmI9u3xF3IigQEiA==\n" +
  "-----END CERTIFICATE-----\n";

let keyLike: jose.KeyLike;

export type Payload = jose.JWTPayload & {
  user_name?: string;
};

export const decryptPlatformJWT = async (token: string) => {
  if (!keyLike) {
    keyLike = await jose.importX509(platformKey, "RS256");
  }
  const { payload } = await jose.jwtVerify(token, keyLike);
  return payload as Payload;
};
