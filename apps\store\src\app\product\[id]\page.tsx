import { fetchProduct } from "@/actions/product-action";
import { ProductDetail } from "@/app/products/[[...category]]/product-detail";
import { Breadcrumb } from "@/components/common/breadcrumb";
import { MainContainer } from "@/components/main-container";
import { commaizeNumber } from "@toss/utils";
import { Skeleton } from "@workspace/ui/components/skeleton";
import { redirect } from "next/navigation";
import { Suspense } from "react";

const ProductItem = async ({
  id,
  category,
}: {
  id: number;
  category: string | undefined;
}) => {
  const data = await fetchProduct(id);
  if (!data) {
    return (
      <div className="text-center text-red-500">상품을 찾을 수 없습니다.</div>
    );
  }

  return (
    <div>
      <Breadcrumb categoryText={category} title={data.name} />
      <img
        src={data.imageUrl}
        alt={data.name}
        className="h-[184px] w-full object-contain"
        loading="lazy"
      />
      <div className="pb-[12px] text-[16px] font-medium">{data.name}</div>
      <div className="flex items-center justify-between border-b border-gray-200 py-3">
        <span className="text-sm text-gray-500">판매가</span>
        <div className="text-sm font-bold text-gray-500">
          <span className="text-lg font-bold text-[#256bff]">
            {commaizeNumber(data.price)}
          </span>{" "}
          P
        </div>
      </div>

      <Suspense fallback={<ProductDetailSkeleton />}>
        <ProductDetail
          id={data.id}
          sourceType={data.sourceType}
          sourceId={data.sourceId}
        />
      </Suspense>
    </div>
  );
};

const ProductItemSkeleton = () => {
  return (
    <div>
      <div className="mb-4">
        <Skeleton className="mb-2 h-4 w-48" />
      </div>
      <div className="flex w-full justify-center">
        <Skeleton className="align-center mb-4 h-[184px] w-[184px]" />
      </div>
      <Skeleton className="mb-3 h-6 w-3/4" />
      <div className="mb-4 flex items-center justify-between border-b border-gray-200 py-3">
        <Skeleton className="h-4 w-12" />
        <div className="flex items-center gap-1">
          <Skeleton className="h-6 w-20" />
        </div>
      </div>
      <ProductDetailSkeleton />
    </div>
  );
};

const ProductDetailSkeleton = () => {
  return (
    <div className="space-y-4">
      <Skeleton className="h-8 w-full" />
      <Skeleton className="h-32 w-full" />
      <Skeleton className="h-6 w-2/3" />
    </div>
  );
};

export default async function ProductPage({
  params,
  searchParams,
}: {
  params: Promise<{ id: string }>;
  searchParams: Promise<{ category: string | undefined }>;
}) {
  const { id } = await params;
  const { category } = await searchParams;

  if (!id) redirect("/");

  return (
    <MainContainer className="py-[20px]" gradient={false}>
      <Suspense fallback={<ProductItemSkeleton />}>
        <ProductItem id={Number(id)} category={category} />
      </Suspense>
    </MainContainer>
  );
}
