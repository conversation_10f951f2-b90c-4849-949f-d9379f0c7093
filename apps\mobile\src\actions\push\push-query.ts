import { useInfiniteQuery } from "@tanstack/react-query";
import {
  fetchPushHistory,
  IndividualMessage,
  IndividualMessageHistory,
} from "./push-action";

const PAGE_SIZE = 20;

export const useInfiniteQueryPushHistory = () =>
  useInfiniteQuery<IndividualMessageHistory>({
    queryKey: ["pushHistory"],
    queryFn: async ({ pageParam }) => {
      return await fetchPushHistory(pageParam as number, PAGE_SIZE);
    },
    getNextPageParam: (lastPage, pages) => {
      const totalCount = pages.reduce(
        (acc, page) => acc + page.individualMessageList.length,
        0,
      );
      if (lastPage.totalCount === totalCount) {
        return undefined;
      }
      return lastPage.currentPage + 1;
    },
    initialPageParam: 1,
    placeholderData: undefined,
  });
