"use client";

import { FilterSelect } from "@/components/filter-select";
import { UnderbarTabs } from "@workspace/ui/components/underbar-tabs";
import dayjs from "dayjs";
import { usePathname, useRouter } from "next/navigation";
import { useCallback } from "react";
import { POLL_CATEGORIES } from "../consts";

export const PollFilter = ({
  isPollClosed,
  category,
  year,
  selectableYears,
  count,
}: {
  isPollClosed: boolean;
  category: string | undefined;
  year: number;
  selectableYears: number[];
  count: number;
}) => {
  const router = useRouter();
  const pathname = usePathname();

  const pollStatus = isPollClosed ? "closed" : "open";
  const categories = POLL_CATEGORIES.map((category) => ({
    name: category.name,
    value: category.value,
  }));

  const categoryValue = category
    ? POLL_CATEGORIES.find((item) => item.value === category)?.value
    : undefined;

  const yearValue = selectableYears.includes(year) ? year : dayjs().year();

  const handleFilter = useCallback(
    (
      status: string,
      typeValue: string | undefined,
      yearValue: number | undefined,
    ) => {
      const closed = status === "closed";
      const params = new URLSearchParams();
      if (typeValue) params.set("category", typeValue);
      if (closed && yearValue) {
        params.set("year", yearValue.toString());
      }
      if (closed) params.set("closed", "true");
      router.replace(`${pathname}?${params.toString()}`);
    },
    [pathname, router],
  );

  const handleCategoryChange = (newCategory: string | undefined) => {
    handleFilter(pollStatus, newCategory, year);
  };

  const handleYearChange = (newYear: number) => {
    handleFilter(pollStatus, category, newYear);
  };

  return (
    <div>
      <FilterSelect
        category={categoryValue}
        categories={categories}
        showYears={isPollClosed}
        year={year}
        selectableYears={selectableYears}
        count={count}
        handleCategoryChange={handleCategoryChange}
        handleYearChange={handleYearChange}
      />
      <UnderbarTabs
        onValueChange={(value) => {
          handleFilter(value, category, yearValue);
        }}
        value={isPollClosed ? "closed" : "open"}
        items={[
          { label: "진행 중인 투표", value: "open" },
          { label: "완료된 투표", value: "closed" },
        ]}
      />
    </div>
  );
};
