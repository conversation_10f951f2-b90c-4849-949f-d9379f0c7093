import {
  AnyPgColumn,
  integer,
  pgTable,
  serial,
  smallint,
  text,
  unique,
  varchar,
} from "drizzle-orm/pg-core";
import { timestampString } from "../types";

export const suggests = pgTable("tb_ll_suggest", {
  uid: serial("uid").primary<PERSON>ey(),
  userDid: varchar("user_did", { length: 60 }).notNull(),
  name: text("name").notNull(),
  category: text("category").notNull(),
  title: text("title").notNull(),
  content: text("content").notNull(),
  hitCount: integer("hit_cnt").default(0).notNull(),
  commentCount: integer("cmt_cnt").default(0).notNull(),
  upCount: integer("up_cnt").default(0).notNull(),
  downCount: integer("down_cnt").default(0).notNull(),
  modReply: varchar("modreply_yn", { length: 1, enum: ["Y", "N"] })
    .default("N")
    .notNull(),
  isAdopted: varchar("adopt_yn", { length: 1, enum: ["Y", "N"] })
    .default("N")
    .notNull(),
  isValid: varchar("val_yn", { length: 1 }).default("Y").notNull(),
  regDate: timestampString("reg_dt").notNull().defaultNow(),
  updDate: timestampString("upd_dt")
    .notNull()
    .defaultNow()
    .$onUpdate(() => new Date().toISOString()),
});

export const suggestComments = pgTable("tb_ll_suggest_comment", {
  uid: serial("uid").primaryKey(),
  userDid: varchar("user_did", { length: 60 }).notNull(),
  suggestUid: integer("suggest_uid")
    .references(() => suggests.uid)
    .notNull(),
  parentUid: integer("parent_uid").references(
    (): AnyPgColumn => suggestComments.uid,
  ),
  name: text("name").notNull(),
  content: text("content").notNull(),
  commentCount: integer("cmt_cnt").default(0).notNull(),
  upCount: integer("up_cnt").default(0).notNull(),
  isValid: varchar("val_yn", { length: 1 }).default("Y").notNull(),
  regDate: timestampString("reg_dt").defaultNow().notNull(),
  updDate: timestampString("upd_dt")
    .defaultNow()
    .notNull()
    .$onUpdate(() => new Date().toISOString()),
});

export const suggestFiles = pgTable("tb_ll_suggest_file", {
  uid: serial("uid").primaryKey(),
  suggestUid: integer("suggest_uid").references(() => suggests.uid),
  suggestCommentUid: integer("cmt_uid").references(() => suggestComments.uid),
  originalName: text("original_name").notNull(),
  uri: text("uri").notNull(),
  isValid: varchar("val_yn", { length: 1, enum: ["Y", "N"] })
    .default("Y")
    .notNull(),
  regDate: timestampString("reg_dt").defaultNow().notNull(),
  updDate: timestampString("upd_dt")
    .defaultNow()
    .notNull()
    .$onUpdate(() => new Date().toISOString()),
});

export const suggestVotes = pgTable(
  "tb_ll_suggest_vote",
  {
    uid: serial("uid").primaryKey(),
    userDid: varchar("user_did", { length: 60 }).notNull(),
    suggestUid: integer("suggest_uid")
      .references(() => suggests.uid)
      .notNull(),
    vote: smallint("vote").notNull(),
    regDate: timestampString("reg_dt").notNull().defaultNow(),
    updDate: timestampString("upd_dt")
      .notNull()
      .defaultNow()
      .$onUpdate(() => new Date().toISOString()),
  },
  (table) => [unique("uq_ll_suggest_vote").on(table.userDid, table.suggestUid)],
);

export const suggestCommentVotes = pgTable(
  "tb_ll_suggest_cmt_vote",
  {
    uid: serial("uid").primaryKey(),
    userDid: varchar("user_did", { length: 60 }).notNull(),
    suggestUid: integer("suggest_uid")
      .references(() => suggests.uid)
      .notNull(),
    commentUid: integer("cmt_uid")
      .references(() => suggestComments.uid)
      .notNull(),
    vote: smallint("vote").notNull(),
    regDate: timestampString("reg_dt").notNull().defaultNow(),
    updDate: timestampString("upd_dt")
      .notNull()
      .defaultNow()
      .$onUpdate(() => new Date().toISOString()),
  },
  (table) => [
    unique("uq_ll_suggest_cmt_vote").on(
      table.userDid,
      table.suggestUid,
      table.commentUid,
    ),
  ],
);
