import { config } from "dotenv";
import { drizzle } from "drizzle-orm/postgres-js";
import { migrate } from "drizzle-orm/postgres-js/migrator";
import postgres from "postgres";

config({ path: ".env" });

declare global {
  // eslint-disable-next-line no-var
  var _db:
    | { [key: string]: ReturnType<typeof drizzle> | undefined }
    | undefined;
}

const MAIN_TENANT_CD = "main";
const MAIN_TENANT_CD_INTERNAL = "citycube";

const getMainDb = async () => {
  return getTenantDb(MAIN_TENANT_CD_INTERNAL);
};

const getTenantDb = async (tenantCd: string) => {
  const tenantCode = isMainTenant(tenantCd)
    ? MAIN_TENANT_CD_INTERNAL
    : tenantCd.toLowerCase();
  if (!globalThis._db) globalThis._db = {};
  if (!globalThis._db[tenantCode]) {
    const sql = postgres(process.env.DATABASE_URL!, {
      database: tenantCode,
      max: 10,
    });
    const db = drizzle(sql);
    globalThis._db[tenantCode] = db;
  }

  return globalThis._db[tenantCode];
};

const isMainTenant = (tenantCd: string) =>
  tenantCd === MAIN_TENANT_CD || tenantCd === MAIN_TENANT_CD_INTERNAL;

export * from "drizzle-orm";
export { getMainDb, getTenantDb, migrate, isMainTenant };
