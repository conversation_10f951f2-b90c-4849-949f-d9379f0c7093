"use client";

import { Button } from "@workspace/ui/components/button";
import { ThumbsDown, ThumbsUp } from "lucide-react";
import { ReactNode, useCallback, useEffect, useState } from "react";

const ToggleButton = ({
  children,
  active,
  onClick,
}: {
  children: ReactNode;
  active: boolean;
  onClick: () => void;
}) => {
  return (
    <Button
      variant={active ? "default" : "outline"}
      className={`h-[32px] rounded-[10px] ${
        active ? "" : "border-[1px] border-[#E1E4E7]"
      }`}
      onClick={onClick}
    >
      {children}
    </Button>
  );
};

export const SuggestVote = ({
  uid,
  up,
  down,
}: {
  uid: number;
  up: number;
  down: number;
}) => {
  const [voteState, setVoteState] = useState<{ up: number; down: number }>({
    up,
    down,
  });
  const [userVote, setUserVote] = useState<number | null>(null);
  const handleVote = useCallback(
    async (vote: number) => {
      const currentVote = userVote;
      setUserVote(vote);
      const response = await fetch(`/api/suggest/${uid}/vote`, {
        method: "POST",
        body: JSON.stringify({ vote }),
      });
      if (!response.ok) {
        setUserVote(currentVote);
        throw new Error("투표에 실패했습니다.");
      }
      const { up, down } = await response.json();
      setVoteState({ up, down });
    },
    [uid, userVote],
  );

  useEffect(() => {
    const fetchVote = async () => {
      const response = await fetch(`/api/suggest/${uid}/vote`);
      if (!response.ok) {
        throw new Error("투표 정보를 불러오는데 실패했습니다.");
      }
      const { vote } = await response.json();
      setUserVote(vote);
    };
    fetchVote();
  }, [uid]);

  return (
    <div className="flex justify-center space-x-[10px]">
      <ToggleButton
        active={userVote === 1}
        onClick={() => handleVote(userVote === 1 ? 0 : 1)}
      >
        <ThumbsUp />
        찬성 {voteState.up}
      </ToggleButton>
      <ToggleButton
        active={userVote === -1}
        onClick={() => handleVote(userVote === -1 ? 0 : -1)}
      >
        <ThumbsDown />
        반대 {voteState.down}
      </ToggleButton>
    </div>
  );
};
