import {
  readJoinedUserCountDaily,
  readJoinedUserCountMonthly,
} from "@workspace/db/crud/common/user";
import { Card } from "@workspace/ui/components/card";
import { Skeleton } from "@workspace/ui/components/skeleton";
import { format, subYears } from "date-fns";
import { Suspense } from "react";
import { AppContent } from "../../components/container";
import { TenantPageProps } from "../../layout";
import { UserStatsOverview } from "./components/overview";
import { PeriodPicker } from "./components/period-picker";
import {
  UserStatsDailyChart,
  UserStatsMonthlyChart,
} from "./components/stats-chart";

const UserStatsChartSkeleton = () => {
  return (
    <Card className="p-6">
      <Skeleton className="mb-4 h-6 w-48" />
      <Skeleton className="h-64 w-full" />
    </Card>
  );
};

const UserStatsMonthly = async ({
  tenantCd,
  dateRange,
}: {
  tenantCd: string;
  dateRange: [Date, Date];
}) => {
  const result = await readJoinedUserCountMonthly(tenantCd, dateRange);

  return (
    <UserStatsMonthlyChart
      title="월별 회원 가입 현황"
      dateRange={dateRange}
      data={result}
    />
  );
};

const UserStatsDaily = async ({
  tenantCd,
  month,
}: {
  tenantCd: string;
  month: Date;
}) => {
  const result = await readJoinedUserCountDaily(
    tenantCd,
    format(month, "yyyy-MM-dd"),
  );

  return (
    <UserStatsDailyChart
      title="일별 회원 가입 현황"
      month={month}
      data={result}
    />
  );
};

const UserStatsAll = async ({ tenantCd }: { tenantCd: string }) => {};

export default async function UserStatsPage({
  params,
  searchParams,
}: TenantPageProps & {
  searchParams: Promise<{ rangeFrom?: string; rangeTo?: string }>;
}) {
  const { tenantCd } = await params;
  const { rangeFrom, rangeTo } = await searchParams;
  const dateRange: [Date, Date] = [
    rangeFrom ? new Date(rangeFrom) : subYears(new Date(), 1),
    rangeTo ? new Date(rangeTo) : new Date(),
  ];

  return (
    <AppContent
      title="회원 현황"
      titleSuffix={<PeriodPicker dateRange={dateRange} />}
    >
      <div className="flex flex-row gap-4">
        <div className="flex flex-1 flex-col gap-4">
          <Suspense fallback={<UserStatsChartSkeleton />}>
            <UserStatsMonthly tenantCd={tenantCd} dateRange={dateRange} />
          </Suspense>
          <Suspense fallback={<UserStatsChartSkeleton />}>
            <UserStatsDaily tenantCd={tenantCd} month={dateRange[1]} />
          </Suspense>
        </div>
        <div>
          <UserStatsOverview tenantCd={tenantCd} />
        </div>
      </div>
    </AppContent>
  );
}
