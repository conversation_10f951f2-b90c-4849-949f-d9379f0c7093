import { integer, pgTable, text, varchar } from "drizzle-orm/pg-core";
import { timestampString } from "../types";

export type Post = typeof posts.$inferSelect;
export type PostFile = typeof postFiles.$inferSelect;

export const posts = pgTable("tb_ll_post", {
  uid: integer("uid").primaryKey().generatedByDefaultAsIdentity(),
  board: varchar("board", { length: 32 }).notNull(),
  depr: text("depr").notNull(),
  name: text("name").notNull(),
  category: text("category").notNull(),
  title: text("title").notNull(),
  content: text("content").notNull(),
  hitCount: integer("hit_cnt").default(0).notNull(),
  startDate: timestampString("start_dt").notNull(),
  endDate: timestampString("end_dt").notNull(),
  isValid: varchar("val_yn", { length: 1 }).default("Y").notNull(),
  regDate: timestampString("reg_dt").defaultNow().notNull(),
  updDate: timestampString("upd_dt")
    .defaultNow()
    .$onUpdate(() => new Date().toISOString()),
});

export const postFiles = pgTable("tb_ll_post_file", {
  uid: integer("uid").primaryKey().generatedByDefaultAsIdentity(),
  postUid: integer("post_uid").references(() => posts.uid),
  originalName: text("original_name").notNull(),
  uri: text("uri").notNull(),
  isValid: varchar("val_yn", { length: 1 }).default("Y").notNull(),
  regDate: timestampString("reg_dt").defaultNow().notNull(),
  updDate: timestampString("upd_dt")
    .defaultNow()
    .$onUpdate(() => new Date().toISOString()),
});
