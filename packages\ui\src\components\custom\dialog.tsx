"use client";

import * as DialogPrimitive from "@radix-ui/react-dialog";
import { cn } from "@workspace/ui/lib/utils";
import { XIcon } from "lucide-react";
import { DialogOverlay, DialogPortal } from "../dialog";

export function DialogContent({
  className,
  children,
  closeButton,
  ...props
}: React.ComponentProps<typeof DialogPrimitive.Content> & {
  closeButton?: boolean;
}) {
  return (
    <DialogPortal data-slot="dialog-portal">
      <DialogOverlay />
      <DialogPrimitive.Content
        data-slot="dialog-content"
        className={cn(
          "bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] fixed left-[50%] top-[50%] z-50 grid min-w-[75%] max-w-[90%] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-[16px] shadow-lg duration-200",
          className,
        )}
        {...props}
      >
        {children}
        {closeButton && (
          <DialogPrimitive.Close className="ring-offset-background data-[state=open]:bg-accent data-[state=open]:text-muted-foreground rounded-xs focus:outline-hidden absolute right-4 top-4 opacity-70 transition-opacity hover:opacity-100 disabled:pointer-events-none [&_svg:not([class*='size-'])]:size-4 [&_svg]:pointer-events-none [&_svg]:shrink-0">
            <XIcon />
            <span className="sr-only">Close</span>
          </DialogPrimitive.Close>
        )}
      </DialogPrimitive.Content>
    </DialogPortal>
  );
}
