import { formatAsDateTime } from "@/lib/datetime";
import { cn } from "@workspace/ui/lib/utils";
import { cva, VariantProps } from "class-variance-authority";
import { Dayjs } from "dayjs";
import Link from "next/link";
import { ReactNode } from "react";

const stateVariants = cva(
  "flex h-[40px] w-[40px] items-center justify-center rounded-full text-center text-[10px] px-[2px] text-pretty",
  {
    variants: {
      variant: {
        closed: "bg-primary text-background",
        expired: "border-[#E1E4E7] border-[1px] bg-[#F6F7F8] text-[#B6BDC4]",
        default: "bg-primary/10 text-primary",
      },
    },
  },
);

interface PollSurveyStateProps extends VariantProps<typeof stateVariants> {
  children: ReactNode;
}

const PollSurveyState = ({ children, variant }: PollSurveyStateProps) => {
  return <div className={cn(stateVariants({ variant }))}>{children}</div>;
};

export const PollSurveyItem = ({
  href,
  category,
  title,
  period,
  state,
  variant,
}: {
  href: string;
  category: string | undefined;
  title: string;
  period: Dayjs[];
  state: ReactNode;
  variant: "closed" | "expired" | "default";
}) => {
  return (
    <Link href={href}>
      <div className="mx-[20px] flex flex-row rounded-lg bg-white p-[20px] shadow-lg">
        <div className="flex-1 flex-col">
          <div className="mb-[6px] text-[12px] font-medium text-[#636F7A]">
            {category}
          </div>
          <div className="mb-[16px] text-ellipsis text-[16px] font-medium">
            {title}
          </div>
          <div className="text-[12px]">
            <span className="text-[#808C98]">기간 </span>
            {formatAsDateTime(period[0])} ~ {formatAsDateTime(period[1])}
          </div>
        </div>
        <PollSurveyState variant={variant}>{state}</PollSurveyState>
      </div>
    </Link>
  );
};
