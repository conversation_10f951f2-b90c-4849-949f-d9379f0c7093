"use client";

import { useQuery } from "@tanstack/react-query";
import { te } from "date-fns/locale";
import {
  fetchPointBalance,
  fetchPointBalanceByTenantCd,
  fetchPointBalanceTotal,
} from "./pont-action";

const pointKeys = {
  all: ["pointKeys"] as const,
  balance: () => [...pointKeys.all, "balance"] as const,
  balanceByTenantCd: (tenantCd: string) =>
    [...pointKeys.all, "balanceByTenantCd", tenantCd] as const,
  totalBalance: () => [...pointKeys.all, "totalBalance"] as const,
};

export const useQueryPointBalance = () => {
  return useQuery({
    queryKey: pointKeys.balance(),
    queryFn: async () => {
      return await fetchPointBalance();
    },
  });
};

export const useQueryPointBalanceByTenantCd = (tenantCd: string) => {
  return useQuery({
    queryKey: pointKeys.balanceByTenantCd(tenantCd),
    queryFn: async () => {
      return await fetchPointBalanceByTenantCd(tenantCd);
    },
  });
};

export const useQueryPointBalanceTotal = () => {
  return useQuery({
    queryKey: pointKeys.totalBalance(),
    queryFn: async () => {
      return await fetchPointBalanceTotal();
    },
  });
};
