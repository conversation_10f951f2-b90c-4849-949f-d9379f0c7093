import { MainContainer } from "@/components/main-container";
import { decryptJWE } from "@workspace/utils/auth/jwt-utils";
import { getSession } from "@workspace/utils/auth/session";
import { Metadata } from "next";
import { Suspense } from "react";
import { fetchBreeds, fetchHairColors } from "../actions/pet-actions";
import { PetPassAPMSDto } from "../actions/pet-types";
import { RegistFormCheck } from "./components/regist-form-check";
import { RegistFormConfirm } from "./components/regist-form-confirm";

export const metadata: Metadata = {
  title: "반려동물 등록하기",
};

const RegistFormLoader = async ({
  data,
}: {
  data: PetPassAPMSDto & { ownName: string };
}) => {
  const breeds = await fetchBreeds();
  if (breeds.data) {
    const breed = breeds.data.find(
      (item) => item.petBreedName === data.petBreedName,
    );
    if (breed) {
      const hairColors = await fetchHairColors({
        breedCode: breed.petBreedCode,
      });
      if (hairColors.data) {
        return (
          <RegistFormConfirm
            data={data}
            breed={breed}
            hairColors={hairColors.data}
          />
        );
      }
    }
  }
};

export default async function PetRegistPage({
  searchParams,
}: {
  searchParams: Promise<{
    step?: string;
    data?: string;
  }>;
}) {
  const session = await getSession();
  if (!session) return null;

  const { step, data } = await searchParams;
  const decryptedData = data
    ? ((await decryptJWE(data)) as PetPassAPMSDto & { ownName: string })
    : null;
  return (
    <MainContainer className="bg-white" gradient={false}>
      {step !== "registPet" || decryptedData === null ? (
        <RegistFormCheck session={session} />
      ) : (
        <Suspense fallback={null}>
          <RegistFormLoader data={decryptedData} />
        </Suspense>
      )}
    </MainContainer>
  );
}
