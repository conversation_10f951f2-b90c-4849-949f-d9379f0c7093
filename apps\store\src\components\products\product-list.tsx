"use client";

import { useInfiniteQueryProducts } from "@/actions/product-query";
import { ProductItem } from "@/types/product";
import { commaizeNumber } from "@toss/utils";
import { Card, CardContent, CardFooter } from "@workspace/ui/components/card";
import { InfiniteScroll } from "@workspace/ui/components/custom/infinite-scroll";
import { Skeleton } from "@workspace/ui/components/skeleton";
import { useRouter } from "next/navigation";
import { useCallback, useMemo } from "react";
import { useSessionStorage } from "usehooks-ts";
import { useViewTypeSession } from "../common/session";

const ProductGridItem = ({
  item,
  onPressItem,
}: {
  item: ProductItem;
  onPressItem: (item: ProductItem) => void;
}) => {
  return (
    <Card
      className="cursor-pointer overflow-hidden rounded-lg border shadow-sm transition-shadow hover:shadow-lg"
      onClick={() => onPressItem(item)}
    >
      <CardContent className="p-0">
        <img
          src={item.imageUrl}
          alt={item.name}
          className="h-[100px] w-full bg-white object-cover"
          loading="lazy"
        />
      </CardContent>
      <CardFooter className="flex h-[120px] flex-col justify-center bg-white p-3 text-center">
        <div className="flex-1">
          <p>{item.brand}</p>
          <p className="line-clamp-2 text-sm leading-tight text-[#292929]">
            {item.name}
          </p>
        </div>
        <p className="text-center text-[#292929]">
          <span className="font-semibold">{commaizeNumber(item.price)}</span> P
        </p>
      </CardFooter>
    </Card>
  );
};

const ProductListItem = ({
  item,
  onPressItem,
}: {
  item: ProductItem;
  onPressItem: (item: ProductItem) => void;
}) => {
  return (
    <Card
      className="cursor-pointer overflow-hidden transition-shadow hover:shadow-lg"
      onClick={() => onPressItem(item)}
    >
      <CardContent className="flex items-center p-3">
        <img
          src={item.imageUrl}
          alt={item.name}
          className="h-[64px] w-[64px] bg-white object-cover"
          loading="lazy"
        />
        <div className="ml-3 flex-1">
          <p className="text-sm leading-tight text-[#292929]">{item.name}</p>
          <p className="text-right text-[#292929]">
            <span className="font-semibold">{commaizeNumber(item.price)}</span>{" "}
            P
          </p>
        </div>
      </CardContent>
    </Card>
  );
};

const ProductListSkeletonItem = () => {
  return (
    <Card className="overflow-hidden transition-shadow">
      <CardContent className="flex items-center p-3">
        <Skeleton className="h-[64px] w-[64px] bg-gray-200" />
        <div className="ml-3 flex-1">
          <Skeleton className="mb-2 h-[16px] w-3/4 bg-gray-200" />
          <div className="text-right">
            <Skeleton className="ml-auto h-[16px] w-1/3 bg-gray-200" />
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

const ProductsGrid = ({
  data,
  onPressItem,
}: {
  data: ProductItem[];
  onPressItem: (item: ProductItem) => void;
}) => {
  return (
    <div className="grid grid-cols-2 gap-[8px]">
      {data.map((item, index) => (
        <ProductGridItem key={index} item={item} onPressItem={onPressItem} />
      ))}
    </div>
  );
};

const ProductsRow = ({
  data,
  onPressItem,
}: {
  data: ProductItem[];
  onPressItem: (item: ProductItem) => void;
}) => {
  return (
    <div className="gap-[8px]">
      {data.map((item, index) => (
        <ProductListItem key={index} item={item} onPressItem={onPressItem} />
      ))}
    </div>
  );
};

const ProductsSkeletonGrid = () => {
  return (
    <div className="grid grid-cols-2 gap-[8px]">
      {Array.from({ length: 10 }).map((_, index) => (
        <Card
          key={index}
          className="overflow-hidden rounded-lg border shadow-sm"
        >
          <CardContent className="p-0">
            <Skeleton className="h-[100px] w-full bg-gray-200" />
          </CardContent>
          <CardFooter className="flex h-[80px] flex-col justify-center bg-white p-3">
            <Skeleton className="mb-2 h-[20px] w-full bg-gray-200" />
            <Skeleton className="h-[20px] w-2/3 bg-gray-200" />
          </CardFooter>
        </Card>
      ))}
    </div>
  );
};

const ProductsSkeletonRow = () => {
  return (
    <div className="gap-[8px]">
      {Array.from({ length: 10 }).map((_, index) => (
        <ProductListSkeletonItem key={index} />
      ))}
    </div>
  );
};

export const ProductList = ({
  count,
  searchTag = [],
  keyword,
  sortOrder,
}: {
  count: number;
  searchTag: string[] | undefined;
  keyword: string;
  sortOrder: string;
}) => {
  const router = useRouter();
  const [viewType] = useViewTypeSession();
  const { data, fetchNextPage, isLoading, isFetchingNextPage } =
    useInfiniteQueryProducts({ searchTag, keyword, sortOrder });

  const products = useMemo(() => data?.pages.flat() ?? [], [data]);

  const handleSelectProduct = useCallback((item: ProductItem) => {
    router.push(
      `/product/${item.id}${searchTag?.length ? `?category=${searchTag.join("/")}` : ""}`,
    );
  }, []);

  const ListComponent = viewType === "grid" ? ProductsGrid : ProductsRow;
  const SkeletonComponent =
    viewType === "grid" ? ProductsSkeletonGrid : ProductsSkeletonRow;
  return (
    <InfiniteScroll
      hasMore={!isFetchingNextPage && products.length < count}
      loadMore={fetchNextPage}
    >
      {isLoading ? (
        <SkeletonComponent />
      ) : products.length === 0 ? (
        <div className="flex h-[200px] items-center justify-center text-gray-500">
          <p className="text-sm">상품이 없습니다.</p>
        </div>
      ) : (
        <ListComponent data={products} onPressItem={handleSelectProduct} />
      )}
      {isFetchingNextPage && <SkeletonComponent />}
    </InfiniteScroll>
  );
};
