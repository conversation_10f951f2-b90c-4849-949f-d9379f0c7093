"use client";

import { ButtonNone } from "@/components/button";
import { cn } from "@workspace/ui/lib/utils";
import { Routes, useNavigate } from "@workspace/webview-state";
import Image from "next/image";
import { PetPassPetDto } from "../../id-card/actions/pet-types";
import { UserName } from "./common";

const PetPassCard = ({
  data,
  className,
}: {
  data: PetPassPetDto;
  className?: string;
}) => {
  const { navigate } = useNavigate();
  const handleClick = () => {
    navigate(Routes.idCard({ category: "pet" }));
  };

  return (
    <ButtonNone asChild onClick={handleClick}>
      <div
        className={cn(
          "h-[110px] w-full overflow-visible rounded-t-[16px]",
          "bg-gradient-to-r from-[#22C8D0] to-[#2EDAE3]",
          className,
        )}
      >
        <div
          className={cn("flex h-full w-full items-start")}
          style={{
            backgroundImage: "url('/assets/wallet/pet-card-mask.png')",
            backgroundSize: "cover",
            backgroundPosition: "right top",
          }}
        >
          <div
            className={cn(
              "flex w-full flex-row items-center justify-center p-[20px]",
            )}
          >
            <div className="flex flex-row items-center">
              <Image
                src={"/assets/wallet/pet-card-dog.png"}
                width={40}
                height={40}
                alt="반려동물 아이콘"
              />
              <span className="ml-[4px] text-[18px] font-semibold">
                반려동물 신분증
              </span>
            </div>
            <UserName userNm={data.petName} />
          </div>
        </div>
      </div>
    </ButtonNone>
  );
};

export const PetPassCards = ({
  data,
  className,
}: {
  data: PetPassPetDto[];
  className?: string;
}) => {
  const item = data[0];
  return <PetPassCard data={item} className={className} />;
};
