"use client";

import { isMainTenantCd } from "@/consts/consts";
import { Skeleton } from "@workspace/ui/components/skeleton";
import { ReactNode } from "react";
import { useQueryPets } from "../../id-card/actions/pet-query";
import { NotFound } from "./card-not-found";
import { IdCard } from "./id-card";
import { PetPassCards } from "./pet-pass-card";
import { PointCard } from "./point-card";

const WalletContentSkeleton = () => {
  return (
    <div className="relative h-full overflow-auto">
      <div className="mb-4">
        <Skeleton className="h-32 w-full rounded-xl" />
      </div>
      <div className="mb-4">
        <Skeleton className="h-24 w-full rounded-xl" />
      </div>
      <div className="mb-4">
        <Skeleton className="h-20 w-full rounded-xl" />
      </div>
    </div>
  );
};
export const WalletContent = ({
  tenantCd,
  userNm,
}: {
  tenantCd: string;
  userNm: string;
}) => {
  const { data: pets, isLoading } = useQueryPets("db");

  const hasIdCard = !isMainTenantCd(tenantCd);
  const hasPetPass = pets?.data && pets.data.length > 0;

  if (isLoading) return <WalletContentSkeleton />;

  return (
    <div className="relative h-full overflow-auto">
      {hasIdCard && (
        <IdCard key="id-card" tenantCd={tenantCd} userNm={userNm} />
      )}
      {hasPetPass && pets?.data && (
        <div
          className="relative w-full transition-all"
          style={{ marginTop: hasIdCard ? -10 : undefined }}
        >
          <PetPassCards key="pet-pass-cards" data={pets.data} />
        </div>
      )}
      <div
        className="relative w-full"
        style={{ marginTop: hasIdCard || hasPetPass ? -10 : undefined }}
      >
        <PointCard tenantCd={tenantCd} />
      </div>
      <NotFound
        userNm={userNm}
        petCardNotFound={!hasPetPass}
        idCardNotFound={!hasIdCard}
      />
    </div>
  );
};
