import { SurveyItemWithOption } from "@workspace/db/crud/living-lab/survey";
import { ReactNode } from "react";

export const SurveyQuestion = ({
  index,
  item,
  children,
}: {
  index: number;
  item: SurveyItemWithOption;
  children: ReactNode;
}) => {
  const seq = (index + 1).toString().padStart(2, "0");
  return (
    <div className="border-b-[1px] border-[#F5F5F5] py-[20px]">
      <div key={item.uid} className="flex flex-row pb-[16px]">
        <div className="text-primary mr-[6px] flex min-w-[30px] text-[16px] font-medium">
          {seq}
        </div>
        <div>
          <div className="text-[14px]">{item.content}</div>
          <div className="mt-[10px] text-[12px] text-[#808C98]">
            {item.guide}
          </div>
        </div>
      </div>
      {children}
    </div>
  );
};
