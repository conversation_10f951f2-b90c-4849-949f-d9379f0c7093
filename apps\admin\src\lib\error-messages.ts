export const ERROR_MESSAGES = {
  AUTH: {
    LOGIN_INVALID: "아이디 혹은 비밀번호를 확인해주세요.",
    LOGIN_UNAUTHORIZED: "로그인 권한이 없습니다.",
    SIGNUP_FAILED: "회원가입에 실패했습니다. 잠시 후 다시 시도해주세요.",
    SIGNUP_DUPLICATE_ID: "이미 사용 중인 아이디입니다.",
    SIGNUP_ID_REQUIRED: "아이디는 필수 입력 항목입니다.",
    SIGNUP_ID_CHECK_FAILED:
      "아이디 중복 확인에 실패했습니다. 잠시 후 다시 시도해주세요.",
  },

  COMMON: {
    UNKNOWN_ERROR: "알 수 없는 오류가 발생했습니다.",
    NETWORK_ERROR: "네트워크 오류가 발생했습니다.",
    SERVER_ERROR: "서버 오류가 발생했습니다.",
  },
} as const;

export type ErrorMessage = typeof ERROR_MESSAGES;
export type AuthErrorKeys = keyof typeof ERROR_MESSAGES.AUTH;
export type CommonErrorKeys = keyof typeof ERROR_MESSAGES.COMMON;
