import { and, desc, eq, getTenantDb, like, SQL } from "@workspace/db/drizzle";
import {
  suggestComments,
  suggestFiles,
  suggests,
} from "@workspace/db/schema/living-lab/suggest";
import { withPagination } from "@workspace/db/utils";
import { SuggestComment, SuggestFile } from "./suggest";

export const createSuggestFilters = (filters: {
  keywordFilter?: "title" | "name";
  keyword?: string;
  category?: string;
  isValid?: string;
  isAdpoted?: string;
  startDate?: Date;
  endDate?: Date;
}) => {
  return and(
    filters.keyword
      ? filters.keywordFilter === "title"
        ? like(suggests.title, `%${filters.keyword}%`)
        : like(suggests.name, `%${filters.keyword}%`)
      : undefined,
    filters.category ? eq(suggests.category, filters.category) : undefined,
    filters.isValid ? eq(suggests.isValid, filters.isValid) : undefined,
  );
};

export const readSuggestsCountAdmin = async (
  tenantId: string,
  filters: SQL<unknown> | undefined,
) => {
  const tenantDb = await getTenantDb(tenantId);
  const result = await tenantDb.$count(suggests, filters);
  return result;
};

export const readSuggestsAdmin = async (
  tenantId: string,
  filters: SQL<unknown> | undefined,
  page: number,
  pageSize: number,
) => {
  const tenantDb = await getTenantDb(tenantId);

  const query = tenantDb
    .select()
    .from(suggests)
    //.where(and(undefined, eq(suggests.category, "시설")))
    .where(filters)
    .orderBy(desc(suggests.uid));

  const result = await (page || pageSize
    ? withPagination(query.$dynamic(), page, pageSize)
    : query);
  return result;
};

export const readSuggestCommentsAdmin = async (
  tenantId: string,
  uid: number,
) => {
  const tenantDb = await getTenantDb(tenantId);
  const result = await tenantDb
    .select({
      comment: suggestComments,
      files: suggestFiles,
    })
    .from(suggestComments)
    .leftJoin(
      suggestFiles,
      and(
        eq(suggestFiles.suggestUid, suggestComments.suggestUid),
        eq(suggestFiles.suggestCommentUid, suggestComments.uid),
        eq(suggestFiles.isValid, "Y"),
      ),
    )
    .where(
      and(
        eq(suggestComments.suggestUid, uid),
        eq(suggestComments.isValid, "Y"),
      ),
    );

  const commentMap = result.reduce(
    (acc, row) => {
      const comment = row.comment;
      if (!acc.has(comment.uid)) {
        acc.set(comment.uid, {
          ...comment,
          files: row.files ? [row.files] : [],
        });
      } else if (row.files) {
        acc.get(comment.uid)?.files.push(row.files);
      }
      return acc;
    },
    new Map<
      number,
      SuggestComment & {
        files: SuggestFile[];
      }
    >(),
  );

  return Array.from(commentMap.values());
};

export const updateSuggestAdmit = async (
  tenantId: string,
  suggestUid: number,
  admit: boolean,
) => {
  const tenantDb = await getTenantDb(tenantId);
  await tenantDb
    .update(suggests)
    .set({ isAdopted: admit ? "Y" : "N" })
    .where(eq(suggests.uid, suggestUid));
};
