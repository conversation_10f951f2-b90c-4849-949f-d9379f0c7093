import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
} from "@workspace/ui/components/select";

export type SelectType = {
  name: string;
  value: string;
};

export const SimpleSelect = ({
  value,
  options,
  defaultValue,
  onChange,
}: {
  value: string | undefined;
  defaultValue?: string;
  options: SelectType[];
  onChange: (value: string) => void;
}) => {
  const selectedValue =
    options.find((o) => o.value === value)?.value ??
    defaultValue ??
    options[0].value;
  const selectedName = options.find((o) => o.value === selectedValue)?.name;
  return (
    <Select value={selectedValue} onValueChange={onChange}>
      <SelectTrigger>{selectedName}</SelectTrigger>
      <SelectContent>
        {options.map((option, index) => (
          <SelectItem key={index} value={option.value}>
            {option.name}
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  );
};
