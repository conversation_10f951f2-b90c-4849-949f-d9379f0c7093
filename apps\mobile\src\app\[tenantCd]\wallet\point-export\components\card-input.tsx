import { ButtonNone } from "@/components/button";
import { CirclePlus } from "lucide-react";

export const CardInput = () => {
  return (
    <div>
      <div className="py-[20px] text-[16px] font-semibold">
        지역화폐로 보낼 카드 선택
      </div>
      <ButtonNone className="flex h-[70px] w-full items-center justify-center rounded-[10px] border-[1px]">
        <div className="flex-1">
          <CirclePlus size={24} className="mr-[8px]" />
          <div>지역화폐 카드 등록하기</div>
        </div>
      </ButtonNone>
      <div className="mt-[20px] text-[14px] font-semibold text-[#256bff]">
        지역화폐 카드 등록 이후 이용하실 수 있습니다.
      </div>
    </div>
  );
};
