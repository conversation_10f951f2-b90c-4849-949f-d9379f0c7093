"use client";

import { PrimaryButton } from "@/components/button";
import { zodResolver } from "@hookform/resolvers/zod";
import { DatePickerForm } from "@workspace/ui/components/custom/date-picker";
import { RadioGrid } from "@workspace/ui/components/custom/radio-grid";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
} from "@workspace/ui/components/form";
import { Input } from "@workspace/ui/components/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@workspace/ui/components/select";
import { useShowModalAlert } from "@workspace/ui/hooks/modal";
import { format } from "date-fns";
import { useRouter } from "next/navigation";
import { useCallback, useEffect } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { fetchOwner } from "../../actions/pet-actions";
import { getPetType, useMutationRegistPet } from "../../actions/pet-query";
import {
  PetPassAPMSDto,
  PetPassBreedDto,
  PetPassHairColorDto,
  PetPassRegistPetRequest,
  PetSizeType,
  PetType,
} from "../../actions/pet-types";

const formSchema = z
  .object({
    pet_birth: z.date(),
    petWeight: z.string().refine((value) => /^\d+(\.\d{1,2})?$/.test(value)),
    petType: z.string().nonempty(),
    petHairColor: z.string().nonempty(),
    petVaccinateYn: z.string().nonempty(),
    petRabiesVirusYn: z.string(),
    petSizeType: z.nativeEnum(PetSizeType),
  })
  .refine(
    (data) =>
      data.petType !== PetType.DOG ||
      (data.petRabiesVirusYn && data.petSizeType),
    {
      path: ["petRabiesVirusYn"],
    },
  );

const ynFields = [
  { value: "Y", label: "완료" },
  { value: "N", label: "미완료" },
];

export const RegistFormConfirm = ({
  data,
  breed,
  hairColors,
}: {
  data: PetPassAPMSDto & { ownName: string };
  breed: PetPassBreedDto;
  hairColors: PetPassHairColorDto[];
}) => {
  const petType = getPetType(breed.petBreedCode);
  const { mutateAsync, isPending } = useMutationRegistPet();
  const router = useRouter();
  const showModalAlert = useShowModalAlert();

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    mode: "onChange",
    defaultValues: {
      petWeight: "",
      petType: petType,
      petVaccinateYn: "",
      petRabiesVirusYn: "",
      petSizeType: undefined,
    },
  });

  const handleSubmit = useCallback(
    async (formData: z.infer<typeof formSchema>) => {
      const model: PetPassRegistPetRequest = {
        ownName: data.ownName,
        petRegNo: data.petRegNo,
        petBirth: format(formData.pet_birth, "yyyyMMdd"),
        petWeight: formData.petWeight,
        petName: data.petName,
        petType: formData.petType,
        petBreed: data.petBreedName,
        petBreedCode: breed.petBreedCode,
        petBreedId: breed.petBreedCode,
        petIsFierce: breed.isFierce === "1",
        petHairColorCode: formData.petHairColor,
        petGender: data.petGender === "수컷" ? "M" : "F",
        petNeuterYn: data.petNeuterYn === "중성" ? "Y" : "N",
        petVaccinateYn: formData.petVaccinateYn,
        petRabiesVirusYn: formData.petRabiesVirusYn,
        petSizeType: formData.petSizeType,
        petOffice: data.officeTel,
        relationshipOwn: 1,
        isVerified: true,
      };
      const result = await mutateAsync(model);
      if (result.data) {
        showModalAlert({
          title: "등록 완료",
          children: "등록이 완료되었습니다.",
          options: {
            onClose: () => {
              router.replace("../id-card?category=pet");
            },
          },
        });
      } else {
        showModalAlert({
          title: "등록 실패",
          children: `등록에 실패하였습니다.\n${result.error}`,
          options: {
            onClose: () => {
              console.log("router", router.replace);
              router.replace("../id-card?category=pet");
            },
          },
        });
      }
    },
    [showModalAlert, router, mutateAsync, data, breed],
  );

  useEffect(() => {
    fetchOwner({
      userDid: "did:dxd:000081c3c9acc80c927d5b15ceac336ec4231040e3c1e19f8aa8",
    }).then((res) => {
      console.log(res);
    });
  }, []);

  return (
    <div>
      <div className="my-[12px] flex flex-col gap-[12px]">
        <Form {...form}>
          <div>
            <div className="mb-[10px] text-[16px] font-semibold">
              반려동물이름
            </div>
            <Input value={data.petName} readOnly />
          </div>
          <div>
            <div className="mb-[10px] text-[16px] font-semibold">성별</div>
            <Input value={data.petGender} readOnly />
          </div>
          <div>
            <div className="mb-[10px] text-[16px] font-semibold">
              국가동물등록번호
            </div>
            <Input value={data.petRegNo} readOnly />
          </div>
          <div>
            <div className="mb-[10px] text-[16px] font-semibold">품종</div>
            <Input value={data.petBreedName} readOnly />
          </div>
          <div>
            <div className="mb-[10px] text-[16px] font-semibold">
              중성화여부
            </div>
            <Input value={data.petNeuterYn} readOnly />
          </div>
          <div>
            <div className="mb-[10px] text-[16px] font-semibold">생년월일</div>
            <FormField
              control={form.control}
              name="pet_birth"
              render={({ field }) => (
                <FormItem>
                  <FormControl>
                    <DatePickerForm
                      field={field}
                      className="w-full"
                      placeholder="생년월일을 선택해주세요"
                      disabled={(date) =>
                        date > new Date() || date < new Date("1900-01-01")
                      }
                    />
                  </FormControl>
                </FormItem>
              )}
            />
          </div>
          <div>
            <div className="mb-[10px] text-[16px] font-semibold">
              몸무게(Kg)
            </div>
            <FormField
              control={form.control}
              name="petWeight"
              render={({ field }) => (
                <FormItem>
                  <FormControl>
                    <Input
                      {...field}
                      placeholder="몸무게를 입력해주세요(소수점 2자리까지 가능)"
                      type="number"
                      step="0.01"
                    />
                  </FormControl>
                </FormItem>
              )}
            />
          </div>
          <div>
            <div className="mb-[10px] text-[16px] font-semibold">털색</div>
            <FormField
              control={form.control}
              name="petHairColor"
              render={({ field }) => (
                <FormItem>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="털색을 선택해주세요" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {hairColors.map((item, index) => (
                        <SelectItem key={index} value={item.petHairColorCode}>
                          {item.petHairColorName}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </FormItem>
              )}
            />
          </div>
          <div>
            <div className="mb-[10px] text-[16px] font-semibold">
              백신 접종 여부
            </div>
            <FormField
              control={form.control}
              name="petVaccinateYn"
              render={({ field }) => (
                <FormItem>
                  <RadioGrid
                    field={field}
                    items={ynFields}
                    className="grid-cols-2"
                  />
                </FormItem>
              )}
            />
          </div>
          {petType === PetType.DOG && (
            <div>
              <div className="mb-[10px] text-[16px] font-semibold">
                광견병 백신 접종 여부
              </div>
              <FormField
                control={form.control}
                name="petRabiesVirusYn"
                render={({ field }) => (
                  <FormItem>
                    <RadioGrid
                      field={field}
                      items={ynFields}
                      className="grid-cols-2"
                    />
                  </FormItem>
                )}
              />
            </div>
          )}
          {petType === PetType.DOG && (
            <div>
              <div className="mb-[10px] text-[16px] font-semibold">
                견종크기
              </div>
              <FormField
                control={form.control}
                name="petSizeType"
                render={({ field }) => (
                  <FormItem>
                    <RadioGrid
                      field={field}
                      items={[
                        { value: PetSizeType.SMALL, label: "소형견" },
                        { value: PetSizeType.MEDIUM, label: "중형견" },
                        { value: PetSizeType.LARGE, label: "대형견" },
                      ]}
                      className="grid-cols-3"
                    />
                  </FormItem>
                )}
              />
            </div>
          )}
          <div className="mt-[40px]">
            <PrimaryButton
              disabled={!form.formState.isValid || isPending}
              onClick={form.handleSubmit(handleSubmit)}
            >
              등록하기
            </PrimaryButton>
          </div>
        </Form>
      </div>
    </div>
  );
};
