import { createStore } from "zustand";

type GlobalState = {
  baseURL: string;
};

type GlobalActions = {
  setBaseURL: (baseURL: string) => void;
};

export type GlobalStore = GlobalState & GlobalActions;

const initialState: GlobalState = {
  baseURL: "",
};

export const createGlobalStore = () =>
  createStore<GlobalStore>((set) => ({
    ...initialState,
    setBaseURL: (baseURL) => {
      set({ baseURL });
    },
  }));
