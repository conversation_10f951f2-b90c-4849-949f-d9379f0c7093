"use client";

import { PrimaryButton } from "@/components/button";
import { Routes, useNavigate } from "@workspace/webview-state";

export const BottomBar = () => {
  const { navigate } = useNavigate();
  const handleButtonClick = () => {
    navigate(Routes.pedometerDetail());
  };
  return (
    <div className="mt-[15px]">
      <PrimaryButton onClick={handleButtonClick}>
        <div className="text-[16px]">상세 기록 보기</div>
      </PrimaryButton>
    </div>
  );
};
