"use client";

import { createContext, ReactNode, useContext, useRef } from "react";
import { useStore } from "zustand";
import { createSurveyStore, SurveyStore } from "./store";

type SurveyStoreApi = ReturnType<typeof createSurveyStore>;

export const SurveyStoreContext = createContext<SurveyStoreApi | undefined>(
  undefined,
);

export const SurveyStoreProvider = ({ children }: { children: ReactNode }) => {
  const storeRef = useRef<SurveyStoreApi>(null);
  if (!storeRef.current) {
    storeRef.current = createSurveyStore();
  }
  return (
    <SurveyStoreContext.Provider value={storeRef.current}>
      {children}
    </SurveyStoreContext.Provider>
  );
};

export const useSurveyStore = <T,>(selector: (store: SurveyStore) => T): T => {
  const context = useContext(SurveyStoreContext);
  if (!context) {
    throw new Error("useSurveyStore must be used within a StoreProvider");
  }
  return useStore(context, selector);
};
