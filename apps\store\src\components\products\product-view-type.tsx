"use client";

import { useSessionStorage } from "usehooks-ts";
import { ViewType, ViewTypeItem } from "../common/view-type";

const viewTypeList: ViewTypeItem<string>[] = [
  {
    url: "/images/view_grid.svg",
    urlSelected: "/images/view_grid_selected.svg",
    value: "grid",
  },
  {
    url: "/images/view_list.svg",
    urlSelected: "/images/view_list_selected.svg",
    value: "list",
  },
];

export const ProductViewType = () => {
  const [viewTypeValue, setViewTypeValue] = useSessionStorage(
    "viewType",
    viewTypeList[0].value,
  );
  return (
    <ViewType
      items={viewTypeList}
      value={viewTypeValue}
      onChange={setViewTypeValue}
    />
  );
};
