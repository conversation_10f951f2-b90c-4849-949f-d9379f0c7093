"use client";

import { Pagination } from "@/components/pagination";
import { parseTenantPath } from "@/lib/tenant";
import { User, UserAndVc } from "@workspace/db/crud/common/user";
import { usePathname, useRouter, useSearchParams } from "next/navigation";
import { UserTable } from "./user-table";

export const UserList = ({
  data,
  count,
  pageNumber,
  pageSize,
}: {
  data: UserAndVc[];
  count: number;
  pageNumber: number;
  pageSize: number;
}) => {
  const router = useRouter();
  const pathname = usePathname();
  const [tenantCd] = parseTenantPath(pathname);
  const searchParams = useSearchParams();

  const handlePageChange = (page: number) => {
    const params = new URLSearchParams(searchParams);
    params.set("page", page.toString());
    router.push(pathname + "?" + params.toString());
  };
  return (
    <div className="mt-8">
      <UserTable tenantCd={tenantCd} data={data} />
      <Pagination
        count={count}
        page={pageNumber}
        pageSize={pageSize}
        onPageChange={handlePageChange}
        className="mt-[10px]"
      />
    </div>
  );
};
