import { readUserV<PERSON><PERSON><PERSON><PERSON>, UserAndVc } from "@workspace/db/crud/common/user";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@workspace/ui/components/table";
import { format } from "date-fns";

export const UserDetailVc = async ({
  tenantCd,
  userData,
}: {
  tenantCd: string;
  userData: UserAndVc;
}) => {
  const result = (await readUserVcHistory(tenantCd, userData.user.userDid)).map(
    (item) => ({
      subject: "시민증",
      dscdVal: item.dscdVal,
      dscdCn: item.dscdCn,
      issuedDt: item.issuedDt,
      expireDt: item.expireDt,
    }),
  );

  return (
    <div>
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>인증항목</TableHead>
            <TableHead>상태</TableHead>
            <TableHead>발급일</TableHead>
            <TableHead>만료일</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {result.map((item, index) => (
            <TableRow
              key={index}
              className={!item.dscdVal ? "bg-green-50 font-semibold" : ""}
            >
              <TableCell>{item.subject}</TableCell>
              <TableCell>{item.dscdCn}</TableCell>
              <TableCell>
                {item.issuedDt && format(item.issuedDt, "yyyy-MM-dd HH:mm")}
              </TableCell>
              <TableCell>
                {item.expireDt && format(item.expireDt, "yyyy-MM-dd HH:mm")}
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
};
