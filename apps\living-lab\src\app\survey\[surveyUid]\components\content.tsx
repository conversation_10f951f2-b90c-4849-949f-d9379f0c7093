import { getSurveyInfo } from "@/actions/survey";
import { getSurveyStateName } from "@/app/survey/consts";
import { PostContent, PostInfo } from "@/components/common/post-contents";
import { formatAsDateTime } from "@/lib/datetime";
import { convertNewlineToJSX } from "@toss/react";
import {
  readSurvey,
  readSurveyItems,
} from "@workspace/db/crud/living-lab/survey";
import { SURVEY_STATE } from "@workspace/db/schema/living-lab/survey";
import { isActionError } from "@workspace/utils/consts/errors";
import dayjs, { Dayjs } from "dayjs";
import { notFound } from "next/navigation";
import { Suspense } from "react";
import { SurveyItemsForm } from "./survey-items-form";
import { SurveyItemsResult } from "./survey-items-result";

export const SurveyDetailContentSkeleton = () => {
  //TODO
  return null;
};

const SurveyInfos = async ({
  surveyUid,
  period,
}: {
  surveyUid: number;
  period: Dayjs[];
}) => {
  const startDate = formatAsDateTime(period[0]);
  const endDate = formatAsDateTime(period[1]);
  const periodText = `${startDate} ~ ${endDate}`;

  const info = await getSurveyInfo(surveyUid);
  if (isActionError(info)) return notFound();

  return (
    <>
      <PostInfo title="설문기간">{periodText}</PostInfo>
      <PostInfo title="설문대상">{info.tenantName} 시민</PostInfo>
      <PostInfo title="참여횟수">{info.count}</PostInfo>
    </>
  );
};

const SurveyItems = async ({
  tenantId,
  surveyUid,
  state,
}: {
  tenantId: string;
  surveyUid: number;
  state: string;
}) => {
  const items = await readSurveyItems(tenantId, surveyUid);
  return (
    <div className="p-[20px]">
      <div className="text-[12px] text-[#808C98]">설문 내용</div>
      {state === SURVEY_STATE.CLOSED ? (
        <SurveyItemsResult surveyUid={surveyUid} items={items} />
      ) : (
        <SurveyItemsForm surveyUid={surveyUid} items={items} />
      )}
    </div>
  );
};

export const SurveyDetailContent = async ({
  tenantCd,
  surveyUid,
}: {
  tenantCd: string;
  surveyUid: number;
}) => {
  const content = await readSurvey(tenantCd, surveyUid);
  if (!content) return notFound();

  const period = [dayjs(content.startDate), dayjs(content.endDate)];
  const badges = [content.category, getSurveyStateName(content.state, period)];
  return (
    <div>
      <PostContent
        badges={badges}
        name={content.name}
        depr={content.depr}
        title={content.title}
        headers={
          <Suspense>
            <SurveyInfos surveyUid={surveyUid} period={period} />
          </Suspense>
        }
      >
        {convertNewlineToJSX(content.content)}
      </PostContent>
      <SurveyItems
        tenantId={tenantCd}
        surveyUid={surveyUid}
        state={content.state}
      />
    </div>
  );
};
