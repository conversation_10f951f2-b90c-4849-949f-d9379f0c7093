"use client";

import { But<PERSON> } from "@workspace/ui/components/button";
import { cn } from "@workspace/ui/lib/utils";
import { ReactNode } from "react";

export const BottomButton = ({
  children,
  disabled,
  onClick,
  className,
}: {
  children: ReactNode;
  disabled?: boolean;
  onClick: () => void;
  className?: string;
}) => {
  return (
    <>
      <div className="fixed bottom-[0px] left-1/2 z-50 w-full max-w-[560px] -translate-x-1/2">
        <div className="h-[34px] w-full bg-gradient-to-t from-[#ffffff] to-[rgba(255,255,255,0)]"></div>
        <div className={cn("w-full bg-[#ffffff] p-[20px]", className)}>
          <Button
            className="h-[52px] w-full rounded-[10px] text-[16px] font-semibold transition-transform active:scale-[0.97]"
            onClick={onClick}
            disabled={disabled}
          >
            {children}
          </Button>
        </div>
      </div>
    </>
  );
};
