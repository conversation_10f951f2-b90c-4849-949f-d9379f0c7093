"use client";

import {
  getSurveyAnswers,
  getSurveyTextAnswers,
  submitSurveyAnswers,
} from "@/actions/survey";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { SurveyAnswers } from "@workspace/db/crud/living-lab/survey";
import { isActionError } from "@workspace/utils/consts/errors";

export const useQuerySurveyAnswer = (surveyUid: number) =>
  useQuery({
    queryKey: ["survey/answer", surveyUid],
    queryFn: async () => {
      const data = await getSurveyAnswers(surveyUid);
      if (isActionError(data)) return data;

      const result = data.reduce((acc, answer) => {
        const accData = acc.get(answer.itemUid);
        if (answer.select) {
          acc.set(
            answer.itemUid,
            accData ? [...accData, answer.select] : [answer.select],
          );
        } else {
          acc.set(answer.itemUid, answer.answer);
        }
        return acc;
      }, new Map());
      return result;
    },
  });

export const useMutationSurveyAnswers = (surveyUid: number) => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationKey: ["survey/answer", surveyUid],
    mutationFn: ({ answers }: { answers: SurveyAnswers }) =>
      submitSurveyAnswers(surveyUid, answers),
    onMutate: () => {
      queryClient.invalidateQueries({ queryKey: ["survey/answer", surveyUid] });
    },
  });
};

export const useQuerySurveyTextAnswers = (
  {
    surveyUid,
    itemUid,
  }: {
    surveyUid: number;
    itemUid: number;
  },
  enabled: boolean,
) => {
  return useQuery({
    queryKey: ["survey/answer", surveyUid, itemUid],
    queryFn: () => getSurveyTextAnswers(surveyUid, itemUid),
    enabled,
  });
};
