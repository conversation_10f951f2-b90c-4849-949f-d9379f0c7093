import "server-only";
import { and, eq, getMainDb } from "@workspace/db/drizzle";
import {
  petInfos,
  petOwners,
  petQrEntries,
} from "@workspace/db/schema/pet-pass/pet-pass";

export type PetOwnerInsert = typeof petOwners.$inferInsert;

export type PetInfo = typeof petInfos.$inferSelect;
export type PetInfoInsert = typeof petInfos.$inferInsert;

export type PetPassQrEntry = typeof petQrEntries.$inferSelect;
export type PetPassQrEntryInsert = typeof petQrEntries.$inferInsert;

export const addOwnerToDb = async (ownerInfo: PetOwnerInsert) => {
  const db = await getMainDb();
  await db.insert(petOwners).values(ownerInfo);
};

export const readPetsFromDb = async (userDid: string) => {
  const db = await getMainDb();
  const result = await db
    .select()
    .from(petInfos)
    .where(and(eq(petInfos.userDid, userDid), eq(petInfos.isValid, "Y")));
  return result;
};

export const addPetToDb = async (petInfo: PetInfoInsert) => {
  const db = await getMainDb();
  await db.insert(petInfos).values(petInfo);
};

export const syncPetsToDb = async (
  userDid: string,
  pets: Omit<PetInfoInsert, "userDid">[],
) => {
  const db = await getMainDb();
  await db.transaction(async (tx) => {
    await tx
      .update(petInfos)
      .set({ isValid: "N" })
      .where(eq(petInfos.userDid, userDid));
    await tx.insert(petInfos).values(pets.map((pet) => ({ userDid, ...pet })));
  });
};

export const deletePetFromDb = async (qrpIdx: number) => {
  const db = await getMainDb();
  await db
    .update(petInfos)
    .set({ isValid: "N" })
    .where(eq(petInfos.qrpIdx, qrpIdx));
};

export const readQrEntriesFromDb = async (userDid: string) => {
  const db = await getMainDb();
  const result = await db
    .select()
    .from(petQrEntries)
    .where(
      and(eq(petQrEntries.userDid, userDid), eq(petQrEntries.isValid, "Y")),
    );
  return result;
};

export const addQrEntryToDb = async (
  userDid: string,
  entries: Omit<PetPassQrEntryInsert, "userDid">[],
) => {
  const db = await getMainDb();
  await db.transaction(async (tx) => {
    await tx
      .update(petQrEntries)
      .set({ isValid: "N" })
      .where(eq(petQrEntries.userDid, userDid));
    await tx.insert(petQrEntries).values(
      entries.map((entry, index) => ({
        userDid,
        ...entry,
      })),
    );
  });
};

export const deleteQrEntryFromDb = async (qrpIdx: number) => {
  const db = await getMainDb();
  await db
    .update(petQrEntries)
    .set({ isValid: "N" })
    .where(eq(petQrEntries.qrpIdx, qrpIdx));
};
