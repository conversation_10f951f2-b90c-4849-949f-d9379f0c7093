import Header from "@/components/common/header";
import { getSession } from "@workspace/utils/auth/session";
import { redirect, RedirectType } from "next/navigation";
import { Suspense } from "react";
import { GuidePanel } from "../components/common";
import { SuggestContent, SuggestContentSkeleton } from "./content";
import { WriteComment } from "./write-comment";

export default async function SuggestContentPage({
  params,
}: {
  params: Promise<{ suggestUid: string }>;
}) {
  const session = await getSession();
  if (!session) redirect("/", RedirectType.replace);
  const { suggestUid: uid } = await params;
  const suggestUid = Number.parseInt(uid);
  if (Number.isNaN(suggestUid)) {
    redirect("/suggest", RedirectType.replace);
  }

  return (
    <main className="flex max-h-screen min-h-screen flex-col bg-[#FFFFFF]">
      <Header title="정책제안 상세보기" parent="/suggest" />
      <div className="flex flex-1 overflow-y-hidden">
        <div className="mb-[80px] flex flex-1 flex-col overflow-y-auto">
          <GuidePanel type="suggest" />
          <Suspense fallback={<SuggestContentSkeleton />}>
            <SuggestContent uid={suggestUid} />
          </Suspense>
        </div>
      </div>
      <WriteComment suggestUid={suggestUid} />
    </main>
  );
}
