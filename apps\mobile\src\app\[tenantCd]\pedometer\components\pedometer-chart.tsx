"use client";

import { ChartConfig, ChartContainer } from "@workspace/ui/components/chart";
import { <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, XAxis } from "recharts";

const chartConfig: ChartConfig = {
  steps: {},
} satisfies ChartConfig;

export const PedometerChart = ({
  data,
}: {
  data: { key: string; steps: number }[];
}) => {
  return (
    <ChartContainer config={chartConfig} className="h-full w-full">
      <BarChart data={data} margin={{ top: 20 }}>
        <XAxis
          dataKey="key"
          tickLine={false}
          tickMargin={8}
          axisLine={false}
          // tickFormatter={(value) => value.slice(0, 3)}
        />
        <Bar
          dataKey="steps"
          fill="rgba(var(--primary), 1)"
          radius={9}
          barSize={12}
        >
          <LabelList dataKey="steps" position="top" offset={8} fontSize={12} />
        </Bar>
      </BarChart>
    </ChartContainer>
  );
};
