{"name": "mobile", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack --port 3001", "build": "next build", "start": "next start --port 3001", "lint": "next lint"}, "dependencies": {"@hookform/resolvers": "catalog:", "@tanstack/react-query": "^5.59.19", "@toss/react": "^1.8.1", "@toss/utils": "^1.6.1", "@workspace/db": "workspace:*", "@workspace/ui": "workspace:*", "@workspace/utils": "workspace:*", "@workspace/webview-state": "workspace:*", "axios": "^1.9.0", "date-fns": "^4.1.0", "embla-carousel": "^8.6.0", "es-toolkit": "^1.36.0", "lucide-react": "^0.454.0", "next": "catalog:next", "react": "catalog:react19", "react-dom": "catalog:react19", "react-hook-form": "catalog:", "react-qr-code": "^2.0.15", "recharts": "^2.15.1", "usehooks-ts": "^3.1.1", "zod": "catalog:"}, "devDependencies": {"@types/node": "^20", "@types/react": "catalog:react19", "@types/react-dom": "catalog:react19", "@webview-bridge/types": "^1.7.7", "@workspace/eslint-config": "workspace:*", "@workspace/typescript-config": "workspace:*", "postcss": "catalog:", "tailwindcss": "catalog:", "typescript": "catalog:"}}