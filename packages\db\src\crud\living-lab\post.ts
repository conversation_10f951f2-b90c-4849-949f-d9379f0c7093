import "server-only";
import {
  and,
  desc,
  eq,
  getTenantDb,
  gte,
  isNull,
  lte,
  min,
  or,
} from "@workspace/db/drizzle";
import {
  Post,
  PostFile,
  postFiles,
  posts,
} from "@workspace/db/schema/living-lab/post";
import {
  betweenYear,
  increment,
  now,
  withPagination,
} from "@workspace/db/utils";
import dayjs from "dayjs";

export const readPostsMinYear = async (tenantId: string, board: string) => {
  const tenantDb = await getTenantDb(tenantId);
  const result = await tenantDb
    .select({ minDate: min(posts.regDate) })
    .from(posts)
    .where(and(eq(posts.board, board), eq(posts.isValid, "Y")));
  return Number.parseInt(dayjs(result[0].minDate ?? new Date()).format("YYYY"));
};

export const readPosts = async ({
  tenantCd,
  board,
  category,
  year,
  page,
  pageSize,
}: {
  tenantCd: string;
  board: string;
  category?: string;
  year?: number;
  page?: number;
  pageSize?: number;
}) => {
  const tenantDb = await getTenantDb(tenantCd);
  const query = tenantDb
    .select({ post: posts, files: postFiles })
    .from(posts)
    .leftJoin(
      postFiles,
      and(eq(postFiles.postUid, posts.uid), eq(postFiles.isValid, "Y")),
    )
    .where(
      and(
        eq(posts.board, board),
        category ? eq(posts.category, category) : undefined,
        betweenYear(posts.regDate, year),
        or(isNull(posts.startDate), lte(posts.startDate, now())),
        or(isNull(posts.endDate), gte(posts.endDate, now())),
        eq(posts.isValid, "Y"),
      ),
    )
    .orderBy(desc(posts.regDate));

  const result = await (page || pageSize
    ? withPagination(query.$dynamic(), page, pageSize)
    : query);

  const resultMap = result.reduce((acc, row) => {
    const postItem = row.post;
    const post = acc.get(postItem.uid) ?? {
      ...postItem,
      files: [],
    };
    if (row.files) {
      post.files.push(row.files);
    }
    acc.set(postItem.uid, post);
    return acc;
  }, new Map<number, Post & { files: PostFile[] }>());

  return Array.from(resultMap.values());
};

export const readPost = async (
  tenantId: string,
  board: string,
  postUid: number,
  filterValid: boolean = true,
) => {
  const tenantDb = await getTenantDb(tenantId);
  const result = await tenantDb
    .select()
    .from(posts)
    .where(
      and(
        eq(posts.board, board),
        eq(posts.uid, postUid),
        ...(filterValid
          ? [
              or(isNull(posts.startDate), lte(posts.startDate, now())),
              or(isNull(posts.endDate), gte(posts.endDate, now())),
              eq(posts.isValid, "Y"),
            ]
          : []),
      ),
    );

  const files = await tenantDb
    .select()
    .from(postFiles)
    .where(
      and(
        eq(postFiles.postUid, postUid),
        isNull(postFiles.postUid),
        filterValid ? eq(postFiles.isValid, "Y") : undefined,
      ),
    );

  return result.length > 0 ? { ...result[0], files } : null;
};

export const increasePostHitCount = async (
  tenantId: string,
  board: string,
  postUid: number,
) => {
  const tenantDb = await getTenantDb(tenantId);
  await tenantDb
    .update(posts)
    .set({ hitCount: increment(posts.hitCount) })
    .where(and(eq(posts.board, board), eq(posts.uid, postUid)));
};
