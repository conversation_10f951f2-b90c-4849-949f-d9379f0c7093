"use client";

import { formatAsDateTime } from "@/lib/datetime";
import { useGlobalStore } from "@/lib/store/store-provider";
import { <PERSON><PERSON> } from "@toss/utils";
import { SuggestCommentDto } from "@workspace/db/crud/living-lab/suggest";
import { Button } from "@workspace/ui/components/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@workspace/ui/components/dropdown-menu";
import { Skeleton } from "@workspace/ui/components/skeleton";
import { cn } from "@workspace/ui/lib/utils";
import { ChevronDown, ChevronUp, Ellipsis, Heart } from "lucide-react";
import { useCallback, useMemo, useState } from "react";
import { useShallow } from "zustand/shallow";
import { ImageFiles } from "../components/common";
import {
  useMutationCommentVotes,
  useQueryComments,
  useQueryCommentVotes,
} from "../query";
import { useSuggestStore } from "../store-provider";

type CommentButtonProps = {
  uid: number;
  commentUid: number;
  onPressComment: (uid: number, commentUid: number) => void;
};

const CommentButton = ({
  uid,
  commentUid,
  onPressComment,
}: CommentButtonProps) => {
  return (
    <Button
      variant="ghost"
      className="text-[12px] font-semibold text-[#808C98]"
      onClick={() => onPressComment(uid, commentUid)}
    >
      답글쓰기
    </Button>
  );
};

const EditButton = ({
  uid,
  commentUid,
  onPressEdit,
  onPressDelete,
}: {
  uid: number;
  commentUid: number;
  onPressEdit: (uid: number, commentUid: number) => void;
  onPressDelete: (uid: number, commentUid: number) => void;
}) => {
  return (
    <DropdownMenu>
      <DropdownMenuTrigger>
        <Ellipsis className="h-[24px] w-[24px]" />
      </DropdownMenuTrigger>
      <DropdownMenuContent>
        <DropdownMenuItem onClick={() => onPressEdit(uid, commentUid)}>
          수정
        </DropdownMenuItem>
        <DropdownMenuItem onClick={() => onPressDelete(uid, commentUid)}>
          삭제
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

const Recomments = ({
  uid,
  commentUid,
  count,
}: {
  uid: number;
  commentUid: number;
  count: number | undefined;
}) => {
  const [showComments, setShowComments] = useState<boolean>(false);
  const { data: votesData } = useQueryCommentVotes({ uid });

  if (!count) return null;
  const toggle = () => setShowComments((prev) => !prev);

  return (
    <div>
      <Button
        variant="none"
        className="flex h-[18px] text-[12px]"
        size="none"
        onClick={toggle}
      >
        {showComments ? (
          <ChevronDown className="mr-[4px] h-[18px] w-[18px]" />
        ) : (
          <ChevronUp className="mr-[4px] h-[18px] w-[18px]" />
        )}
        <span className="text-[#808C98]">답글 {count}</span>
      </Button>
      {showComments && (
        <CommentItems
          uid={uid}
          parentUid={commentUid}
          order="oldest"
          votes={votesData}
        />
      )}
    </div>
  );
};

export const CommentItem = ({
  uid,
  parentUid,
  item,
  voted,
}: {
  uid: number;
  parentUid: number | null;
  item: SuggestCommentDto;
  voted: boolean | undefined;
}) => {
  const baseURL = useGlobalStore(useShallow((state) => state.baseURL));
  const [addComment, editComment] = useSuggestStore(
    useShallow((state) => [state.addComment, state.editComment]),
  );

  const onPressEdit = useCallback(
    (uid: number, item: SuggestCommentDto) => {
      editComment(uid, {
        commentUid: item.uid,
        content: item.content,
        files: item.files.map((f) => ({
          uid: f.uid,
          uri: f.uri,
          originalName: f.originalName,
        })),
      });
    },
    [editComment],
  );

  const deleteComment = (suggestUid: number, commentUid: number) => {
    //TODO: delete comment
    console.log(suggestUid, commentUid);
  };

  const { mutateAsync } = useMutationCommentVotes({ uid, parentUid });

  const handleVote = useCallback(
    async (commentUid: number, vote: number) => {
      mutateAsync({ commentUid, vote: vote ? 1 : 0 });
    },
    [mutateAsync],
  );

  return (
    <div key={item.uid} className="mb-[10px] flex flex-col">
      <div className="flex flex-row">
        <div className="mr-[14px]">
          <div className="h-[40px] w-[40px] bg-slate-400"></div>
        </div>
        <div className="flex-1 flex-col">
          <div className="flex items-center justify-between">
            <div className="text-[12px] font-medium">
              {Masker.maskName(item.name)}
            </div>
            <EditButton
              uid={uid}
              commentUid={item.uid!}
              onPressEdit={() => onPressEdit(uid, item)}
              onPressDelete={deleteComment}
            />
          </div>
          <div className="mb-[8px] text-[14px]">
            <ImageFiles baseURL={baseURL} files={item.files} />
            {item.content}
          </div>
          <div className="flex flex-row items-center text-[12px] text-[#B6BDC4]">
            <span>{formatAsDateTime(item.regDate)}</span>
            {!parentUid && (
              <CommentButton
                uid={uid}
                commentUid={item.uid!}
                onPressComment={addComment}
              />
            )}
            <div className="ml-auto flex flex-row items-center justify-center text-right">
              <Button
                variant="none"
                size="icon"
                className="h-[20px] w-[20px]"
                onClick={() => handleVote(item.uid!, voted ? 0 : 1)}
              >
                <Heart
                  className={cn(
                    "h-[20px] w-[20px]",
                    voted ? "text-primary" : "text-[#B6BDC4]",
                  )}
                />
              </Button>
              <span className="min-w-[14px] text-center text-[12px]">
                {item.upCount}
              </span>
            </div>
          </div>
          <Recomments
            uid={uid}
            commentUid={parentUid ?? item.uid!}
            count={item.commentCount}
          />
        </div>
      </div>
    </div>
  );
};

export const CommentItems = ({
  uid,
  parentUid,
  votes,
  order,
}: {
  uid: number;
  parentUid: number | null;
  votes: number[] | undefined;
  order: "latest" | "oldest";
}) => {
  const { data, isLoading } = useQueryComments({ uid, commentUid: parentUid });
  const orderedData = useMemo(
    () =>
      data?.sort((a, b) => {
        if (order === "latest") return b.uid! - a.uid!;
        return a.uid! - b.uid!;
      }),
    [data, order],
  );

  if (isLoading) return <CommentItemSkeleton />;

  return orderedData?.map((r) => (
    <CommentItem
      key={r.uid}
      uid={uid}
      parentUid={parentUid}
      item={r}
      voted={votes?.includes(r.uid!)}
    />
  ));
};

export const CommentItemSkeleton = () => {
  return (
    <div className="mb-[10px] flex flex-col">
      <div className="flex flex-row">
        <div className="mr-[14px]">
          <Skeleton className="h-[40px] w-[40px]" />
        </div>
        <div className="flex-1">
          <Skeleton className="mb-[8px] h-[12px] w-[100px]" />
          <Skeleton className="mb-[8px] h-[14px] w-full" />
          <Skeleton className="mb-[8px] h-[14px] w-[80%]" />
          <div className="flex flex-row items-center">
            <Skeleton className="h-[12px] w-[60px]" />
            <Skeleton className="ml-auto h-[12px] w-[40px]" />
          </div>
        </div>
      </div>
    </div>
  );
};
