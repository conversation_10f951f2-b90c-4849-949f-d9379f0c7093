"use client";

import { DateRange } from "@/lib/utils";
import { DatePicker } from "@workspace/ui/components/custom/date-picker";
import { format } from "date-fns";

export const applyDateRangeParams = (
  target: URLSearchParams,
  name: string,
  dateRange: DateRange | undefined,
) => {
  if (!dateRange) return;
  if (dateRange[0]) {
    target.set(`${name}From`, format(dateRange[0], "yyyy-MM-dd"));
  }
  if (dateRange[1]) {
    target.set(`${name}To`, format(dateRange[1], "yyyy-MM-dd"));
  }
};

export const DateRangePicker = ({
  dateRange,
  maxDate,
  disableUnset = false,
  onChange,
}: {
  dateRange?: DateRange;
  maxDate?: Date;
  disableUnset?: boolean;
  onChange: (value: DateRange) => void;
}) => {
  if (
    dateRange &&
    dateRange[0] &&
    dateRange[1] &&
    dateRange[0] > dateRange[1]
  ) {
    dateRange = [dateRange[1], dateRange[0]];
  }
  const handleTimeRangeChange = (value: DateRange) => {
    onChange(value);
  };

  const fromDate =
    dateRange && dateRange[0] ? new Date(dateRange[0]) : undefined;
  const toDate = dateRange && dateRange[1] ? new Date(dateRange[1]) : undefined;

  console.log(fromDate, toDate);
  return (
    <div className="flex items-center gap-2">
      <DatePicker
        date={fromDate}
        onChange={(date) => {
          if (disableUnset && !date) return;
          handleTimeRangeChange([date, toDate]);
        }}
        placeholder="yyyy.mm.dd"
        toDate={toDate}
      />
      -
      <DatePicker
        date={toDate}
        onChange={(date) => {
          if (disableUnset && !date) return;
          handleTimeRangeChange([fromDate, date]);
        }}
        placeholder="yyyy.mm.dd"
        fromDate={fromDate}
        toDate={maxDate}
      />
    </div>
  );
};
