import Header from "@/components/common/header";
import { getSession } from "@workspace/utils/auth/session";
import { redirect, RedirectType } from "next/navigation";
import { Suspense } from "react";
import { PollDetailContent } from "./components/content";

const PollDetailContentSkeleton = () => {
  return null;
};

export default async function PollDetailContentPage({
  params,
}: {
  params: Promise<{ pollUid: string }>;
}) {
  const session = await getSession();
  if (!session) redirect("/", RedirectType.replace);
  const { tenantCd, userDid } = session;

  const { pollUid } = await params;
  const uid = Number.parseInt(pollUid);
  if (Number.isNaN(uid)) {
    redirect("/poll", RedirectType.replace);
  }

  return (
    <main className="flex max-h-screen min-h-screen flex-col bg-[#FFFFFF]">
      <Header title="투표 내용 상세보기" parent="/poll" />
      <Suspense fallback={<PollDetailContentSkeleton />}>
        <PollDetailContent
          tenantCd={tenantCd}
          userDid={userDid}
          pollUid={uid}
        />
      </Suspense>
    </main>
  );
}
