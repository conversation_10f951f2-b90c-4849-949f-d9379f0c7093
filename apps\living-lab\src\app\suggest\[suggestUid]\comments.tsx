"use client";

import { Button } from "@workspace/ui/components/button";
import { cn } from "@workspace/ui/lib/utils";
import { useState } from "react";
import { useQueryCommentVotes } from "../query";
import { CommentItems } from "./comment-item";

export const SuggestComments = ({
  uid,
  count,
}: {
  uid: number;
  count: number;
}) => {
  const [order, setOrder] = useState<number>(0);
  const { data } = useQueryCommentVotes({ uid });
  return (
    <div>
      <div className="flex h-[44px] flex-row items-center justify-between py-[10px] text-[12px]">
        <div className="text-[12px]">
          <span className="text-[#808C98]">댓글 </span> {count}
        </div>
        <div>
          {["최신순", "등록순"].map((text, index) => (
            <Button
              key={index}
              variant="none"
              className={cn(
                "px-[5px] py-[8px] text-[12px] font-semibold",
                order === index && "text-primary",
              )}
              onClick={() => setOrder(index)}
            >
              {text}
            </Button>
          ))}
        </div>
      </div>
      <CommentItems
        uid={uid}
        parentUid={null}
        order={order === 0 ? "latest" : "oldest"}
        votes={data}
      />
    </div>
  );
};
