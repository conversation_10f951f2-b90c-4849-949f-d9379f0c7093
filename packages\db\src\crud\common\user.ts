import "server-only";
import {
  and,
  count,
  desc,
  eq,
  getTenantDb,
  isMainTenant,
  like,
  or,
  sql,
} from "@workspace/db/drizzle";
import { users, userVcs } from "@workspace/db/schema/common/user";
import { betweenDate, coalesce, withPagination } from "@workspace/db/utils";
import { firstOrNull } from "@workspace/utils/array";
import { endOfMonth, startOfMonth } from "date-fns";

export type User = typeof users.$inferSelect;
export type UserAndVc = { user: User; vc?: typeof userVcs.$inferSelect | null };

export type UserSearchFilter = {
  searchType?: string;
  keyword?: string;
  gender?: string;
  vc?: string;
  vcDt?: [Date | undefined, Date | undefined];
  joinDt?: [Date | undefined, Date | undefined];
  lastLgnDt?: [Date | undefined, Date | undefined];
  userStts?: string;
};

const createUserFilters = (filters: UserSearchFilter | undefined) => {
  const {
    searchType,
    keyword,
    gender,
    joinDt,
    lastLgnDt,
    vc,
    userStts = "NORMAL",
  } = filters ?? {};

  const keywordFilter = keyword
    ? searchType === "all"
      ? or(
          like(users.userDid, `%${keyword}%`),
          like(users.userNm, `%${keyword}%`),
          like(users.telnoEnc, `%${keyword}%`),
          like(users.brdt, `%${keyword}%`),
        )
      : searchType === "userDid"
        ? like(users.userDid, `%${keyword}%`)
        : searchType === "userNm"
          ? like(users.userNm, `%${keyword}%`)
          : searchType === "birth"
            ? like(users.brdt, `%${keyword}%`)
            : undefined
    : undefined;

  return and(
    keywordFilter,
    userStts ? eq(users.userStts, userStts) : undefined,
    gender ? eq(users.gender, gender) : undefined,
    joinDt ? betweenDate(users.joinDt, joinDt) : undefined,
    lastLgnDt ? betweenDate(users.lastLgnDt, lastLgnDt) : undefined,
    vc
      ? eq(coalesce(userVcs.dscdVal, true), vc === "Y" ? false : true)
      : undefined,
  );
};

export const readUserByUserDid = async (
  tenantCd: string,
  userDid: string,
): Promise<UserAndVc | null> => {
  const tenantDb = await getTenantDb(tenantCd);
  if (isMainTenant(tenantCd)) {
    const result = await tenantDb
      .select({ user: users })
      .from(users)
      .where(eq(users.userDid, userDid))
      .limit(1)
      .then(firstOrNull);
    return result;
  }

  const result = await tenantDb
    .select({ user: users, vc: userVcs })
    .from(users)
    .where(eq(users.userDid, userDid))
    .leftJoin(
      userVcs,
      and(eq(users.userDid, userVcs.subjectId), eq(userVcs.dscdVal, false)),
    )
    .limit(1)
    .then(firstOrNull);
  console.log("readUserByUserDid query", result);
  return result;
};

export const readUserCount = async (
  tenantCd: string,
  userSearchFilter?: UserSearchFilter,
) => {
  const filters = createUserFilters(userSearchFilter);
  const tenantDb = await getTenantDb(tenantCd);

  if (isMainTenant(tenantCd)) {
    const result = await tenantDb.$count(users, filters);
    return result;
  }

  const result = await tenantDb
    .select({ count: count() })
    .from(users)
    .leftJoin(
      userVcs,
      and(eq(users.userDid, userVcs.subjectId), eq(userVcs.dscdVal, false)),
    )
    .where(filters);

  return result[0]?.count ?? 0;
};

export const readUsers = async (
  tenantCd: string,
  userSearchFilter: UserSearchFilter,
  page: number,
  pageSize: number,
) => {
  const tenantDb = await getTenantDb(tenantCd);
  const filters = createUserFilters(userSearchFilter);
  const query = isMainTenant(tenantCd)
    ? tenantDb
        .select({ user: users })
        .from(users)
        .where(filters)
        .orderBy(desc(users.joinDt))
    : tenantDb
        .select({ user: users, vc: userVcs })
        .from(users)
        .leftJoin(
          userVcs,
          and(eq(users.userDid, userVcs.subjectId), eq(userVcs.dscdVal, false)),
        )
        .where(filters)
        .orderBy(desc(users.joinDt));

  const result = await (page || pageSize
    ? withPagination(query.$dynamic(), page, pageSize)
    : query);

  return result;
};

export const readJoinedUserCountMonthly = async (
  tenantCd: string,
  range: [Date, Date],
) => {
  const tenantDb = await getTenantDb(tenantCd);
  const result = await tenantDb
    .select({
      yearMonth: sql<string>`DATE_TRUNC('month', ${users.joinDt})`,
      count: count(),
    })
    .from(users)
    .where(betweenDate(users.joinDt, range))
    .groupBy(sql`DATE_TRUNC('month', ${users.joinDt})`)
    .orderBy(sql`DATE_TRUNC('month', ${users.joinDt})`);

  return result;
};

export const readJoinedUserCountDaily = async (
  tenantCd: string,
  monthString: string,
): Promise<{ day: string; count: number }[]> => {
  const tenantDb = await getTenantDb(tenantCd);
  const date = new Date(monthString);
  const fromDate = startOfMonth(date);
  const toDate = endOfMonth(date);

  const result = await tenantDb
    .select({
      day: sql<string>`DATE_TRUNC('day', ${users.joinDt})`,
      count: count(),
    })
    .from(users)
    .where(betweenDate(users.joinDt, [fromDate, toDate]))
    .groupBy(sql`DATE_TRUNC('day', ${users.joinDt})`)
    .orderBy(sql`DATE_TRUNC('day', ${users.joinDt})`);

  return result;
};

export const readUserVcHistory = async (tenantCd: string, userDid: string) => {
  const tenantDb = await getTenantDb(tenantCd);
  const result = await tenantDb
    .select()
    .from(userVcs)
    .where(eq(userVcs.subjectId, userDid))
    .orderBy(desc(userVcs.issuedDt));

  return result;
};
