"use client";

import { PrimaryButton } from "@/components/button";
import { cn } from "@workspace/ui/lib/utils";
import { Routes, useNavigate } from "@workspace/webview-state";
import Image from "next/image";

const useMenuItems = () => {
  const { navigate } = useNavigate();
  return [
    {
      label: "포인트 전환",
      icon: "/assets/wallet/point-exchange.svg",
      children: [
        {
          label: "전환",
          icon: "/assets/wallet/point-send.svg",
          onPress: () => navigate(Routes.walletPointExport()),
        },
      ],
    },
    {
      label: "포인트 관리",
      icon: "/assets/wallet/point-manage.svg",
      children: [
        { label: "내역", onPress: () => navigate(Routes.walletPointHistory()) },
      ],
    },
    {
      icon: "/assets/wallet/point-use.svg",
      label: "포인트 사용",
      children: [
        {
          label: "사용하러 가기",
          icon: "/assets/wallet/point-use-white.svg",
          onPress: () => navigate(Routes.store()),
        },
      ],
    },
  ];
};

export const PointMenus = ({ className }: { className?: string }) => {
  const menuItems = useMenuItems();
  return (
    <div className={cn("flex flex-col gap-[12px]", className)}>
      {menuItems.map((menuItem, index) => {
        return (
          <div
            key={index}
            className="rounded-[10px] bg-[#ECF2FD] p-[12px] shadow-sm"
          >
            <div className="mb-[12px] flex items-center">
              <Image
                src={menuItem.icon}
                width={24}
                height={24}
                alt=""
                className="mr-[8px]"
              />
              <span className="text-[16px] font-semibold">
                {menuItem.label}
              </span>
            </div>
            <div className="flex">
              {menuItem.children?.map((item, index) => {
                return (
                  <div key={index} className="flex-1">
                    <PrimaryButton onClick={item.onPress}>
                      {item.label}
                    </PrimaryButton>
                  </div>
                );
              })}
            </div>
          </div>
        );
      })}
    </div>
  );
};
