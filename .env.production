TZ=Asia/Seoul
NODE_ENV=production
#DATABASE_URL="mysql://ubuntu:fhrxkdhrkfm1!@host.docker.internal:3306/citywallet_saas?charset=utf8mb4"
DATABASE_URL='postgresql://postgres:<EMAIL>:5432/citycube'
PLATFORM_REWARD_URL="http://**********:30200"
PLATFORM_USER_URL="http://**********:30300"
PLATFORM_SAAS_URL="https://**********:30500"
PLATFORM_PUSH_URL="http://**********:30700"
#PLATFORM_USER_URL="http://host.docker.interal:30300"

#mobile
PET_PASS_URL="https://dev-api.petpass.kr/v1"

# store
STORE_GIFTISHOW_URL="https://bizapi.giftishow.com/bizApi"
STORE_GIFTISHOW_ID="<EMAIL>"
STORE_GIFTISHOW_AUTH_CODE="REAL5e868633c88c4733838c334f36b0ff52"
STORE_GIFTISHOW_AUTH_TOKEN="8japtF/6I9sLhbBxm1s0MQ=="