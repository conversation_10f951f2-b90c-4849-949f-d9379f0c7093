import { headers } from "next/headers";

export type ClientInfo = {
  ip: string;
  agent: string;
};

export const getClientIP = async () => {
  const headersList = await headers();

  return (
    headersList.get("x-forwarded-for")?.split(",")[0]?.trim() ??
    headersList.get("x-real-ip") ??
    headersList.get("x-client-ip") ??
    headersList.get("remote-addr") ??
    headersList.get("x-forwarded-host") ??
    "127.0.0.1"
  );
};

export const getClientAgent = async () => {
  const headersList = await headers();
  return (
    headersList.get("user-agent") ??
    headersList.get("x-user-agent") ??
    "unknown"
  );
};

export const getClientInfo = async (): Promise<ClientInfo> => {
  const ip = await getClientIP();
  const agent = await getClientAgent();

  return { ip, agent };
};
