"use client";

import { BottomButton } from "@/components/common/bottom-button";
import { useOrderSession } from "@/components/common/session";
import { useRouter } from "next/navigation";
import { useCallback } from "react";
import { useSessionStorage } from "usehooks-ts";

export const OrderButton = ({ id }: { id: number }) => {
  const router = useRouter();
  const [, setOrderSession] = useOrderSession();

  const handlePurchase = useCallback(() => {
    const nonce = Math.random().toString(36).substring(2, 15);
    setOrderSession({ id, nonce });
    router.push(`/order/${id}?nonce=${nonce}`);
  }, [id, router]);

  return <BottomButton onClick={handlePurchase}>구매하기</BottomButton>;
};
