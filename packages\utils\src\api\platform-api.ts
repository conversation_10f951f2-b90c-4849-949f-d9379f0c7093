import { cache } from "react";
import { UserInfo } from "../auth/types";

export const fetchUserInfo = cache(async (token: string) => {
  const url = `${process.env.PLATFORM_USER_URL}/api/search`;
  console.log("Fetching user info from:", url);
  const response = await fetch(url, {
    method: "GET",
    headers: {
      Authorization: `Bearer ${token}`,
      "Content-Type": "application/json",
    },
  });

  if (!response.ok) {
    throw new Error("Failed to fetch user info");
  }

  const data = await response.json();
  return data as UserInfo;
});
