import { createStore } from "zustand";

export type VoteType = "single" | "multiple";
export type VoteStep = "vote" | "sign" | "result";

export type PollVoteState = {
  pollUid: number | null;
  type: VoteType;
  step: VoteStep;
  selectedItem: Set<number>;
};

export type PollVoteActions = {
  setPollUid: (pollUid: number) => void;
  setVoteType: (type: VoteType) => void;
  setStep: (step: VoteStep) => void;
  setSelectedItem: (uid: number, enabled: boolean) => void;
  reset: () => void;
};

export type PollVoteStore = PollVoteState & PollVoteActions;

export const createPollVoteStore = () =>
  createStore<PollVoteStore>((set) => ({
    pollUid: null,
    type: "single" as VoteType,
    step: "vote" as VoteStep,
    selectedItem: new Set(),
    setPollUid: (pollUid) => set({ pollUid }),
    setVoteType: (type) => set({ type }),
    setStep: (step) => set({ step }),
    setSelectedItem: (uid, enabled) => {
      set((state) => {
        const selectedItem =
          state.type === "single"
            ? new Set<number>()
            : new Set(state.selectedItem);
        if (enabled) {
          selectedItem.add(uid);
        } else {
          selectedItem.delete(uid);
        }
        return { selectedItem };
      });
    },
    reset: () =>
      set({
        pollUid: null,
        step: "vote",
        selectedItem: new Set(),
      }),
  }));
