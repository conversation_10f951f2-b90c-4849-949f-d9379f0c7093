import { PrimaryButton, SecondaryButton } from "@/components/button";
import { isMainTenantCd } from "@/consts/consts";
import {
  Sheet,
  SheetContent,
  SheetTitle,
} from "@workspace/ui/components/sheet";
import { Routes, useBridgeLoose, useNavigate } from "@workspace/webview-state";
import Image from "next/image";
import { useCallback } from "react";

export const IdChangeSheet = ({
  tenantCd,
  open,
  onOpenChange,
}: {
  tenantCd: string;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}) => {
  const { loose } = useBridgeLoose();
  const { navigate, dismissTo } = useNavigate();

  const isMainTenant = isMainTenantCd(tenantCd);

  const handleAuth = useCallback(() => {
    navigate(Routes.citizenId({ origin: "/id-card" }));
    onOpenChange(false);
  }, [navigate]);

  const handleRevoke = useCallback(async () => {
    if (isMainTenant) return;
    await loose.revokeVc();
    await dismissTo({ url: "cw.app://main/home" }, true);
    onOpenChange(false);
  }, [tenantCd, dismissTo]);

  return (
    <Sheet modal={true} open={open} onOpenChange={onOpenChange}>
      <SheetContent side="bottom" className="rounded-t-[16px]">
        <SheetTitle className="text-[20px] font-medium">
          인증변경하기
        </SheetTitle>
        <div className="flex flex-col gap-[20px] py-[20px]">
          <PrimaryButton onClick={handleAuth}>
            <Image
              src={"/assets/id-card/btn-change.png"}
              width={20}
              height={20}
              alt="지역 변경"
            />
            거주지역 변경하기
          </PrimaryButton>
          {!isMainTenant && (
            <SecondaryButton onClick={handleRevoke}>
              <Image
                src={"/assets/id-card/btn-revoke.png"}
                width={20}
                height={20}
                alt="인증 해제"
              />
              인증 해제하기
            </SecondaryButton>
          )}
        </div>
      </SheetContent>
    </Sheet>
  );
};
