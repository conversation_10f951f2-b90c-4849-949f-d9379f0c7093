"use client";

import { PostSuggestItem } from "@/components/post-suggest/post-suggest-item";
import { useGlobalStore } from "@/lib/store/store-provider";
import { Suggest, SuggestFile } from "@workspace/db/crud/living-lab/suggest";
import { getImageURL } from "@workspace/utils/files";
import { MessageCircleMore, ThumbsDown, ThumbsUp } from "lucide-react";
import { useShallow } from "zustand/shallow";

const SuggestItemStats = ({
  upCount,
  downCount,
  commentCount,
}: {
  upCount: number;
  downCount: number;
  commentCount: number;
}) => {
  return (
    <div className="flex items-center text-[10px]">
      <span className="mr-2 flex items-center">
        <ThumbsUp className="mr-[3px] h-[16px] w-[16px] text-[#808C98]" />
        {upCount}
      </span>
      <span className="mr-2 flex items-center">
        <ThumbsDown className="mr-[3px] h-[16px] w-[16px] text-[#808C98]" />{" "}
        {downCount}
      </span>
      <span className="flex items-center">
        <MessageCircleMore className="mr-[3px] h-[16px] w-[16px] text-[#808C98]" />
        {commentCount}
      </span>
    </div>
  );
};

export const SuggestItem = ({
  item,
  className,
}: {
  item: Suggest & { files: SuggestFile[] };
  className?: string;
}) => {
  const baseURL = useGlobalStore(useShallow((state) => state.baseURL));
  const imageURL =
    item.files.length > 0 ? getImageURL(baseURL, item.files[0].uri) : null;

  return (
    <PostSuggestItem
      href={`/suggest/${item.uid}`}
      category={item.category}
      title={item.title}
      imageURL={imageURL}
      hitCount={item.hitCount}
      regDate={item.regDate}
      stats={
        <SuggestItemStats
          upCount={item.upCount}
          downCount={item.downCount}
          commentCount={item.commentCount}
        />
      }
      className={className}
    />
  );
};
