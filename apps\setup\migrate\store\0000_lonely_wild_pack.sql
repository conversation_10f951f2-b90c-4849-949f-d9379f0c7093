CREATE TABLE "tb_store_delivery" (
	"id" bigint PRIMARY KEY GENERATED BY DEFAULT AS IDENTITY (sequence name "tb_store_delivery_id_seq" INCREMENT BY 1 MINVALUE 1 MAXVALUE 9223372036854775807 START WITH 1 CACHE 1),
	"order_detail_id" bigint NOT NULL,
	"time" timestamp DEFAULT NOW() NOT NULL,
	"count" integer DEFAULT 1 NOT NULL,
	"delivery_service" varchar(32),
	"transaction_id" varchar(64),
	"tracking_code" varchar(64),
	"status" smallint NOT NULL
);
--> statement-breakpoint
CREATE TABLE "tb_store_order_detail" (
	"id" bigint PRIMARY KEY GENERATED BY DEFAULT AS IDENTITY (sequence name "tb_store_order_detail_id_seq" INCREMENT BY 1 MINVALUE 1 MAXVALUE 9223372036854775807 START WITH 1 CACHE 1),
	"order_id" bigint NOT NULL,
	"product_id" bigint NOT NULL,
	"price" integer NOT NULL,
	"count" integer DEFAULT 1 NOT NULL
);
--> statement-breakpoint
CREATE TABLE "tb_store_order" (
	"id" bigint PRIMARY KEY GENERATED BY DEFAULT AS IDENTITY (sequence name "tb_store_order_id_seq" INCREMENT BY 1 MINVALUE 1 MAXVALUE 9223372036854775807 START WITH 1 CACHE 1),
	"user_did" varchar(255) NOT NULL,
	"status" smallint NOT NULL,
	"title" varchar(64) DEFAULT '' NOT NULL,
	"price" integer NOT NULL,
	"time" timestamp DEFAULT NOW() NOT NULL,
	"receiverName" varchar(32) NOT NULL,
	"receiverPhone" varchar(32) NOT NULL,
	"receiverAddress" varchar(100)
);
--> statement-breakpoint
CREATE TABLE "tb_store_payment" (
	"id" bigint PRIMARY KEY GENERATED BY DEFAULT AS IDENTITY (sequence name "tb_store_payment_id_seq" INCREMENT BY 1 MINVALUE 1 MAXVALUE 9223372036854775807 START WITH 1 CACHE 1),
	"user_did" varchar(255) NOT NULL,
	"order_id" bigint NOT NULL,
	"amount" integer NOT NULL,
	"time" timestamp DEFAULT NOW() NOT NULL,
	"status" smallint NOT NULL
);
--> statement-breakpoint
CREATE TABLE "tb_store_product_tag" (
	"product_id" bigint NOT NULL,
	"tag_id" bigint NOT NULL
);
--> statement-breakpoint
CREATE TABLE "tb_store_product" (
	"id" bigint PRIMARY KEY GENERATED BY DEFAULT AS IDENTITY (sequence name "tb_store_product_id_seq" INCREMENT BY 1 MINVALUE 1 MAXVALUE 9223372036854775807 START WITH 1 CACHE 1),
	"source_type" varchar(50) NOT NULL,
	"source_id" varchar(50) NOT NULL,
	"name" varchar(255) NOT NULL,
	"brand" varchar(32),
	"price" integer NOT NULL,
	"image_url" varchar(400) NOT NULL,
	"status" smallint NOT NULL,
	"created_time" timestamp DEFAULT NOW() NOT NULL,
	"modified_time" timestamp DEFAULT CURRENT_TIMESTAMP NOT NULL
);
--> statement-breakpoint
CREATE TABLE "tb_store_tag" (
	"id" bigint PRIMARY KEY GENERATED BY DEFAULT AS IDENTITY (sequence name "tb_store_tag_id_seq" INCREMENT BY 1 MINVALUE 1 MAXVALUE 9223372036854775807 START WITH 1 CACHE 1),
	"name" varchar(20) NOT NULL,
	CONSTRAINT "tb_store_tag_name_unique" UNIQUE("name")
);
