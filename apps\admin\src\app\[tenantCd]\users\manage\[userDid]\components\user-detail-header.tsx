import { <PERSON><PERSON> } from "@toss/utils";
import { User, UserAndVc } from "@workspace/db/crud/common/user";
import { Card } from "@workspace/ui/components/card";
import { Skeleton } from "@workspace/ui/components/skeleton";
import { ReactNode } from "react";

export const UserDetailHeaderSkeleton = () => {
  return (
    <div>
      <Card className="mb-4 flex flex-col gap-4 rounded-xl bg-white p-4 shadow">
        <div>
          <div className="mb-2 flex items-center text-xl font-semibold text-gray-900">
            <Skeleton className="h-7 w-16" />
          </div>
          <div className="text-sm text-gray-500">
            <Skeleton className="h-4 w-32" />
          </div>
        </div>
      </Card>
    </div>
  );
};

export const UserDetailHeader = ({
  userData,
  children,
}: {
  userData: UserAndVc;
  children?: ReactNode;
}) => {
  const { user } = userData;
  return (
    <div>
      <Card className="mb-4 flex flex-col gap-4 rounded-xl bg-white p-4 shadow">
        <div>
          <div className="flex items-center gap-2 text-xl font-semibold text-gray-900">
            {Masker.maskName(user.userNm)}
            <span className="text-sm font-normal text-gray-500">
              / {user.gender === "M" ? "남성" : "여성"} / {user.mblOt} /{" "}
              {Masker.maskPhoneNumber(user.telnoEnc)}
            </span>
          </div>
          <div className="text-sm text-gray-500">{user.userDid}</div>
        </div>
      </Card>
      <Card className="flex flex-col gap-4 rounded-xl bg-white p-4 shadow">
        {children}
      </Card>
    </div>
  );
};
