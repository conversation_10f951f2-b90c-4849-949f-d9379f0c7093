import { Tabs, TabsList, TabsTrigger } from "@workspace/ui/components/tabs";
import { cn } from "../lib/utils";

export const UnderbarTabs = ({
  items,
  value,
  tabClassName,
  onValueChange,
}: {
  items: { label: string; value: string }[];
  value: string;
  tabClassName?: string;
  onValueChange: (value: string) => void;
}) => {
  return (
    <Tabs value={value} onValueChange={onValueChange}>
      <TabsList className="h-auto w-full rounded-none bg-transparent">
        {items.map((item, index) => (
          <div key={index} className="flex flex-1 justify-center">
            <TabsTrigger
              value={item.value}
              className={cn(
                "data-[state=active]:border-b-primary data-[state=active]:text-primary rounded-none border-b-[2px] border-b-transparent bg-transparent p-[10px] text-[16px] font-normal text-[#808C98] shadow-none data-[state=active]:font-semibold data-[state=active]:shadow-none",
                tabClassName,
              )}
            >
              {item.label}
            </TabsTrigger>
          </div>
        ))}
      </TabsList>
    </Tabs>
  );
};
