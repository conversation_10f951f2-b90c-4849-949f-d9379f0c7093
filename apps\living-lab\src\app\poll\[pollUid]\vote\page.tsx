import { getSession } from "@workspace/utils/auth/session";
import { redirect, RedirectType } from "next/navigation";
import { PollVoteContainer } from "./components/content";
import { PollVoteContent } from "./components/poll-content";
import { PollSignContent } from "./components/poll-sign";

export default async function PollVotePage({
  params,
}: {
  params: Promise<{ pollUid: string }>;
}) {
  const session = await getSession();
  if (!session) redirect("/", RedirectType.replace);
  const { pollUid } = await params;
  const uid = Number.parseInt(pollUid);
  if (Number.isNaN(uid)) {
    redirect("/poll", RedirectType.replace);
  }
  const tenantCd = session.tenantCd;

  return (
    <main className="flex max-h-screen min-h-screen flex-col bg-[#FFFFFF]">
      <PollVoteContainer
        pollUid={uid}
        voteStep={<PollVoteContent tenantCd={tenantCd} pollUid={uid} />}
        signStep={<PollSignContent />}
      />
    </main>
  );
}
