"use client";

import { createContext, ReactNode, useContext, useRef } from "react";
import { useStore } from "zustand";
import { createPollVoteStore, PollVoteStore } from "./store";

type PollVoteStoreApi = ReturnType<typeof createPollVoteStore>;

export const PollVoteStoreContext = createContext<PollVoteStoreApi | undefined>(
  undefined,
);

export const PollStoreProvider = ({ children }: { children: ReactNode }) => {
  const storeRef = useRef<PollVoteStoreApi>(null);
  if (!storeRef.current) {
    storeRef.current = createPollVoteStore();
  }
  return (
    <PollVoteStoreContext.Provider value={storeRef.current}>
      {children}
    </PollVoteStoreContext.Provider>
  );
};

export const usePollStore = <T,>(selector: (store: PollVoteStore) => T): T => {
  const pollVoteStoreContext = useContext(PollVoteStoreContext);
  if (!pollVoteStoreContext) {
    throw new Error("store must be used within a StoreProvider");
  }
  return useStore(pollVoteStoreContext, selector);
};
