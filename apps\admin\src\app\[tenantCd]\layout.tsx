import { getSession } from "@/lib/session";
import { redirect } from "next/navigation";
import { ReactNode } from "react";
import {
  App<PERSON><PERSON>Container,
  App<PERSON>ontaine<PERSON>,
  AppHeader,
} from "./components/container";
import { Profile } from "./components/profile";
import { AppSidebar } from "./components/sidebar";
import { getNavItems } from "./menu-items";

export type TenantPageProps = {
  params: Promise<{ tenantCd: string }>;
  searchParams: Promise<{ [key: string]: string | string[] | undefined }>;
};

export type TenantLayoutProps = TenantPageProps & {
  children: ReactNode;
};

export default async function Layout({ params, children }: TenantLayoutProps) {
  const { tenantCd } = await params;
  const session = await getSession();
  const tenantInfo = session?.tenants?.find((t) => t.tenantCd === tenantCd);
  if (!session || !tenantInfo) redirect("/");

  const navItems = getNavItems(tenantInfo);

  return (
    <AppContainer session={session}>
      <AppHeader tenantName={tenantInfo.tenantNm ?? tenantInfo.tenantCd}>
        <Profile session={session} role={tenantInfo.role} />
      </AppHeader>
      <div className="flex">
        <AppSidebar items={navItems} />
        <AppBodyContainer>{children}</AppBodyContainer>
      </div>
    </AppContainer>
  );
}
