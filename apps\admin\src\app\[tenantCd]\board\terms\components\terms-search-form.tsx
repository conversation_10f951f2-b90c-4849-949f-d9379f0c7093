"use client";

import {
  applyDate<PERSON><PERSON>eP<PERSON><PERSON>,
  DateRangePicker,
} from "@/components/date-range-picker";
import { <PERSON><PERSON> } from "@workspace/ui/components/button";
import { Card } from "@workspace/ui/components/card";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
} from "@workspace/ui/components/form";
import { Input } from "@workspace/ui/components/input";
import { Label } from "@workspace/ui/components/label";
import {
  RadioGroup,
  RadioGroupItem,
} from "@workspace/ui/components/radio-group";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@workspace/ui/components/select";
import { format } from "date-fns";
import { useRouter } from "next/navigation";
import { useForm } from "react-hook-form";
import { z } from "zod";

const formSchema = z.object({
  searchType: z.string(),
  keyword: z.string().optional(),
  valYn: z.string(),
  mustYn: z.string(),
  regDt: z.tuple([z.date().optional(), z.date().optional()]).optional(),
  chgDt: z.tuple([z.date().optional(), z.date().optional()]).optional(),
});

export const TermsSearchForm = ({
  initialParams,
  isMainTenant,
}: {
  initialParams?: Record<string, any>;
  isMainTenant?: boolean;
}) => {
  const router = useRouter();
  const form = useForm<z.infer<typeof formSchema>>({
    values: {
      searchType: initialParams?.searchType ?? "all",
      keyword: initialParams?.keyword ?? "",
      valYn: initialParams?.valYn ?? "-",
      mustYn: initialParams?.mustYn ?? "-",
      regDt: initialParams?.regDt,
      chgDt: initialParams?.chgDt,
    },
  });

  const handleSubmit = form.handleSubmit((data) => {
    const searchParams = new URLSearchParams();
    if (data.keyword) {
      searchParams.set("searchType", data.searchType);
      searchParams.set("keyword", data.keyword);
    }
    if (data.valYn !== "-") {
      searchParams.set("valYn", data.valYn);
    }
    if (data.mustYn !== "-") {
      searchParams.set("mustYn", data.mustYn);
    }
    applyDateRangeParams(searchParams, "regDt", data.regDt);
    applyDateRangeParams(searchParams, "chgDt", data.chgDt);
    router.push(`?${searchParams.toString()}`);
  });

  return (
    <Card className="p-4">
      <Form {...form}>
        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="flex flex-wrap gap-4">
            <div className="flex min-w-[300px] flex-1 items-center gap-2">
              <Label className="w-20 text-sm font-medium">검색어</Label>
              <FormField
                control={form.control}
                name="searchType"
                render={({ field }) => (
                  <FormItem>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger className="w-24">
                          <SelectValue />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="all">전체</SelectItem>
                        <SelectItem value="title">제목</SelectItem>
                        <SelectItem value="contents">내용</SelectItem>
                      </SelectContent>
                    </Select>
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="keyword"
                render={({ field }) => (
                  <FormItem className="flex-1">
                    <FormControl>
                      <Input
                        {...field}
                        type="text"
                        placeholder="검색어를 입력하세요"
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
            </div>

            <div className="flex min-w-[300px] flex-1 items-center gap-2">
              <Label className="w-20 text-sm font-medium">사용여부</Label>
              <FormField
                control={form.control}
                name="valYn"
                render={({ field }) => (
                  <FormItem>
                    <FormControl>
                      <RadioGroup
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                        className="flex gap-4"
                      >
                        <div className="flex items-center space-x-1">
                          <RadioGroupItem value="-" id="val-all" />
                          <Label htmlFor="val-all" className="text-sm">
                            전체
                          </Label>
                        </div>
                        <div className="flex items-center space-x-1">
                          <RadioGroupItem value="Y" id="val-yes" />
                          <Label htmlFor="val-yes" className="text-sm">
                            사용
                          </Label>
                        </div>
                        <div className="flex items-center space-x-1">
                          <RadioGroupItem value="N" id="val-no" />
                          <Label htmlFor="val-no" className="text-sm">
                            미사용
                          </Label>
                        </div>
                      </RadioGroup>
                    </FormControl>
                  </FormItem>
                )}
              />
            </div>
          </div>
          <div className="flex flex-wrap gap-4">
            <div className="flex min-w-[300px] flex-1 items-center gap-2">
              <Label className="w-20 text-sm font-medium">등록일</Label>
              <div className="flex items-center gap-2">
                <FormField
                  control={form.control}
                  name="regDt"
                  render={({ field }) => (
                    <DateRangePicker
                      dateRange={field.value}
                      onChange={field.onChange}
                    />
                  )}
                />
              </div>
            </div>
            <div className="flex min-w-[300px] flex-1 items-center gap-2">
              <Label className="w-20 text-sm font-medium">필수여부</Label>
              <FormField
                control={form.control}
                name="mustYn"
                render={({ field }) => (
                  <FormItem>
                    <FormControl>
                      <RadioGroup
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                        className="flex gap-4"
                      >
                        <div className="flex items-center space-x-1">
                          <RadioGroupItem value="-" id="must-all" />
                          <Label htmlFor="must-all" className="text-sm">
                            전체
                          </Label>
                        </div>
                        <div className="flex items-center space-x-1">
                          <RadioGroupItem value="Y" id="must-y" />
                          <Label htmlFor="must-y" className="text-sm">
                            필수
                          </Label>
                        </div>
                        <div className="flex items-center space-x-1">
                          <RadioGroupItem value="N" id="must-n" />
                          <Label htmlFor="must-n" className="text-sm">
                            선택
                          </Label>
                        </div>
                      </RadioGroup>
                    </FormControl>
                  </FormItem>
                )}
              />
            </div>
          </div>

          <div className="flex flex-wrap gap-4">
            <div className="flex min-w-[300px] flex-1 items-center gap-2">
              <Label className="w-20 text-sm font-medium">수정일</Label>
              <div className="flex items-center gap-2">
                <FormField
                  control={form.control}
                  name="chgDt"
                  render={({ field }) => (
                    <DateRangePicker
                      dateRange={field.value}
                      onChange={field.onChange}
                    />
                  )}
                />
              </div>
            </div>
          </div>

          <div className="flex flex-wrap items-center gap-4">
            <div className="ml-auto flex gap-4">
              <Button
                type="button"
                variant="outline"
                className="px-6 py-2"
                onClick={() => router.push("./terms/create")}
              >
                약관 등록
              </Button>
              <Button type="submit" className="px-6 py-2">
                검색
              </Button>
            </div>
          </div>
        </form>
      </Form>
    </Card>
  );
};
