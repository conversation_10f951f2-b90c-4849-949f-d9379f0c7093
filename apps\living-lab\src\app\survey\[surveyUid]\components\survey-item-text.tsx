"use client";

import { SubmitButton } from "@/components/common/submit-button";
import { convertNewlineToJSX } from "@toss/react";
import { Button } from "@workspace/ui/components/button";
import { Skeleton } from "@workspace/ui/components/skeleton";
import { cn } from "@workspace/ui/lib/utils";
import { isActionError } from "@workspace/utils/consts/errors";
import { PlusIcon } from "lucide-react";
import { useRouter } from "next/navigation";
import { useState } from "react";
import { useQuerySurveyTextAnswers } from "../../query";

const SurveyItemAnswer = ({ text }: { text: string }) => {
  return (
    <div className="flex rounded-md border-[1px] p-[10px] text-[14px]">
      {convertNewlineToJSX(text)}
    </div>
  );
};

const SurveyItemAnswerSkeleton = () => {
  return (
    <div className="space-y-[12px]">
      <Skeleton className="h-[8px] w-[90%] bg-gray-300" />
      <Skeleton className="h-[8px] w-[20%] bg-gray-300" />
      <Skeleton className="h-[8px] w-[70%] bg-gray-300" />
    </div>
  );
};

export const SurveyItemResultText = ({
  surveyUid,
  itemUid,
}: {
  surveyUid: number;
  itemUid: number;
}) => {
  const [isShow, setShow] = useState(false);
  const { data, isLoading } = useQuerySurveyTextAnswers(
    { surveyUid, itemUid },
    isShow,
  );

  const result = isActionError(data) ? [] : data;

  return !isShow ? (
    <Button
      variant="ghost"
      size="none"
      className="text-primary flex h-[32px] w-full items-center justify-center text-center"
      onClick={() => setShow(true)}
    >
      <PlusIcon className={cn("mr-[8px] h-[20px] w-[20px]")} />
      기타 의견 보기
    </Button>
  ) : isLoading ? (
    <SurveyItemAnswerSkeleton />
  ) : result ? (
    <div className="space-y-[6px]">
      {result.map((r, index) => (
        <SurveyItemAnswer key={index} text={r.answer!} />
      ))}
    </div>
  ) : null;
};

export const SurveyResultCloseButton = () => {
  const route = useRouter();
  return <SubmitButton onClick={() => route.back()}>확인</SubmitButton>;
};
