"use client";

import React, {
  createContext,
  ReactNode,
  useContext,
  useEffect,
  useRef,
} from "react";
import { createStore, useStore } from "zustand";
import { useShallow } from "zustand/shallow";
import { BridgeProvider } from "./bridge-provider";

type GlobalState = {
  appScheme: string;
};

type GlobalActions = {
  setAppScheme: (appScheme: string) => void;
};

type GlobalStore = GlobalState & GlobalActions;

const initialState: GlobalState = {
  appScheme: "",
};

const createGlobalStore = (initialState: GlobalState) =>
  createStore<GlobalStore>((set) => ({
    ...initialState,
    setAppScheme: (appScheme) => {
      set({ appScheme });
    },
  }));

type GlobalStoreApi = ReturnType<typeof createGlobalStore>;

const GlobalStoreContext = createContext<GlobalStoreApi | undefined>(undefined);

const GlobalStoreProvider = ({
  appScheme,
  children,
}: {
  appScheme: string;
  children: ReactNode;
}) => {
  const storeRef = useRef<GlobalStoreApi>(null);
  if (!storeRef.current) {
    storeRef.current = createGlobalStore({ ...initialState, appScheme });
  }
  return (
    <GlobalStoreContext.Provider value={storeRef.current}>
      {children}
    </GlobalStoreContext.Provider>
  );
};

export const useGlobalStore = <T,>(selector: (store: GlobalStore) => T): T => {
  const globalStoreContext = useContext(GlobalStoreContext);
  if (!globalStoreContext) {
    throw new Error("store must be used within a StoreProvider");
  }
  return useStore(globalStoreContext, useShallow(selector));
};

export const GlobalBridgeProvider = ({
  appScheme,
  children,
}: {
  appScheme: string;
  children: ReactNode;
}) => {
  return (
    <BridgeProvider>
      <GlobalStoreProvider appScheme={appScheme}>
        {children}
      </GlobalStoreProvider>
    </BridgeProvider>
  );
};
