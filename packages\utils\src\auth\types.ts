export const SESSION_KEY = "session";

export type TenantInfo = {
  tenantCd: string;
  tenantNm: string;
};

export type UserInfo = {
  userNm: string;
  userDid: string;
  gender: string;
  brdt: string;
  mblOt: string;
  mblTelNo: string;
  userCi: string;
  tenants: TenantInfo[];
};

export type Role = "ADMIN" | "USER";

export type Session = UserInfo & {
  tenantCd: string;
  tenantNm: string;
  roles: Role[];
  platformToken?: string;
};
