import { MainContainer } from "@/components/main-container";
import { ServicesContent } from "./content";

interface ServicesPageProps {
  params: Promise<{ tenantCd: string }>;
  searchParams: Promise<{ category: string | string[] | undefined }>;
}

export default async function ServicesPage({
  params,
  searchParams,
}: Readonly<ServicesPageProps>) {
  const { tenantCd } = await params;
  const { category } = await searchParams;

  return (
    <MainContainer paddingTop paddingBottom>
      <ServicesContent
        tenantCd={tenantCd}
        initialCategory={category as string}
      />
    </MainContainer>
  );
}
