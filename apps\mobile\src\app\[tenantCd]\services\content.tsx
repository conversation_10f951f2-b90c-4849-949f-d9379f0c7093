"use client";

import { isMainTenantCd, MAIN_TENANT_CD } from "@/consts/consts";
import { SwitchCase } from "@toss/react";
import { UnderbarTabs } from "@workspace/ui/components/underbar-tabs";
import { useCallback, useState } from "react";
import { ServicesCommon } from "./components/services-common";
import { ServicesLocal } from "./components/services-local";

const getCategories = (tenantCd: string) => {
  if (isMainTenantCd(tenantCd)) {
    return [
      { label: "공통", value: "common" },
      { label: "로컬", value: "local" },
    ];
  }

  return [
    { label: "로컬", value: "local" },
    { label: "공통", value: "common" },
  ];
};

export const ServicesContent = ({
  tenantCd,
  initialCategory = getCategories(tenantCd)[0].value,
}: {
  tenantCd: string;
  initialCategory: string;
}) => {
  const categories = getCategories(tenantCd);

  const [category, setCategory] = useState<string>(
    isMainTenantCd(tenantCd)
      ? "common"
      : (categories.find((c) => c.value === initialCategory)?.value ??
          categories[0].value),
  );

  const handleCategory = useCallback(
    (value: string) => {
      if (isMainTenantCd(tenantCd) && value === "local") return;
      setCategory(value);
    },
    [category, tenantCd],
  );

  return (
    <div>
      <UnderbarTabs
        tabClassName="w-full"
        onValueChange={handleCategory}
        value={category}
        items={categories}
      />
      <div className="mt-[8px]">
        <SwitchCase
          caseBy={{
            common: <ServicesCommon tenantId={tenantCd} />,
            local: (
              <ServicesLocal
                tenantCd={tenantCd}
                onNotFound={() => {
                  setCategory(categories[0].value);
                }}
              />
            ),
          }}
          value={category}
        />
      </div>
    </div>
  );
};
