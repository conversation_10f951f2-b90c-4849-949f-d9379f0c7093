import "server-only";
import {
  and,
  count,
  countDistinct,
  desc,
  eq,
  getTenantDb,
  gte,
  inArray,
  isNull,
  lte,
  min,
  ne,
  or,
} from "@workspace/db/drizzle";
import {
  POLL_CATEGORY,
  POLL_STATE,
  pollFiles,
  pollItems,
  pollReferences,
  polls,
  pollVotes,
} from "@workspace/db/schema/living-lab/poll";
import {
  suggestFiles,
  suggests,
} from "@workspace/db/schema/living-lab/suggest";
import { betweenYear, now } from "@workspace/db/utils";
import { actionError, ErrorCode } from "@workspace/utils/consts/errors";
import dayjs from "dayjs";
import { Suggest, SuggestFile } from "./suggest";

export type Poll = typeof polls.$inferSelect;
export type PollItem = typeof pollItems.$inferSelect;
export type PollFile = typeof pollFiles.$inferSelect;

export const readPollMinYear = async (tenantId: string) => {
  const tenantDb = await getTenantDb(tenantId);
  const result = await tenantDb
    .select({ minDate: min(polls.regDate) })
    .from(polls)
    .where(eq(polls.isValid, "Y"));
  return Number.parseInt(dayjs(result[0].minDate ?? new Date()).format("YYYY"));
};

export const readPolls = async (
  tenantId: string,
  category: POLL_CATEGORY | undefined,
  year: number | undefined,
  isPollClosed: boolean,
) => {
  const tenantDb = await getTenantDb(tenantId);
  const result = await tenantDb
    .select()
    .from(polls)
    .where(
      and(
        category ? eq(polls.category, category) : undefined,
        betweenYear(polls.regDate, year),
        isPollClosed
          ? or(
              ne(polls.state, POLL_STATE.VOTING),
              gte(polls.startDate, now()),
              lte(polls.endDate, now()),
            )
          : and(
              eq(polls.state, POLL_STATE.VOTING),
              lte(polls.startDate, now()),
              gte(polls.endDate, now()),
            ),
        eq(polls.isValid, "Y"),
      ),
    )
    .orderBy(desc(polls.regDate));

  return result;
};

export const readPoll = async (
  tenantId: string,
  pollUid: number,
  filterValid: boolean = true,
) => {
  const tenantDb = await getTenantDb(tenantId);
  const result = await tenantDb
    .select()
    .from(polls)
    .where(
      and(
        eq(polls.uid, pollUid),
        filterValid ? eq(polls.isValid, "Y") : undefined,
      ),
    );

  const files = await tenantDb
    .select()
    .from(pollFiles)
    .where(
      and(
        eq(pollFiles.pollUid, pollUid),
        isNull(pollFiles.pollItemUid),
        filterValid ? eq(pollFiles.isValid, "Y") : undefined,
      ),
    )
    .orderBy(pollFiles.uid);
  return result.length > 0 ? { ...result[0], files } : null;
};

export const readPollReferences = async (tenantId: string, pollUid: number) => {
  const tenantDb = await getTenantDb(tenantId);
  const suggestUids = await tenantDb
    .select({ suggestUid: pollReferences.suggestUid })
    .from(pollReferences)
    .where(
      and(eq(pollReferences.pollUid, pollUid), eq(pollReferences.isValid, "Y")),
    )
    .then((rows) =>
      rows.map((row) => row.suggestUid).filter((uid) => uid !== null),
    );

  if (suggestUids.length === 0) return [];

  const result = await tenantDb
    .select({ suggest: suggests, files: suggestFiles })
    .from(suggests)
    .leftJoin(
      suggestFiles,
      and(
        eq(suggestFiles.suggestUid, suggests.uid),
        eq(suggestFiles.isValid, "Y"),
      ),
    )
    .where(and(inArray(suggests.uid, suggestUids), eq(suggests.isValid, "Y")));

  const suggestMap = result.reduce((acc, row) => {
    const suggest = acc.get(row.suggest.uid) ?? {
      ...row.suggest,
      files: [],
    };
    if (row.files) suggest.files.push(row.files);
    acc.set(row.suggest.uid, suggest);
    return acc;
  }, new Map<number, Suggest & { files: SuggestFile[] }>());

  return Array.from(suggestMap.values());
};

export const readPollItems = async (tenantId: string, pollUid: number) => {
  const tenantDb = await getTenantDb(tenantId);
  const result = await tenantDb
    .select({ pollItem: pollItems, file: pollFiles })
    .from(pollItems)
    .leftJoin(
      pollFiles,
      and(
        eq(pollFiles.pollUid, pollItems.pollUid),
        eq(pollFiles.pollItemUid, pollItems.uid),
        eq(pollFiles.isValid, "Y"),
      ),
    )
    .where(and(eq(pollItems.pollUid, pollUid), eq(pollItems.isValid, "Y")))
    .orderBy(pollItems.order);

  const pollMap = result.reduce((acc, row) => {
    const pollItem = row.pollItem;
    const poll = acc.get(pollItem.uid) ?? {
      ...pollItem,
      files: [],
    };
    if (row.file) poll.files.push(row.file);
    acc.set(pollItem.uid, poll);
    return acc;
  }, new Map<number, PollItem & { files: PollFile[] }>());
  return Array.from(pollMap.values());
};

export const readPollVoterCount = async (tenantId: string, pollUid: number) => {
  const tenantDb = await getTenantDb(tenantId);
  const result = await tenantDb
    .select({ count: countDistinct(pollVotes.userDid) })
    .from(pollVotes)
    .where(and(eq(pollVotes.pollUid, pollUid), eq(pollVotes.isValid, "Y")));
  return result[0].count;
};

export const readPollVoteCount = async (tenantId: string, pollUid: number) => {
  const tenantDb = await getTenantDb(tenantId);
  const result = await tenantDb
    .select({
      pollItemUid: pollVotes.pollItemUid,
      count: count(),
    })
    .from(pollVotes)
    .where(
      and(
        eq(pollVotes.pollUid, pollUid),
        eq(pollVotes.isValid, "Y"),
        eq(pollVotes.pollItemUid, pollVotes.pollItemUid),
      ),
    )
    .groupBy(pollVotes.pollItemUid);
  return result;
};
export const readPollVoted = async (
  tenantId: string,
  userDid: string,
  pollUid: number,
) => {
  const tenantDb = await getTenantDb(tenantId);
  const activeVotes = await tenantDb.$count(
    pollVotes,
    and(
      eq(pollVotes.pollUid, pollUid),
      eq(pollVotes.userDid, userDid),
      eq(pollVotes.isValid, "Y"),
    ),
  );
  return activeVotes > 0;
};

export const addPollVote = async (
  tenantCd: string,
  userDid: string,
  pollUid: number,
  pollItemUid: number[],
) => {
  const tenantDb = await getTenantDb(tenantCd);
  return await tenantDb.transaction(async (tx) => {
    const activePollCount = await tx.$count(
      polls,
      and(
        eq(polls.uid, pollUid),
        eq(polls.state, POLL_STATE.VOTING),
        eq(polls.isValid, "Y"),
        lte(polls.startDate, now()),
        gte(polls.endDate, now()),
      ),
    );
    if (activePollCount === 0) {
      return actionError(ErrorCode.POLL_CLOSED);
    }

    const activeVotes = await tx.$count(
      pollVotes,
      and(
        eq(pollVotes.pollUid, pollUid),
        eq(pollVotes.userDid, userDid),
        eq(pollVotes.isValid, "Y"),
      ),
    );
    if (activeVotes > 0) {
      return actionError(ErrorCode.POLL_VOTED_ALREADY);
    }
    await tx
      .insert(pollVotes)
      .values(
        pollItemUid.map((pollItemUid) => ({ pollUid, pollItemUid, userDid })),
      );
    return true;
  });
};
