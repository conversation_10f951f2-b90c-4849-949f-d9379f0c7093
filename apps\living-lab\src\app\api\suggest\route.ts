import { addSuggest } from "@workspace/db/crud/living-lab/suggest";
import { getSession } from "@workspace/utils/auth/session";
import { NextResponse } from "next/server";
import { parseSuggestForm } from "./common";

export async function POST(request: Request) {
  const session = await getSession();
  if (!session) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  try {
    const { category, title, content, savedFiles } =
      await parseSuggestForm(request);

    const suggestUid = await addSuggest(
      session.tenantCd,
      {
        userDid: session.userDid,
        name: session.userNm,
        category,
        title,
        content,
      },
      savedFiles,
    );
    return NextResponse.json({ suggestUid });
  } catch (error) {
    console.error("Error processing upload:", error);
    return NextResponse.json({ error: "Upload failed" }, { status: 500 });
  }
}
