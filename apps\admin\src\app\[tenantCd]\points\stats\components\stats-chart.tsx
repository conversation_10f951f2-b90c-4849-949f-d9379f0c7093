"use client";

import { <PERSON>, <PERSON><PERSON>eader, CardTitle } from "@workspace/ui/components/card";
import {
  ChartConfig,
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from "@workspace/ui/components/chart";
import { endOfMonth, format, startOfMonth } from "date-fns";
import * as React from "react";
import { useMemo } from "react";
import { Bar, BarChart, CartesianGrid, XAxis } from "recharts";

const chartConfig = {
  sum: { label: "포인트 잔액", color: "#000033" },
} satisfies ChartConfig;

export const UserStatsChart = ({
  title,
  data,
}: {
  title?: string;
  data: { column: string; sum: number }[];
}) => {
  return (
    <Card className="w-full py-0">
      {title && (
        <CardHeader>
          <CardTitle>{title}</CardTitle>
        </CardHeader>
      )}
      <ChartContainer
        config={chartConfig}
        className="aspect-auto h-[250px] w-full"
      >
        <BarChart data={data}>
          <CartesianGrid vertical={false} />
          <XAxis
            dataKey="column"
            tickLine={false}
            tickMargin={10}
            axisLine={false}
          />
          <ChartTooltip cursor={false} content={<ChartTooltipContent />} />
          <Bar dataKey="sum" fill={chartConfig.sum.color} radius={4} />
        </BarChart>
      </ChartContainer>
    </Card>
  );
};

export const UserStatsMonthlyChart = ({
  title,
  dateRange,
  data,
}: {
  title?: string;
  dateRange: [Date, Date];
  data: { yearMonth: string; sum: number }[];
}) => {
  const chartData = useMemo(() => {
    const allMonths: { column: string; sum: number }[] = [];

    // 시작 월부터 끝 월까지 모든 월 생성
    const current = new Date(
      dateRange[0].getFullYear(),
      dateRange[0].getMonth(),
      1,
    );
    const end = new Date(
      dateRange[1].getFullYear(),
      dateRange[1].getMonth(),
      1,
    );

    while (current <= end) {
      const yearMonth = format(current, "yyyy-MM");
      const existingData = data.find((item) =>
        item.yearMonth.startsWith(yearMonth),
      );

      allMonths.push({
        column: format(current, "yyyy.MM"),
        sum: existingData?.sum || 0,
      });

      current.setMonth(current.getMonth() + 1);
    }

    return allMonths;
  }, [dateRange, data]);

  return <UserStatsChart title={title} data={chartData} />;
};

export const UserStatsDailyChart = ({
  title,
  month,
  data,
}: {
  title?: string;
  month: Date;
  data: { day: string; sum: number }[];
}) => {
  const chartData = useMemo(() => {
    const allDays: { column: string; sum: number }[] = [];
    const fromDate = startOfMonth(month);
    const toDate = endOfMonth(month);

    while (fromDate <= toDate) {
      const day = format(fromDate, "yyyy-MM-dd");
      const existingData = data.find((item) => item.day.startsWith(day));

      allDays.push({
        column: format(fromDate, "MM/dd"),
        sum: existingData?.sum || 0,
      });
      fromDate.setDate(fromDate.getDate() + 1);
    }

    return allDays;
  }, [month, data]);

  return <UserStatsChart title={title} data={chartData} />;
};
