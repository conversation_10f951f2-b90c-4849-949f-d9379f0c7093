import {
  deleteSuggest,
  readSuggest,
  updateSuggest,
} from "@workspace/db/crud/living-lab/suggest";
import { getSession } from "@workspace/utils/auth/session";
import { NextRequest, NextResponse } from "next/server";
import { parseSuggestForm } from "../common";

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ suggestUid: number }> },
) {
  const session = await getSession();
  if (!session) {
    return NextResponse.json(new Error("Unauthorized"), { status: 401 });
  }
  const { suggestUid } = await params;
  const currentData = await readSuggest(session.tenantCd, suggestUid);
  if (!currentData) {
    return NextResponse.json(new Error("Not Found"), { status: 404 });
  }
  if (currentData.userDid !== session.userDid) {
    return NextResponse.json(new Error("Forbidden"), { status: 403 });
  }

  try {
    const { category, title, content, savedFiles, activeFiles } =
      await parseSuggestForm(request);

    if (!title || !content) {
      return NextResponse.json({ error: "내용 오류" }, { status: 400 });
    }

    await updateSuggest(
      session.tenantCd,
      suggestUid,
      {
        name: session.userNm,
        userDid: session.userDid,
        category,
        title,
        content,
      },
      savedFiles,
      activeFiles,
    );
    return NextResponse.json({ suggestUid });
  } catch (error) {
    console.error("Error processing upload:", error);
    return NextResponse.json({ error: "Upload failed" }, { status: 500 });
  }
}

export async function DELETE(
  _request: NextRequest,
  { params }: { params: Promise<{ suggestUid: number }> },
) {
  const session = await getSession();
  if (!session) {
    return NextResponse.json(new Error("Unauthorized"), { status: 401 });
  }
  const { suggestUid } = await params;
  const currentData = await readSuggest(session.tenantCd, suggestUid);
  if (!currentData) {
    return NextResponse.json(new Error("Not Found"), { status: 404 });
  }
  if (currentData.userDid !== session.userDid) {
    return NextResponse.json(new Error("Forbidden"), { status: 403 });
  }

  try {
    await deleteSuggest(session.tenantCd, suggestUid);
    return NextResponse.json({ ok: true });
  } catch (error) {
    console.error("Error processing delete:", error);
    return NextResponse.json({ error: "Delete failed" }, { status: 500 });
  }
}
