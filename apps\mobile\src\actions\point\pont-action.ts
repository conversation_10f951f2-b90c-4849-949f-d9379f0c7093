"use server";

import { cache } from "react";
import { platformRewardApi } from "../platform-api";

export type PointBalanceType = {
  tenantCd: string;
  name: string;
  balance: number;
};

export const fetchPointBalance = cache(async () => {
  const { data } = await platformRewardApi.get<PointBalanceType[]>(
    "/api/search/balance",
  );
  return data;
});

export const fetchPointBalanceByTenantCd = cache(async (tenantCd: string) => {
  return fetchPointBalance().then((data) =>
    data.find((item) => item.tenantCd === tenantCd),
  );
});

export const fetchPointBalanceTotal = cache(async () => {
  const { data } = await platformRewardApi.get<{
    totalBalance: number;
  }>("/api/search/balance/total");
  return data;
});
