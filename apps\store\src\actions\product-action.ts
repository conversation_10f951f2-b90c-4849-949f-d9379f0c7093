"use server";

import { GiftishowApiService } from "@/api/giftishow/giftishow-api";
import { OrderRequest } from "@/types/order";
import { ProductDetailItem, ProductsFilter } from "@/types/product";
import {
  cancelPendingOrder,
  cancelPendingOrders,
  createOrder,
  readPendingOrdersTotalPrice,
  readProduct,
  readProducts,
  readProductsCount,
} from "@workspace/db/crud/store/store";
import { decryptJWE, encryptJWE } from "@workspace/utils/auth/jwt-utils";
import { getSession } from "@workspace/utils/auth/session";
import { UserApprovalResponseData } from "@workspace/webview-state/types";
import { format } from "date-fns";

type OrderPayload = {
  orderId: number;
  productId: number;
  sourceType: string;
  sourceId: string;
  nonce: string;
};

const giftishowApi = new GiftishowApiService();

const sources = [giftishowApi];

export const syncProducts = async () => {
  sources.forEach(async (source) => {
    await source.syncProducts();
  });
};

export const prunePendingOrders = async (
  dateBefore: Date = new Date(Date.now() - 10 * 60 * 1000),
) => {
  const dateBeforeText = format(dateBefore, "yyyy-MM-dd HH:mm:ss");
  await cancelPendingOrders(dateBeforeText);
};

export const fetchProductsCount = async (filter: ProductsFilter) => {
  return await readProductsCount(filter);
};

export const fetchProducts = async (filter: ProductsFilter) => {
  return await readProducts(filter);
};

export const fetchProduct = async (
  id: number,
): Promise<ProductDetailItem | null> => {
  const result = await readProduct(id);
  return result
    ? {
        ...result,
        deliver: giftishowApi.deliver,
        deliverMethod: giftishowApi.deliverMethod,
      }
    : null;
};

export const fetchProductDetail = async (
  sourceType: string,
  sourceId: string,
) => {
  const source = sources.find((s) => s.sourceType === sourceType);
  if (source) {
    return { data: await source.readProductDetail(sourceId) };
  } else {
    return { error: `지원하지 않는 상품입니다: ${sourceType}` };
  }
};

export const processPlaceOrder = async (model: OrderRequest) => {
  const session = await getSession();
  if (!session) {
    return { error: "세션이 유효하지 않습니다." };
  }

  // 이전 주문 요청 제거
  cancelPendingOrder({ userDid: session.userDid });

  const product = await readProduct(model.productId);
  if (!product) return { error: "상품 정보를 찾을 수 없습니다." };

  const source = sources.find((s) => s.sourceType === product.sourceType);
  if (!source) return { error: "지원하지 않는 상품입니다." };

  const pendingOrdersTotalPrice = await readPendingOrdersTotalPrice();
  let result;
  try {
    result = await source.readStockAvailability(
      product.sourceId,
      model.price,
      pendingOrdersTotalPrice,
    );
  } catch (error) {
    return { error: (error as Error).message };
  }
  if (!result) return { error: "현재 상품을 구매할 수 없습니다." };

  const orderId = await createOrder({
    userDid: session.userDid,
    title: model.name,
    price: model.price,
    receiverName: session.userNm,
    receiverPhone: session.mblTelNo,
    productId: product.id,
  });

  const payload: OrderPayload = {
    orderId: orderId,
    productId: product.id,
    sourceType: product.sourceType,
    sourceId: product.sourceId,
    nonce: model.nonce,
  };

  return { data: await encryptJWE(payload) };
};

export const processPayment = async (model: UserApprovalResponseData) => {
  if (model.type !== "payment")
    return { error: "유효하지 않은 결제 요청입니다." };
  const data = (await decryptJWE(model.payload)) as OrderPayload;
  if (!data) return { error: "유효하지 않은 결제 정보입니다." };
  if (!model.signature) {
    await cancelPendingOrder({ orderId: data.orderId });
    return { error: "결제 서명이 유효하지 않습니다." };
  }
  // TODO: 결제 서명 검증, 포인트 차감, 주문 처리
};
