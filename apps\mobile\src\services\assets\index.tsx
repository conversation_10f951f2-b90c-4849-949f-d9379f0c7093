import Image from "next/image";
import defaultAssets from "./default";
import gyept from "./gyept";
import { Assets, ImageAssetType } from "./types";

const assetsData: { [key: string]: () => Assets } = {
  gyept,
  main: defaultAssets,
};

const assets = (tenantCd: string) => {
  return (assetsData[tenantCd] || defaultAssets)();
};

export const ImageAsset = ({ src }: { src: ImageAssetType }) => {
  const { src: imageSrc, width, height, alt = "" } = src;
  return (
    <Image
      src={imageSrc}
      alt={alt}
      width={width}
      height={height}
      style={{ width, height }}
    />
  );
};

export default assets;
