import { decryptPlatformJWT } from "./auth/platform-auth";

describe("parseJWT", () => {
  it("should correctly parse JWT token", async () => {
    const token = `******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************`;
    const result = await decryptPlatformJWT(token);
    expect(result).toBeDefined();
  });
});
