import { cn } from "@workspace/ui/lib/utils";
import Image from "next/image";
import { ReactNode } from "react";

type CheckBoxStyle = "noframe" | "circle" | "circleNoFrame";

const CheckBoxImage = ({
  checked,
  style,
}: {
  checked: boolean;
  style: CheckBoxStyle;
}) => {
  const imageSource =
    style === "noframe"
      ? checked
        ? "/assets/images/check-noframe-on.png"
        : "/assets/images/check-noframe-off.png"
      : style === "circle" || style === "circleNoFrame"
        ? checked
          ? "/assets/images/check-circle-on.png"
          : "/assets/images/check-circle-off.png"
        : undefined;
  if (!imageSource) return null;
  const size = style == "noframe" ? 16 : 24;
  const className =
    style === "noframe" ? "h-[16px] w-[16px]" : "h-[24px] w-[24px]";

  return (
    <Image
      src={imageSource}
      className={className}
      alt={checked ? "체크됨" : "체크안됨"}
      width={size}
      height={size}
    />
  );
};

export const CheckBox = ({
  checked,
  style,
  children,
  detailChildren,
  onChange,
  className,
  containerClassName,
}: {
  checked: boolean;
  style: CheckBoxStyle;
  children: ReactNode;
  detailChildren?: ReactNode;
  onChange: (checked: boolean) => void;
  className?: string;
  containerClassName?: string;
}) => {
  const containerViewClass =
    style === "circle"
      ? cn("rounded-[10px] p-[10px]", checked ? "bg-lightblue" : "bg-gray4")
      : undefined;

  const checkboxViewClass =
    style === "noframe"
      ? "gap-[2px]"
      : style === "circle"
        ? "gap-[8px]"
        : undefined;

  return (
    <button onClick={() => onChange(!checked)}>
      <div className={cn(containerViewClass, containerClassName)}>
        <div
          className={cn(
            "flex flex-row items-center",
            checkboxViewClass,
            className,
          )}
        >
          <CheckBoxImage style={style} checked={checked} />
          {children}
        </div>
        {detailChildren}
      </div>
    </button>
  );
};
