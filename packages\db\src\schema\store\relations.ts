import { relations } from "drizzle-orm";
import { deliveries, orderDetails, orders, payments } from "./order";
import { products, productTags, tags } from "./product";

export const productsRelations = relations(products, ({ many }) => ({
  tagList: many(productTags),
  orderDetails: many(orderDetails),
}));

export const tagsRelations = relations(tags, ({ many }) => ({
  productList: many(productTags),
}));

export const ordersRelations = relations(orders, ({ many }) => ({
  detail: many(orderDetails),
  paymentList: many(payments),
}));

export const orderDetailsRelations = relations(
  orderDetails,
  ({ one, many }) => ({
    order: one(orders, {
      fields: [orderDetails.orderId],
      references: [orders.id],
    }),
    product: one(products, {
      fields: [orderDetails.productId],
      references: [products.id],
    }),
    delivery: many(deliveries),
  }),
);

export const paymentsRelations = relations(payments, ({ one }) => ({
  order: one(orders, {
    fields: [payments.orderId],
    references: [orders.id],
  }),
}));

export const deliveriesRelations = relations(deliveries, ({ one }) => ({
  orderDetail: one(orderDetails, {
    fields: [deliveries.orderDetailId],
    references: [orderDetails.id],
  }),
}));

export const productTagsRelations = relations(productTags, ({ one }) => ({
  product: one(products, {
    fields: [productTags.productId],
    references: [products.id],
  }),
  tag: one(tags, {
    fields: [productTags.tagId],
    references: [tags.id],
  }),
}));
