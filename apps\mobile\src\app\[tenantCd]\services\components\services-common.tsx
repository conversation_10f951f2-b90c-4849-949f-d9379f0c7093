"use client";

import {
  useGetLocalLoungeItems,
  useGetServiceItems,
} from "@/consts/menu-items";
import { ServiceList } from "./services-list";

export const ServicesCommon = ({ tenantId }: { tenantId: string }) => {
  const data = [useGetServiceItems(), useGetLocalLoungeItems(tenantId)];

  return (
    <div className="flex flex-col gap-[8px]">
      {data.map(
        (item, index) => item && <ServiceList key={index} data={item} />,
      )}
    </div>
  );
};
