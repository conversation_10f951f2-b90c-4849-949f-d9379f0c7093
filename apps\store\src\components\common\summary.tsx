import { ReactNode } from "react";

export type SummaryItemProps = {
  title?: string;
  children?: ReactNode;
};

export const SummaryItem = ({ title, children }: SummaryItemProps) => {
  return (
    <div className="flex items-center justify-between">
      {title ? (
        <span className="text-[14px] text-gray-500">{title}</span>
      ) : (
        <span />
      )}
      {children && (
        <span className="text-[14px] font-medium text-gray-800">
          {children}
        </span>
      )}
    </div>
  );
};
