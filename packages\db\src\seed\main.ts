import { drizzle } from "drizzle-orm/postgres-js";
import { seed } from "drizzle-seed";
import postgres from "postgres";
import { surveyItems, surveys } from "../schema/living-lab/survey";

async function main() {
  console.log(process.env.DATABASE_URL);
  const sql = postgres(process.env.DATABASE_URL!);
  const db = drizzle(sql);
  // await seed(db, { surveys, surveyItems }).refine((f) => ({
  //   surveys: {
  //     columns: {
  //       state: f.valuesFromArray({ values: ["I", "C", "X"] }),
  //     },
  //   },
  // }));
}

main();
