import { readPushTargetInfo } from "@workspace/db/crud/common/push";
import { readTermsAgreeData } from "@workspace/db/crud/common/terms";
import { UserAndVc } from "@workspace/db/crud/common/user";
import {
  Table,
  TableBody,
  TableCaption,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@workspace/ui/components/table";
import { format } from "date-fns";
import { Suspense } from "react";
import { UserDetailSubject } from "./user-detail-common";

const TermsTable = async ({ userDid }: { userDid: string }) => {
  const termsAgreeData = await readTermsAgreeData(userDid);
  return (
    <div>
      <UserDetailSubject>약관 동의 내역</UserDetailSubject>
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>약관명</TableHead>
            <TableHead>약관버전</TableHead>
            <TableHead>동의 여부</TableHead>
            <TableHead>동의 일시</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {termsAgreeData.map(({ termsAgree, termsData }, index) => (
            <TableRow key={`${termsAgree.termsCd}-${termsAgree.termsId}`}>
              <TableCell>{termsData?.termsTitle}</TableCell>
              <TableCell>{termsData?.termsVer}</TableCell>
              <TableCell>{termsAgree.agreVal ? "동의" : "비동의"}</TableCell>
              <TableCell>
                {format(termsAgree.agreDt, "yyyy.MM.dd HH:mm")}
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
};

const PushTable = async ({ userDid }: { userDid: string }) => {
  const pushTargetInfo = await readPushTargetInfo(userDid);
  if (!pushTargetInfo) {
    return <div>알림 설정 정보가 없습니다.</div>;
  }

  const getStatus = (enable: boolean | null) => {
    return enable === null ? "-" : enable ? "수신" : "수신 안함";
  };
  const getDate = (date: Date | null) => {
    return date ? format(date, "yyyy.MM.dd HH:mm") : "-";
  };

  return (
    <div>
      <UserDetailSubject>알림 설정</UserDetailSubject>
      {pushTargetInfo ? (
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>알림 종류</TableHead>
              <TableHead>수신 여부</TableHead>
              <TableHead>동의/거부일시</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            <TableRow>
              <TableCell>기본</TableCell>
              <TableCell>{getStatus(pushTargetInfo.notifYn)}</TableCell>
              <TableCell>{getDate(pushTargetInfo.notifDt)}</TableCell>
            </TableRow>
            <TableRow>
              <TableCell>혜택/이벤트</TableCell>
              <TableCell>{getStatus(pushTargetInfo.mktYn)}</TableCell>
              <TableCell>{getDate(pushTargetInfo.mktDt)}</TableCell>
            </TableRow>
          </TableBody>
        </Table>
      ) : (
        <div className="text-gray-500">알림 설정 정보가 없습니다.</div>
      )}
    </div>
  );
};

export const UserDetailTerms = async ({
  userData,
}: {
  userData: UserAndVc;
}) => {
  const userDid = userData.user.userDid;

  return (
    <div className="flex flex-col gap-4">
      <Suspense>
        <TermsTable userDid={userDid} />
      </Suspense>
      <Suspense>
        <PushTable userDid={userDid} />
      </Suspense>
    </div>
  );
};
