"use client";

import { Pagination } from "@/components/pagination";
import { PointPolicyItem } from "@workspace/db/crud/common/points";
import { usePathname, useRouter, useSearchParams } from "next/navigation";
import { PointTable } from "./point-table";

interface PolicyListProps {
  data: PointPolicyItem[];
  count: number;
  pageNumber: number;
  pageSize: number;
  onEditClick: (policy: PointPolicyItem) => void;
}

export const PolicyList = ({
  data,
  count,
  pageNumber,
  pageSize,
  onEditClick,
}: PolicyListProps) => {
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();

  const handlePageChange = (page: number) => {
    const params = new URLSearchParams(searchParams);
    params.set("page", page.toString());
    router.push(pathname + "?" + params.toString());
  };

  return (
    <div className="mt-8">
      <PointTable data={data} onEditClick={onEditClick} />
      <Pagination
        count={count}
        page={pageNumber}
        pageSize={pageSize}
        onPageChange={handlePageChange}
        className="mt-[10px]"
      />
    </div>
  );
};
