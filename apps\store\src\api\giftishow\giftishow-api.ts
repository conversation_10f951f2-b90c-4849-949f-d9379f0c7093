import assert from "assert";
import { convertTagToArray } from "@/types/category";
import { ProductStatus, upsertProducts } from "@workspace/db/crud/store/store";
import axios from "axios";
import { ProductApiService } from "../product-api";

enum ApiCodes {
  goods = "0101",
  productDetail = "0111",

  accountBalance = "0301",
}

type Goods = {
  goodsCode: string;
  goodsTypeDtlNm: string;
  goodsName: string;
  brandName?: string;
  brandIconImg: string;
  content: string;
  contentAddDesc: string;
  goodsImgB?: string;
  salePrice: number;
  goodsComName: string;
};

type GoodsResponse = {
  code: string;
  message: string | null;
  result?: {
    listNum: number;
    goodsList: Goods[];
  };
};

type ProductDetailResponse = {
  code: string;
  message: string | null;
  result: {
    goodsDetail: {
      salePrice: number;
      content: string;
      affiliate: string;
      limitDay: number;
      goodsStateCd: "SALE" | "SUS";
    };
  };
};

type AccountBalanceResponse = {
  code: string;
  message: string | null;
  result?: {
    balance: string;
  };
};

const giftishowApi = axios.create({
  baseURL: process.env.STORE_GIFTISHOW_URL,
});

const getParams = (apiCode: ApiCodes, params: Record<string, string> = {}) => {
  assert(
    process.env.STORE_GIFTISHOW_AUTH_CODE,
    "STORE_GIFTISHOW_AUTH_CODE is not defined",
  );
  assert(
    process.env.STORE_GIFTISHOW_AUTH_TOKEN,
    "STORE_GIFTISHOW_AUTH_TOKEN is not defined",
  );

  return new URLSearchParams({
    api_code: apiCode,
    custom_auth_code: process.env.STORE_GIFTISHOW_AUTH_CODE,
    custom_auth_token: process.env.STORE_GIFTISHOW_AUTH_TOKEN,
    dev_yn: "N",
    ...params,
  });
};

export class GiftishowApiService implements ProductApiService {
  readonly sourceType: string = "giftishow";
  readonly deliver: string = "1588-6474 (기프티쇼)";
  readonly deliverMethod: string = "MMS 메시지 즉시발송";

  private readProducts = async (
    pageIndex: number = 1,
    pageSize: number = 20,
  ) => {
    const { data } = await giftishowApi.post<GoodsResponse>(
      "goods",
      getParams(ApiCodes.goods, {
        start: pageIndex.toString(),
        size: pageSize.toString(),
      }),
    );
    return data;
  };

  private fetchProductsCount = async () => {
    const data = await this.readProducts();
    if (data.code !== "0000") {
      throw new Error(`Failed to fetch products: ${data.message}`);
    }
    return data.result?.listNum;
  };

  syncProducts = async () => {
    const count = await this.fetchProductsCount();
    const result = await this.readProducts(1, count);
    if (
      result.code !== "0000" ||
      !result.result ||
      result.result.goodsList.length < 1
    ) {
      throw new Error(`Failed to fetch products: ${result.message}`);
    }

    const items = result.result.goodsList.map((item) => {
      const tags = convertTagToArray(item.goodsTypeDtlNm);

      return {
        sourceId: item.goodsCode,
        name: item.goodsName,
        brand: item.brandName ?? item.goodsComName,
        price: item.salePrice,
        imageUrl: item.goodsImgB ?? item.brandIconImg,
        status: ProductStatus.FOR_SALE,
        tags,
      };
    });

    await upsertProducts(this.sourceType, items);
  };

  readProductDetail = async (sourceId: string) => {
    const { data } = await giftishowApi.post<ProductDetailResponse>(
      `goods/${sourceId}`,
      getParams(ApiCodes.productDetail),
    );
    const { goodsDetail } = data.result;
    return {
      price: goodsDetail.salePrice,
      content: goodsDetail.content.replaceAll("\n", "<br/>"),
      isValid: goodsDetail.goodsStateCd === "SALE",
      notes: [
        { title: "교환방식", content: "모바일 교환권" },
        { title: "교환처", content: goodsDetail.affiliate },
        { title: "유효기간", content: `${goodsDetail.limitDay}일` },
      ],
    };
  };

  readStockAvailability = async (
    sourceId: string,
    price: number,
    pendingOrdersTotalPrice: number,
  ) => {
    const productDetail = await this.readProductDetail(sourceId);
    if (!productDetail.isValid || productDetail.price !== price) {
      return false;
    }

    const { data: accountBalance } =
      await giftishowApi.post<AccountBalanceResponse>(
        "bizmoney",
        getParams(ApiCodes.accountBalance, {
          user_id: process.env.STORE_GIFTISHOW_ID ?? "",
        }),
      );

    if (!accountBalance.result) return false;

    const balance = Number(accountBalance.result.balance);
    if (
      isNaN(balance) ||
      balance < productDetail.price + pendingOrdersTotalPrice
    ) {
      throw new Error(
        "모바일 상품권 정산 작업으로 인해 상품권 발송이 취소되었습니다. 잠시 후 다시 시도하시거나 고객센터로 문의해주십시오.",
      );
    }

    return true;
  };
}
