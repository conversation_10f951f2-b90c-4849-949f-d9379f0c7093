import { OutlineButton } from "@/components/button";
import { Skeleton } from "@workspace/ui/components/skeleton";
import { cn } from "@workspace/ui/lib/utils";
import { formatBirth } from "@workspace/utils/string-utils";
import { Copy } from "lucide-react";
import Image from "next/image";
import { useState } from "react";
import {
  useMutationDeletePet,
  useQueryPetHairColors,
} from "../../actions/pet-query";
import { PetPassPetDto } from "../../actions/pet-types";
import { PetQrEntry } from "./pet-pass-qr-entry";

export const PetPassCardSkeleton = ({ className }: { className?: string }) => {
  return (
    <div
      className={cn(
        `overflow-clip rounded-[18px] bg-white shadow-lg`,
        className,
      )}
    >
      <div
        className="flex h-[140px] flex-col items-center justify-center bg-cover bg-no-repeat p-[12px]"
        style={{
          backgroundImage: "url('/assets/id-card/pet-pass/pet-pass-bg.png')",
        }}
      >
        <Skeleton className="h-[100px] w-[100px] rounded-full" />
      </div>
      <div className="p-[20px]">
        <div className="flex flex-row">
          <div className="flex w-full flex-row">
            <div className="flex flex-1 flex-col justify-around">
              <Skeleton className="h-[14px] w-[80px]" />
              <Skeleton className="mt-2 h-[24px] w-[120px]" />
              <Skeleton className="mt-2 h-[14px] w-[100px]" />
            </div>
            <div className="flex min-w-[100px] items-start justify-center">
              <Skeleton className="h-[100px] w-[100px]" />
            </div>
          </div>
        </div>
        <div className="mt-[20px] grid grid-cols-2 gap-x-[20px] gap-y-[10px] border-t-[1px] pt-[20px]">
          {/* 품종 */}
          <div>
            <Skeleton className="h-[14px] w-[40px]" />
            <Skeleton className="mt-1 h-[16px] w-[80px]" />
          </div>

          {/* 성별 */}
          <div>
            <Skeleton className="h-[14px] w-[40px]" />
            <Skeleton className="mt-1 h-[16px] w-[60px]" />
          </div>

          {/* 중성화 */}
          <div>
            <Skeleton className="h-[14px] w-[40px]" />
            <Skeleton className="mt-1 h-[16px] w-[70px]" />
          </div>

          {/* 생년월일 */}
          <div>
            <Skeleton className="h-[14px] w-[60px]" />
            <Skeleton className="mt-1 h-[16px] w-[80px]" />
          </div>

          {/* 국가동물등록번호 */}
          <div className="col-span-2">
            <Skeleton className="h-[14px] w-[120px]" />
            <div className="mt-1 flex items-center gap-2">
              <Skeleton className="h-[16px] w-[140px]" />
              <Skeleton className="h-[14px] w-[14px]" />
            </div>
          </div>
        </div>
        <div className="mt-[20px] flex gap-[10px]">
          <Skeleton className="h-[40px] w-[120px]" />
          <Skeleton className="h-[40px] w-[120px]" />
        </div>
      </div>
    </div>
  );
};

export const PetPassCard = ({
  data,
  className,
}: {
  data: PetPassPetDto;
  className?: string;
}) => {
  const [isDetail, setIsDetail] = useState(false);
  const { mutateAsync } = useMutationDeletePet();

  const { data: petColors } = useQueryPetHairColors({
    breedCode: data.petBreedCode,
  });

  const petColor = petColors?.data
    ? petColors.data.find(
        (item) => item.petHairColorCode === data.petHairColorCode,
      )
    : null;

  return (
    <div
      className={cn(
        `overflow-clip rounded-[18px] bg-white shadow-lg`,
        className,
      )}
    >
      <div
        className="flex h-[140px] flex-col items-center justify-center bg-cover bg-no-repeat p-[12px]"
        style={{
          backgroundImage: "url('/assets/id-card/pet-pass/pet-pass-bg.png')",
        }}
      >
        <Image
          src={"/assets/id-card/pet-pass/profile.png"}
          width={100}
          height={100}
          alt="프로필 사진"
        />
      </div>
      <div className="p-[20px]">
        <div className="flex flex-row">
          <div className="flex w-full flex-row">
            <div className="flex flex-1 flex-col justify-around">
              <div className="text-[14px] text-[#808c98]">반려동물신분증</div>

              <div className="text-[24px] font-semibold">{data.petName}</div>
              <div className="text-[14px] text-[#636f7a]">
                보호자 {data.ownName}
              </div>
            </div>
            <div className="flex min-w-[100px] items-start justify-center">
              <PetQrEntry />
            </div>
          </div>
        </div>
        <div className="mt-[20px] grid grid-cols-2 gap-x-[20px] gap-y-[10px] border-t-[1px] pt-[20px]">
          <div>
            <div className="text-[14px] text-[#808c98]">품종</div>
            <div className="text-[16px] font-medium text-[#000]">
              {data.petBreed}
            </div>
          </div>

          <div>
            <div className="text-[14px] text-[#808c98]">성별</div>
            <div className="text-[16px] font-medium text-[#000]">
              {data.petGender === "M" ? "남자아이" : "여자아이"}
            </div>
          </div>

          <div>
            <div className="text-[14px] text-[#808c98]">중성화</div>
            <div className="text-[16px] font-medium text-[#000]">
              {data.petNeuterYn === "Y" ? "중성화함" : "중성화안함"}
            </div>
          </div>

          <div>
            <div className="text-[14px] text-[#808c98]">생년월일</div>
            <div className="text-[16px] font-medium text-[#000]">
              {formatBirth(data.petBirth)}
            </div>
          </div>

          <div className="col-span-2">
            <div className="text-[14px] text-[#808c98]">국가동물등록번호</div>
            <div className="flex items-center gap-2 text-[16px] font-medium text-[#000]">
              {data.petRegNo}
              <Copy
                size={14}
                className="cursor-pointer"
                onClick={() => navigator.clipboard.writeText(data.petRegNo)}
              />
            </div>
          </div>
          {isDetail && (
            <>
              <div>
                <div className="text-[14px] text-[#808c98]">모색</div>
                <div className="text-[16px] font-medium text-[#000]">
                  {petColor?.petHairColorName}
                </div>
              </div>
              <div>
                <div className="text-[14px] text-[#808c98]">몸무게</div>
                <div className="text-[16px] font-medium text-[#000]">
                  {data.petWeight}kg
                </div>
              </div>
            </>
          )}
        </div>
        <div className="mt-[20px] flex gap-[10px]">
          <OutlineButton
            className="h-[40px]"
            onClick={() => setIsDetail(!isDetail)}
          >
            {isDetail ? "간략정보표시" : "상세정보표시"}
          </OutlineButton>
          <OutlineButton
            className="h-[40px]"
            onClick={() => {
              mutateAsync({ qrpIdx: data.qrpIdx });
            }}
          >
            삭제하기
          </OutlineButton>
        </div>
      </div>
    </div>
  );
};
