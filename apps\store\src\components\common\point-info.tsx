import { commaizeNumber } from "@toss/utils";
import React from "react";

export const PointInfo = ({ mileage }: { mileage: number | undefined }) => {
  return (
    <div>
      <div className="flex items-center justify-between border-b-[1px] border-[#000000]">
        <div>
          <h3 className="text-md font-bold text-[#292929]">
            교환가능한 포인트
          </h3>
        </div>
        <div className="flex items-end">
          <span className="text-lg font-bold text-[#292929]">
            {mileage !== undefined ? (
              commaizeNumber(mileage)
            ) : (
              <div className="inline-flex items-center">
                <div className="h-4 w-4 animate-spin rounded-full border-2 border-gray-300 border-t-gray-600"></div>
              </div>
            )}
          </span>
          <span className="ml-1 text-lg text-[#292929]">P</span>
        </div>
      </div>
      <div className="flex justify-between">
        <span className="text-[11px] text-[#858585]">
          ⓘ CITYCUBE 이용을 통해 적립된 마일리지 포인트
        </span>
      </div>
    </div>
  );
};
