import "@workspace/ui/globals.css";
import "@workspace/ui/globals-mobile.css";
import { getBaseURL } from "@/middleware";
import type { Metadata, Viewport } from "next";
import { GlobalStoreProvider } from "../lib/store/store-provider";
import Providers from "./providers";

export const metadata: Metadata = {
  title: "리빙랩",
  description: "리빙랩",
};

export const viewport: Viewport = {
  themeColor: "#ffffff",
  width: "device-width",
  initialScale: 1,
  maximumScale: 1,
  userScalable: false,
  viewportFit: "cover",
};

export default async function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const baseURL = await getBaseURL();
  return (
    <html lang="ko">
      <body>
        <GlobalStoreProvider>
          <Providers baseURL={baseURL}>{children}</Providers>
        </GlobalStoreProvider>
      </body>
    </html>
  );
}
