name: Docker Build and Push

on:
  push:
    branches:
      - main
    paths:
      - "apps/**"
      - "packages/**"
      - "Dockerfile"
      - "Dockerfile-setup"
      - ".github/workflows/docker-build.yml"
  workflow_dispatch:
    inputs:
      app:
        description: "Choose the app to build"
        required: true
        type: choice
        options:
          - admin
          - living-lab
          - mobile
          - setup
          - store

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}

jobs:
  changes:
    if: github.event_name != 'workflow_dispatch'
    runs-on: ubuntu-latest
    outputs:
      apps: ${{ steps.filter.outputs.changes }}
      has_changes: ${{ steps.check-changes.outputs.has_changes }}
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Get changed apps
        id: filter
        uses: dorny/paths-filter@v2
        with:
          filters: |
            admin:
              - 'apps/admin/**'
              - 'packages/**'
              - 'Dockerfile'
            living-lab:
              - 'apps/living-lab/**'
              - 'packages/**'
              - 'Dockerfile'
            mobile:
              - 'apps/mobile/**'
              - 'packages/**'
              - 'Dockerfile'
            setup:
              - 'apps/setup/**'
              - 'packages/**'
              - 'Dockerfile-setup'
            store:
              - 'apps/store/**'
              - 'packages/**'
              - 'Dockerfile'

      - name: Check if there are changes
        id: check-changes
        run: |
          CHANGES='${{ steps.filter.outputs.changes }}'
          if [ "$CHANGES" == "[]" ]; then
            echo "has_changes=false" >> $GITHUB_OUTPUT
          else
            echo "has_changes=true" >> $GITHUB_OUTPUT
          fi

  build-and-push:
    needs: [changes]
    if: >-
      always() &&
      (
        github.event_name == 'workflow_dispatch' ||
        (needs.changes.result == 'success' && needs.changes.outputs.has_changes == 'true')
      )
    runs-on: ubuntu-latest
    strategy:
      fail-fast: false
      matrix:
        app: ${{ github.event_name == 'workflow_dispatch' && fromJSON(format('["{0}"]', inputs.app)) || fromJSON(needs.changes.outputs.apps) }}
    permissions:
      contents: read
      packages: write

    steps:
      - name: Skip if no changes
        if: github.event_name != 'workflow_dispatch' && !contains(fromJSON(needs.changes.outputs.apps), matrix.app)
        run: echo "No changes detected for ${{ matrix.app }}" && exit 0

      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Log in to the Container registry
        uses: docker/login-action@v3
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Extract metadata for Docker
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}/${{ matrix.app }}
          tags: |
            type=ref,event=branch
            type=ref,event=pr
            type=semver,pattern={{version}}
            type=semver,pattern={{major}}.{{minor}}
            type=sha,prefix=
            type=raw,value=latest,enable={{is_default_branch}}

      - name: Build and push Docker image
        if: matrix.app != 'none'
        uses: docker/build-push-action@v5
        with:
          context: .
          file: ${{ matrix.app == 'setup' && 'Dockerfile-setup' || 'Dockerfile' }}
          push: true
          build-args: |
            PROJECT=${{ matrix.app }}
            NODE_ENV=production
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          cache-from: type=gha
          cache-to: type=gha,mode=max

  cleanup-old-images:
    needs: [build-and-push, changes]
    if: always() && needs.build-and-push.result == 'success'
    runs-on: ubuntu-latest
    strategy:
      matrix:
        app: ${{ github.event_name == 'workflow_dispatch' && fromJSON(format('["{0}"]', inputs.app)) || fromJSON(needs.changes.outputs.apps) }}
    permissions:
      contents: read
      packages: write
    steps:
      - name: Debug package name
        run: |
          echo "Repository: ${{ github.repository }}"
          echo "Image name: ${{ env.IMAGE_NAME }}"
          echo "App: ${{ matrix.app }}"
          echo "Package name: ${{ env.IMAGE_NAME }}/${{ matrix.app }}"

      - name: Delete old container images
        uses: actions/delete-package-versions@v5
        continue-on-error: true
        with:
          package-name: ${{ env.IMAGE_NAME }}/${{ matrix.app }}
          package-type: container
          min-versions-to-keep: 10
          delete-only-untagged-versions: false
          ignore-versions: '^(latest|main|develop)$'
