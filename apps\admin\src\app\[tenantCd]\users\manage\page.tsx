import { isMainTenant, MAIN_TENANT_CD } from "@/lib/tenant";
import { convertStringToDateRange } from "@/lib/utils";
import {
  readUserCount,
  readUsers,
  UserSearchFilter,
} from "@workspace/db/crud/common/user";
import { DataTableSkeleton } from "@workspace/ui/components/data-table";
import { Suspense } from "react";
import { AppContent } from "../../components/container";
import { TenantPageProps } from "../../layout";
import { UserList } from "./components/user-list";
import { UserSearchForm } from "./components/user-search-form";

const parseSearchParams = (search: {
  [x: string]: string | string[] | undefined;
}): UserSearchFilter => {
  if (!search) return {};
  const getString = (
    value: string | string[] | undefined,
  ): string | undefined => {
    if (Array.isArray(value)) return value[0];
    return value;
  };

  return {
    searchType: getString(search.searchType),
    keyword: getString(search.keyword),
    gender: getString(search.gender),
    vc: getString(search.vc),
    vcDt: convertStringToDateRange([
      getString(search.vcDtFrom),
      getString(search.vcDtTo),
    ]),
    joinDt: convertStringToDateRange([
      getString(search.joinDtFrom),
      getString(search.joinDtTo),
    ]),
  };
};

const UserManageContent = async ({
  tenantCd,
  searchParams,
  pageNumber,
  pageSize,
}: {
  tenantCd: string;
  searchParams: UserSearchFilter;
  pageNumber: number;
  pageSize: number;
}) => {
  const userCount = await readUserCount(tenantCd, searchParams);
  const users = await readUsers(tenantCd, searchParams, pageNumber, pageSize);

  return (
    <UserList
      data={users}
      count={userCount}
      pageNumber={pageNumber}
      pageSize={pageSize}
    />
  );
};

export default async function UserManagePage({
  params,
  searchParams,
}: TenantPageProps) {
  const { tenantCd } = await params;
  const { page = "1", ...search } = await searchParams;
  const initialParams = parseSearchParams(search);
  const pageNumber = parseInt(page as string, 10) ?? 1;
  const pageSize = 10;

  return (
    <AppContent title="회원 관리">
      <UserSearchForm
        initialParams={initialParams}
        isMainTenant={isMainTenant(tenantCd)}
      />
      <Suspense fallback={<DataTableSkeleton pageSize={pageSize} />}>
        <UserManageContent
          tenantCd={tenantCd}
          searchParams={initialParams}
          pageNumber={pageNumber}
          pageSize={pageSize}
        />
      </Suspense>
    </AppContent>
  );
}
