"use client";

import { Outline<PERSON>utton, PrimaryButton } from "@/components/button";
import { useVisibilityEffect } from "@/hooks/use-visibility-effect";
import { cn } from "@workspace/ui/lib/utils";
import { formatBirth, maskedAddress } from "@workspace/utils/string-utils";
import { Routes, useBridgeStore, useNavigate } from "@workspace/webview-state";
import { UserData } from "@workspace/webview-state/types";
import Image from "next/image";
import { useCallback, useState } from "react";
import { IdChangeSheet } from "./id-change-sheet";

const CitizenIdCard = ({
  masked,
  className,
}: {
  masked: boolean;
  className?: string;
}) => {
  const { navigate } = useNavigate();
  const [userData, setUserData] = useState<UserData | null>(null);
  const [appUserData, refreshUserData] = useBridgeStore((state) => [
    state.userData,
    state.refreshUserData,
  ]);

  useVisibilityEffect(async () => {
    setUserData(appUserData);
    try {
      const userData = await refreshUserData();
      setUserData(userData);
    } catch (error) {
      console.error("Error refreshing user data:", error);
    }
  });

  const handleAuth = useCallback(() => {
    navigate(Routes.citizenId({ origin: "/id-card" }));
  }, [navigate]);

  return (
    <div
      className={cn(
        `overflow-clip rounded-[18px] bg-white shadow-lg`,
        className,
      )}
    >
      <div
        className="flex h-[160px] flex-col items-center justify-center bg-cover bg-no-repeat p-[12px]"
        style={{
          backgroundImage: "url('/assets/default/citizen-id-bg.png')",
        }}
      >
        <Image
          src={"/assets/id-card/profile.png"}
          width={120}
          height={120}
          alt="프로필 사진"
        />
      </div>
      <div className="p-[20px]">
        <div className="flex flex-row">
          <div className="flex-[3]">
            <span className="text-[14px] font-medium">모바일 시민증</span>
          </div>
          <div className="flex-[4]">
            <div className="flex flex-col gap-[10px]">
              <OutlineButton
                className="border-primary text-[14px] font-medium"
                onClick={handleAuth}
              >
                <Image
                  src={"/assets/id-card/id-card.png"}
                  width={20}
                  height={20}
                  alt="인증하기"
                />
                인증하기
              </OutlineButton>
              <div className="flex flex-row gap-[10px]">
                {/* <OutlineButton>표출변경</OutlineButton> */}
                <OutlineButton className="text-[14px] font-medium">
                  인증이력
                </OutlineButton>
              </div>
            </div>
          </div>
        </div>
        <div className="mt-[20px] flex flex-row border-t-[1px] py-[20px]">
          <div className="flex flex-1 flex-col">
            <div className="text-[28px] font-semibold tracking-[16px]">
              {userData?.name && userData.name}
            </div>
            <div className="mt-[14px] text-[16px] font-medium">
              {userData?.birth && formatBirth(userData?.birth)}
            </div>
            <div className="mt-[14px] text-[16px]">
              {userData?.address
                ? masked
                  ? maskedAddress(userData.address)
                  : userData.address
                : null}
            </div>
          </div>
          <div className="flex min-w-[100px] items-start justify-center">
            <Image
              src={"/assets/id-card/qr-code.png"}
              width={100}
              height={100}
              alt="QR 코드"
            />
          </div>
        </div>
      </div>
    </div>
  );
};

const CitizenIdButtons = ({
  tenantCd,
  masked,
  setMasked,
  className,
}: {
  tenantCd: string;
  masked: boolean;
  setMasked: (masked: boolean) => void;
  className?: string;
}) => {
  const { navigate } = useNavigate();
  const [idChangeSheetOpen, setIdChangeSheetOpen] = useState<boolean>(false);
  const items = [
    {
      text: "지역/인증 변경",
      imageUrl: "/assets/id-card/btn-change.png",
      onPress: () => setIdChangeSheetOpen(true),
    },
    {
      text: "QR코드 촬영",
      imageUrl: "/assets/id-card/btn-qr.png",
      onPress: () => navigate(Routes.qrScan()),
    },
  ];
  return (
    <div className={cn("flex flex-row gap-[8px]", className)}>
      {items.map((item) => (
        <PrimaryButton
          key={item.text}
          className="flex-1"
          onClick={item.onPress}
        >
          <Image src={item.imageUrl} width={20} height={20} alt={item.text} />
          {item.text}
        </PrimaryButton>
      ))}
      <IdChangeSheet
        tenantCd={tenantCd}
        open={idChangeSheetOpen}
        onOpenChange={setIdChangeSheetOpen}
      />
    </div>
  );
};

export const CitizenIdPage = ({ tenantCd }: { tenantCd: string }) => {
  const [masked, setMasked] = useState(true);
  return (
    <div className="flex h-full flex-1 flex-col">
      <CitizenIdCard masked={masked} className="flex-1" />
      <CitizenIdButtons
        tenantCd={tenantCd}
        masked={masked}
        setMasked={setMasked}
        className="mt-[16px]"
      />
    </div>
  );
};
