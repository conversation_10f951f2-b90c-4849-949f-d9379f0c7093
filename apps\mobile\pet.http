@BASE_URL = https://dev-api.petpass.kr/v1

### PET PASS
GET {{BASE_URL}}/owners/1743332510823
Authorization: Bearer 3ZBMwkW21bU6CfaHkQhSJj4Z8+tsn4XQfhAqeuFjrNw=


### PET PASS
GET {{BASE_URL}}/owners/1234567890
Authorization: Bearer 3ZBMwkW21bU6CfaHkQhSJj4Z8+tsn4XQfhAqeuFjrNw=


### PET PASS (고양이)
GET {{BASE_URL}}/country-animal?pet_reg_no=410160010958170&own_name=김대철
Authorization: Bearer 3ZBMwkW21bU6CfaHkQhSJj4Z8+tsn4XQfhAqeuFjrNw=


### PET PASS (개)
GET {{BASE_URL}}/country-animal?pet_reg_no=410160011703745&own_name=김재준
Authorization: Bearer 3ZBMwkW21bU6CfaHkQhSJj4Z8+tsn4XQfhAqeuFjrNw=



### PET PASS (개)
GET {{BASE_URL}}/breeds
Authorization: Bearer 3ZBMwkW21bU6CfaHkQhSJj4Z8+tsn4XQfhAqeuFjrNw=

### PET PASS (등록)
POST {{BASE_URL}}/owners
Authorization: Bearer 3ZBMwkW21bU6CfaHkQhSJj4Z8+tsn4XQfhAqeuFjrNw=
Content-Type: application/json

{
    "own_ci":"D+m7BGkzsI1NH3dAWA+k3XcnJYXGWPKc3eYcNU/BqXMVR+VSMA5ggYavMPB6FUAnXpH8s1yP5Qz5AQN4ZwZjlw==",
    "own_code":"did:dxd:000081c3c9acc80c927d5b15ceac336ec4231040e3c1e19f8aa8",
    "own_name": "홍길동",
    "own_phone": "01023585807",
    "own_email": "<EMAIL>",
    "own_birth": "20020627",
    "own_gender": "M"
}
