"use client";

import { BottomButton } from "@/components/common/bottom-button";
import { useRouter } from "next/navigation";
import { ReactNode } from "react";

export const VoteButton = ({
  pollUid,
  disabled,
  children,
}: {
  pollUid: number;
  disabled?: boolean;
  children: ReactNode;
}) => {
  const router = useRouter();

  return (
    <BottomButton
      disabled={disabled}
      onClick={() => {
        router.push(`/poll/${pollUid}/vote`);
      }}
    >
      {children}
    </BottomButton>
  );
};
