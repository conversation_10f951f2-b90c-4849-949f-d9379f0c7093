{"name": "@workspace/ui", "version": "0.0.0", "private": true, "scripts": {"ui": "pnpm dlx shadcn@latest", "lint": "eslint . --max-warnings 0"}, "dependencies": {"@radix-ui/react-alert-dialog": "^1.1.13", "@radix-ui/react-avatar": "^1.1.2", "@radix-ui/react-checkbox": "^1.1.2", "@radix-ui/react-collapsible": "^1.1.2", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.2", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-popover": "^1.1.11", "@radix-ui/react-radio-group": "^1.2.1", "@radix-ui/react-select": "^2.1.2", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.1", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-toggle-group": "^1.1.10", "@radix-ui/react-tooltip": "^1.2.7", "@tanstack/react-table": "^8.20.6", "@tiptap/extension-color": "^2.22.3", "@tiptap/extension-heading": "^2.22.3", "@tiptap/extension-horizontal-rule": "^2.22.3", "@tiptap/extension-image": "^2.22.3", "@tiptap/extension-link": "^2.22.3", "@tiptap/extension-placeholder": "^2.22.3", "@tiptap/extension-text-style": "^2.22.3", "@tiptap/extension-typography": "^2.22.3", "@tiptap/extension-underline": "^2.22.3", "@tiptap/pm": "^2.22.3", "@tiptap/react": "^2.22.3", "@tiptap/starter-kit": "^2.22.3", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "date-fns": "^4.1.0", "embla-carousel-react": "^8.6.0", "lucide-react": "^0.454.0", "next-themes": "^0.4.6", "postcss": "catalog:", "postcss-load-config": "^6.0.1", "pretendard": "^1.3.9", "react-day-picker": "^8.10.1", "react-hook-form": "catalog:", "recharts": "^2.15.1", "sonner": "^2.0.5", "tailwind-merge": "^2.5.4", "tailwindcss-animate": "^1.0.7", "zustand": "^5.0.1"}, "devDependencies": {"@types/eslint": "catalog:", "@types/node": "catalog:", "@types/react": "catalog:react19", "@types/react-dom": "catalog:react19", "@workspace/eslint-config": "workspace:*", "@workspace/typescript-config": "workspace:*", "autoprefixer": "^10.4.20", "tailwindcss": "catalog:", "typescript": "catalog:"}, "peerDependencies": {"react": "catalog:react19"}, "exports": {"./globals.css": "./src/styles/globals.css", "./globals-mobile.css": "./src/styles/globals-mobile.css", "./postcss.config": "./postcss.config.mjs", "./tailwind.config": "./tailwind.config.ts", "./lib/*": "./src/lib/*.ts", "./hooks/*": ["./src/hooks/*.tsx", "./src/hooks/*.ts"], "./components/tiptap": "./src/components/tiptap/index.ts", "./components/*": "./src/components/*.tsx", "./providers/*": ["./src/providers/*.tsx", "./src/providers/*.ts"]}}