"use client";

import { checkUserId, signUp } from "@/actions/auth";
import { zodResolver } from "@hookform/resolvers/zod";
import { SaasTennant } from "@workspace/db/schema/common/saas";
import { Button } from "@workspace/ui/components/button";
import { Card, CardContent } from "@workspace/ui/components/card";
import { Form, FormField, FormMessage } from "@workspace/ui/components/form";
import { Input } from "@workspace/ui/components/input";
import { Label } from "@workspace/ui/components/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@workspace/ui/components/select";
import { redirect } from "next/dist/server/api-utils";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useCallback, useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";

const isProduction = process.env.NODE_ENV !== "development";

const formSchema = z
  .object({
    userId: z
      .string()
      .trim()
      .regex(/^[a-z0-9]+$/, {
        message: "아이디는 영문, 숫자만 사용할 수 있습니다",
      })
      .min(isProduction ? 4 : 1, { message: "아이디를 4자 이상 입력해주세요" }),
    userPwd: z
      .string()
      .min(isProduction ? 8 : 1, {
        message: `비밀번호는 ${isProduction ? 8 : 1}자 이상이어야 합니다`,
      })
      .max(72, { message: "비밀번호는 최대 72자까지 입력할 수 있습니다" }),
    userPwdConfirm: z.string(),
    userNm: z.string().min(1, { message: "이름을 입력해주세요" }),
    userEmail: z
      .string()
      .email({ message: "유효한 이메일 주소를 입력해주세요" }),
    userPhone: z
      .string()
      .min(11, { message: "전화번호를 정확히 입력해주세요" }),
    tenantCd: z.string().nonempty({ message: "지역코드를 선택해주세요" }),
    userDepr: z.string(),
  })
  .refine((data) => data.userPwd === data.userPwdConfirm, {
    message: "비밀번호가 일치하지 않습니다",
    path: ["userPwdConfirm"],
  });

export const SignUpForm = ({ tenants }: { tenants: SaasTennant[] }) => {
  const router = useRouter();
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      userId: "",
      userPwd: "",
      userNm: "",
      userEmail: "",
      userPhone: "",
      userPwdConfirm: "",
      tenantCd: "",
      userDepr: "",
    },
  });

  const [idTaken, setIdTaken] = useState<boolean | undefined>(undefined);
  const [phoneNumbers, setPhoneNumbers] = useState<string[]>(
    Array.from({ length: 3 }, () => ""),
  );

  const updatePhoneNumber = useCallback((index: number, value: string) => {
    const newValue = value.replace(/\D/g, "");
    setPhoneNumbers((prev) => {
      prev[index] = newValue;
      return [...prev];
    });
  }, []);

  useEffect(() => {
    form.setValue("userPhone", phoneNumbers.join(""));
  }, [phoneNumbers, form]);

  const handleCheckId = useCallback(async () => {
    const userId = form.getValues("userId");
    if (!userId) {
      form.setError("userId", {
        type: "manual",
        message: "아이디를 입력해주세요",
      });
      return;
    }
    form.clearErrors("userId");

    try {
      const result = await checkUserId(userId);
      setIdTaken(result.data);
    } catch (error) {
      console.error("아이디 중복 확인 오류:", error);
      form.setError("userId", {
        type: "manual",
        message: "아이디 중복 확인에 실패했습니다",
      });
    }
  }, [form]);

  const handleSubmit = form.handleSubmit(
    async (data: z.infer<typeof formSchema>) => {
      console.log(data);
      try {
        const result = await signUp(data);
        if (result.data) {
          alert(
            "회원 가입 신청이 완료되었습니다. 계정 활성화까지 시간이 소요될 수 있습니다.",
          );
          router.back();
        } else {
          alert(result.error ?? "회원가입에 실패했습니다. 다시 시도해주세요.");
        }
      } catch (e) {
        console.error(e);
        alert("회원가입 중 오류가 발생했습니다. 잠시 후 다시 시도해주세요.");
      }
    },
  );

  return (
    <div className="fixed inset-0 flex items-center justify-center overflow-y-auto bg-[#F6FAFF]">
      <Card className="w-[800px] overflow-hidden">
        <CardContent className="flex flex-row p-0">
          <div className="bg-primary flex w-[240px] flex-col items-center justify-center p-4 text-white">
            <div className="flex flex-grow flex-col items-center justify-center">
              <img
                src="/assets/login-logo.svg"
                className="mb-4"
                alt="CITY Wallet 로고"
              />
              <div className="text-[16px]">관리자 회원가입</div>
            </div>
            <div className="text-[12px] text-[#F6FAFF]">
              Copyright © Data-Alliance 2024
            </div>
          </div>
          <Form {...form}>
            <form onSubmit={handleSubmit} className="flex-grow-[3] p-[50px]">
              <div className="flex flex-col gap-6">
                <div className="flex flex-col items-center text-center">
                  <h1 className="mb-2 text-2xl font-bold">회원 가입</h1>
                  <p className="text-muted-foreground text-balance">
                    필수 정보를 입력해주세요.
                  </p>
                </div>
                <div className="text-end text-sm">
                  이미 회원이신가요?{" "}
                  <Link
                    href="/auth/login"
                    className="underline underline-offset-4"
                  >
                    로그인
                  </Link>
                </div>

                <div className="flex items-start gap-2">
                  <Label className="w-1/4 pt-2">아이디*</Label>
                  <div className="flex w-full flex-col gap-2">
                    <div className="flex items-center gap-2">
                      <FormField
                        control={form.control}
                        name="userId"
                        render={({ field }) => (
                          <div className="flex-1">
                            <Input
                              placeholder="아이디를 입력해주세요"
                              {...field}
                              onChange={(e) => {
                                const value = e.target.value
                                  .replace(/[^a-z0-9]/g, "")
                                  .toLowerCase();
                                field.onChange(value);
                                setIdTaken(undefined);
                              }}
                            />
                          </div>
                        )}
                      />
                      <Button
                        type="button"
                        variant="outline"
                        className="whitespace-nowrap"
                        onClick={handleCheckId}
                        disabled={idTaken === false}
                      >
                        중복 확인
                      </Button>
                    </div>
                    <FormField
                      control={form.control}
                      name="userId"
                      render={() => <FormMessage />}
                    />
                    {idTaken === false ? (
                      <FormMessage className="text-green-500">
                        사용 가능한 아이디입니다.
                      </FormMessage>
                    ) : idTaken === true ? (
                      <FormMessage className="text-red-500">
                        이미 사용 중인 아이디입니다.
                      </FormMessage>
                    ) : null}
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <Label htmlFor="password" className="w-1/4">
                    비밀번호*
                  </Label>
                  <FormField
                    control={form.control}
                    name="userPwd"
                    render={({ field }) => (
                      <div className="w-full">
                        <Input
                          type="password"
                          placeholder="비밀번호를 입력해주세요"
                          {...field}
                        />
                        <FormMessage />
                      </div>
                    )}
                  />
                </div>
                <div className="flex items-center gap-2">
                  <Label htmlFor="password" className="w-1/4">
                    비밀번호 확인*
                  </Label>
                  <FormField
                    control={form.control}
                    name="userPwdConfirm"
                    render={({ field }) => (
                      <div className="w-full">
                        <Input
                          type="password"
                          placeholder="비밀번호를 다시 입력해주세요"
                          {...field}
                        />
                        <FormMessage />
                      </div>
                    )}
                  />
                </div>
                <div className="flex items-center gap-2">
                  <Label className="w-1/4">이름</Label>
                  <FormField
                    control={form.control}
                    name="userNm"
                    render={({ field }) => (
                      <div className="w-full">
                        <Input
                          type="text"
                          placeholder="이름을 입력해주세요"
                          {...field}
                        />
                        <FormMessage />
                      </div>
                    )}
                  />
                </div>

                <div className="flex items-center gap-2">
                  <Label className="w-1/4">Email</Label>
                  <FormField
                    control={form.control}
                    name="userEmail"
                    render={({ field }) => (
                      <div className="w-full">
                        <Input
                          type="email"
                          placeholder="이메일을 입력해주세요"
                          {...field}
                        />
                        <FormMessage />
                      </div>
                    )}
                  />
                </div>
                <div className="flex items-start gap-2">
                  <Label className="w-1/4 pt-2">전화번호</Label>
                  <div className="flex w-full flex-col gap-2">
                    <div className="flex items-center gap-2">
                      <div className="flex-1">
                        <Input
                          placeholder="010"
                          maxLength={3}
                          value={phoneNumbers[0]}
                          onChange={(e) => updatePhoneNumber(0, e.target.value)}
                        />
                      </div>
                      <span>-</span>
                      <div className="flex-1">
                        <Input
                          placeholder="1234"
                          maxLength={4}
                          value={phoneNumbers[1]}
                          onChange={(e) => updatePhoneNumber(1, e.target.value)}
                        />
                      </div>
                      <span>-</span>
                      <div className="flex-1">
                        <Input
                          placeholder="5678"
                          maxLength={4}
                          value={phoneNumbers[2]}
                          onChange={(e) => updatePhoneNumber(2, e.target.value)}
                        />
                      </div>
                    </div>
                    <FormField
                      control={form.control}
                      name="userPhone"
                      render={() => <FormMessage />}
                    />
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <Label className="w-1/4">지역코드</Label>
                  <FormField
                    control={form.control}
                    name="tenantCd"
                    render={({ field }) => (
                      <div className="w-full">
                        <Select
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="지역코드를 선택해주세요" />
                          </SelectTrigger>
                          <SelectContent>
                            {tenants.map((tenant) => (
                              <SelectItem
                                key={tenant.tenantCd}
                                value={tenant.tenantCd}
                              >
                                {tenant.tenantNm}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </div>
                    )}
                  />
                </div>
                <div className="flex items-center gap-2">
                  <Label className="w-1/4">소속/직급</Label>
                  <FormField
                    control={form.control}
                    name="userDepr"
                    render={({ field }) => (
                      <div className="w-full">
                        <Input type="text" {...field} />
                        <FormMessage />
                      </div>
                    )}
                  />
                </div>

                <Button
                  type="submit"
                  className="w-full"
                  disabled={form.formState.isSubmitting || idTaken !== false}
                >
                  회원 가입
                </Button>
                <div className="text-center text-sm text-gray-500">
                  회원 가입 신청 후 계정 활성화까지 시간이 소요될 수 있습니다.
                </div>
              </div>
            </form>
          </Form>
        </CardContent>
      </Card>
    </div>
  );
};
