import { icons } from "@/assets/icons";
import Header from "@/components/common/header";
import { EmptyList } from "@/components/common/list";
import {
  readSuggestMinYear,
  readSuggests,
} from "@workspace/db/crud/living-lab/suggest";
import { Skeleton } from "@workspace/ui/components/skeleton";
import { getSession } from "@workspace/utils/auth/session";
import Image from "next/image";
import Link from "next/link";
import { redirect, RedirectType } from "next/navigation";
import { Suspense } from "react";
import { GuidePanel } from "./components/common";
import { SuggestFilter } from "./components/suggest-filter";
import { SuggestItem } from "./components/suggest-item";

const SuggestContents = async ({
  tenantCd,
  category,
  year,
}: {
  tenantCd: string;
  category: string | undefined;
  year: number | undefined;
}) => {
  const suggests = await readSuggests(tenantCd, category, year);
  const minYear = await readSuggestMinYear(tenantCd);
  const currentYear = new Date().getFullYear();
  const years = Array.from(
    { length: currentYear - minYear + 1 },
    (_, i) => currentYear - i,
  );

  return (
    <>
      <SuggestFilter
        category={category}
        year={Number.isNaN(year) ? currentYear : year!}
        selectableYears={years}
        count={suggests.length}
      />
      <div className="flex flex-1 overflow-y-hidden">
        <div className="w-full overflow-y-auto bg-[#FAFBFF] py-[20px]">
          {suggests.length < 1 ? (
            <EmptyList>현재 진행중인 제안이 없습니다.</EmptyList>
          ) : (
            <div className="flex flex-col space-y-[10px]">
              {suggests.map((item, index) => (
                <SuggestItem
                  key={index}
                  item={item}
                  className="mx-[20px] rounded-lg bg-white p-[20px] shadow-lg"
                />
              ))}
            </div>
          )}
        </div>
      </div>
    </>
  );
};

const SuggestContentsSkeleton = () => {
  return (
    <div className="flex flex-1 overflow-y-hidden">
      <div className="w-full overflow-y-auto bg-[#FAFBFF] py-[20px]">
        {Array.from({ length: 3 }).map((_, index) => (
          <div key={index} className="flex flex-col space-y-[10px]">
            <div className="mx-[20px] rounded-lg bg-white p-[20px]">
              <div className="flex justify-between">
                <div>
                  <Skeleton className="mb-[6px] h-[12px] w-[50px]" />
                  <Skeleton className="mb-[16px] h-[16px] w-[150px]" />
                  <Skeleton className="h-[12px] w-[80px]" />
                </div>
                <Skeleton className="h-[65px] w-[65px]" />
              </div>
              <div className="mt-4 flex justify-between">
                <div className="flex items-center text-[10px]">
                  <Skeleton className="mr-2 h-[16px] w-[30px]" />
                  <Skeleton className="mr-2 h-[16px] w-[30px]" />
                  <Skeleton className="h-[16px] w-[30px]" />
                </div>
                <Skeleton className="h-[10px] w-[50px]" />
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default async function SuggestPage({
  searchParams,
}: {
  searchParams: Promise<{ category?: string; year?: string }>;
}) {
  const session = await getSession();
  if (!session) redirect("/", RedirectType.replace);

  const { tenantCd } = session;
  const { category, year } = await searchParams;

  return (
    <main className="flex max-h-screen min-h-screen flex-col bg-[#FFFFFF]">
      <Header title="정책제안" parent="/home" />
      <GuidePanel type="suggest" />
      <Suspense fallback={<SuggestContentsSkeleton />}>
        <SuggestContents
          tenantCd={tenantCd}
          category={category}
          year={year ? Number.parseInt(year) : undefined}
        />
      </Suspense>
      <Link href="/suggest/write">
        <div className="bg-primary fixed bottom-[30px] right-[14px] flex h-[44px] w-[44px] items-center justify-center rounded-full shadow-lg">
          <Image
            src={icons.pencil}
            className="h-24px w-[24px]"
            alt="작성하기"
          />
        </div>
      </Link>
    </main>
  );
}
