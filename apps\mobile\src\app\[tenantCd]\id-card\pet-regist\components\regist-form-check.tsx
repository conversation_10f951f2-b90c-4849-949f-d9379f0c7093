"use client";

import { <PERSON><PERSON>None, PrimaryButton } from "@/components/button";
import { CheckBox } from "@/components/checkbox";
import { useCustomSearchParams } from "@/hooks/use-custom-search-params";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
} from "@workspace/ui/components/form";
import { Input } from "@workspace/ui/components/input";
import { Label } from "@workspace/ui/components/label";
import { useShowModalAlert } from "@workspace/ui/hooks/modal";
import { Session } from "@workspace/utils/auth/types";
import { ChevronRight } from "lucide-react";
import React, { useCallback, useState } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { useMutationCheckAPMS } from "../../actions/pet-query";

const termsList = [
  {
    title: "반려동물 신분증 이용 동의 (필수)",
    mandatory: true,
  },
  {
    title: "반려동물 신분증 마케팅 이용 동의 (선택)",
    mandatory: false,
  },
];

const petRegNumCheckUrl =
  "https://www.animal.go.kr/front/awtis/record/recordConfirmList.do?menuNo=2000000011";

const formSchema = z.object({
  ownName: z.string().min(2, "이름을 입력해주세요."),
  petRegNo: z.string().length(15),
});

export const RegistFormCheck = ({ session }: { session: Session }) => {
  const [, setSearchParams] = useCustomSearchParams();
  const { mutateAsync, isPending } = useMutationCheckAPMS();
  const showModalAlert = useShowModalAlert();

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    mode: "onChange",
    defaultValues: { ownName: session.userNm, petRegNo: "" },
  });

  const [termsAgree, setTermsAgree] = useState<boolean[]>(
    Array.from({ length: termsList.length }).map(() => false),
  );

  const isMandatoryChecked = termsList
    .filter((term) => term.mandatory)
    .every((_, index) => termsAgree[index]);
  const isAllChecked = termsAgree.every((agree) => agree);

  const handleCheckAll = (isChecked: boolean) => {
    const newTermsAgree = termsAgree.map(() => isChecked);
    setTermsAgree(newTermsAgree);
  };

  const handlePetRegNumCheck = () => {
    window.open(`${petRegNumCheckUrl}#_blank`);
  };

  const handleSubmit = useCallback(async (data: z.infer<typeof formSchema>) => {
    try {
      const result = await mutateAsync(data);
      if (result.data) {
        setSearchParams({ step: "registPet", data: result.data }, true);
      } else {
        showModalAlert({ children: result.error });
      }
    } catch (e) {
      alert("국가동물등록번호 조회에 실패하였습니다.");
    }
  }, []);

  return (
    <div>
      <div className="py-[20px] text-[20px] font-medium">
        반려동물 신분증 발급을 위해 아래 약관동의 및 정보를 입력해 주세요
      </div>
      <div className="flex items-center py-[14px]">
        <CheckBox
          checked={isAllChecked}
          style="circle"
          onChange={handleCheckAll}
        >
          <Label className="text-[16px] font-medium">
            약관에 전체동의 (선택사항 포함)
          </Label>
        </CheckBox>
      </div>
      <div className="flex flex-col gap-[8px] rounded-[10px] bg-[#F6F7F8] p-[20px]">
        {termsList.map((term, index) => (
          <CheckBox
            key={index}
            style="noframe"
            className="gap-[10px]"
            checked={termsAgree[index]}
            onChange={(isChecked) => {
              setTermsAgree((prev) =>
                prev.map((agree, i) => (i === index ? isChecked : agree)),
              );
            }}
          >
            <Label>{term.title}</Label>
          </CheckBox>
        ))}
      </div>
      <Form {...form}>
        <div className="mt-[20px]">
          <div className="mb-[10px] text-[16px] font-semibold">등록자 이름</div>
          <FormField
            control={form.control}
            name="ownName"
            render={({ field }) => (
              <FormItem>
                <FormControl>
                  <Input
                    {...field}
                    placeholder="이름을 입력해 주세요"
                    // readOnly={process.env.NODE_ENV !== "development"}
                  />
                </FormControl>
              </FormItem>
            )}
          />
        </div>
        <div className="mt-[12px]">
          <div className="mb-[10px] text-[16px] font-semibold">
            국가동물등록번호
          </div>
          <FormField
            control={form.control}
            name="petRegNo"
            render={({ field }) => (
              <FormItem>
                <FormControl>
                  <Input
                    {...field}
                    placeholder="15자리 동물 등록번호 or RFID 칩 코드를 입력해주세요"
                  />
                </FormControl>
              </FormItem>
            )}
          />
        </div>
        <div className="mt-[12px] py-[10px]">
          <ButtonNone onClick={handlePetRegNumCheck}>
            <div className="flex flex-row">
              <div className="flex-1 text-[14px]">
                등록번호를 모른다면, 번호 조회하기
              </div>
              <div>
                <ChevronRight className="h-[18px] w-[18px]" />
              </div>
            </div>
          </ButtonNone>
        </div>
        <div className="mb-[20px] rounded-[10px] bg-gray-100 p-4 text-sm">
          <p className="mb-[8px] text-[14px] font-semibold">ⓘ 동물등록번호</p>
          <ul className="list-disc pl-5">
            <li>
              국가동물보호정보시스템을 통해 등록한 동물등록번호 및
              마이크로(RFID, 무선전자개체식별장치) 칩코드를 입력해 주세요.
            </li>
            <li>반려동물이 등록되지 않은 경우는 등록 후 진행해주세요.</li>
          </ul>
        </div>
        <PrimaryButton
          disabled={!form.formState.isValid || !isMandatoryChecked || isPending}
          onClick={form.handleSubmit(handleSubmit)}
        >
          약관 동의 및 등록하기
        </PrimaryButton>
      </Form>
      <div className="h-[40px]" />
      <div className="mx-[-20px] flex flex-row items-center border-t-[1px] px-[20px] py-[12px]">
        <div className="flex-1">
          <p className="mb-[5px] text-[12px] text-[#808C98]">1분만에 완료!</p>
          <p className="text-[16px]">국가동물등록 서비스</p>
        </div>
        <div>
          <PrimaryButton className="text-[14px]">
            바로가기
            <ChevronRight className="h-[18px] w-[18px]" />
          </PrimaryButton>
        </div>
      </div>
    </div>
  );
};
