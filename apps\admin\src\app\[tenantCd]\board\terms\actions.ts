"use server";

import { TermsData, TermsDataInsert } from "@workspace/db/crud/common/terms";
import { eq, getMainDb, getTenantDb } from "@workspace/db/drizzle";
import { termsData } from "@workspace/db/schema/common/terms";

export const upsertTerms = async (
  tenantCd: string,
  termsId: number | undefined,
  model: TermsDataInsert,
) => {
  const tenantDb = await getTenantDb(tenantCd);
  if (!termsId) {
    await tenantDb.insert(termsData).values(model);
  } else {
    await tenantDb
      .update(termsData)
      .set({
        seq: model.seq,
        termsCd: model.termsCd,
        termsTitle: model.termsTitle,
        termsVer: model.termsVer,
        termsSmry: model.termsSmry,
        termsConts: model.termsConts,
        mustYn: model.mustYn,
        valYn: model.valYn,
        privacyYn: model.privacyYn,
        chgDt: new Date(),
      })
      .where(eq(termsData.termsId, termsId));
  }
};
