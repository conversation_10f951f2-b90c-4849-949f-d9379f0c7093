"use client";

import { login } from "@/actions/auth";
import { zodResolver } from "@hookform/resolvers/zod";
import { Button } from "@workspace/ui/components/button";
import { Card, CardContent } from "@workspace/ui/components/card";
import { Checkbox } from "@workspace/ui/components/checkbox";
import { Form, FormField, FormMessage } from "@workspace/ui/components/form";
import { Input } from "@workspace/ui/components/input";
import { Label } from "@workspace/ui/components/label";
import Link from "next/link";
import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";

const isProduction = process.env.NODE_ENV !== "development";

const formSchema = z.object({
  id: z
    .string()
    .min(isProduction ? 4 : 1, { message: "아이디를 입력해주세요" }),
  password: z
    .string()
    .min(1, { message: "비밀번호를 입력해주세요" })
    .min(isProduction ? 8 : 1, {
      message: `비밀번호는 ${isProduction ? 8 : 1}자 이상이어야 합니다`,
    }),
});

export const LoginForm = () => {
  const [rememberMe, setRememberMe] = useState(false);

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: { id: "", password: "" },
  });

  // 컴포넌트 마운트 시 저장된 ID와 체크박스 상태 불러오기
  useEffect(() => {
    const savedId = localStorage.getItem("rememberedId");
    if (savedId) {
      form.setValue("id", savedId);
      setRememberMe(true);
    }
  }, [form]);

  const handleSubmit = form.handleSubmit(async (data) => {
    if (rememberMe) {
      localStorage.setItem("rememberedId", data.id);
    } else {
      localStorage.removeItem("rememberedId");
    }
    const result = await login(data.id, data.password);
    if (result.error) {
      form.setError("root", { message: result.error });
    }
  });

  return (
    <div className="fixed inset-0 flex items-center justify-center overflow-y-auto bg-[#F6FAFF]">
      <Card className="w-[800px] overflow-hidden">
        <CardContent className="flex flex-row p-0">
          <div className="bg-primary flex w-[240px] flex-col items-center justify-center p-4 text-white">
            <div className="flex flex-grow flex-col items-center justify-center">
              <img
                src="/assets/login-logo.svg"
                className="mb-4"
                alt="CITY Wallet 로고"
              />
              <div className="text-[16px]">관리자 로그인</div>
            </div>
            <div className="text-[12px] text-[#F6FAFF]">
              Copyright © Data-Alliance 2024
            </div>
          </div>
          <Form {...form}>
            <form onSubmit={handleSubmit} className="flex-grow-[3] p-[50px]">
              <div className="flex flex-col gap-6">
                <div className="flex flex-col items-center text-center">
                  <h1 className="mb-2 text-2xl font-bold">로그인</h1>
                  <p className="text-muted-foreground text-balance">
                    {form.formState.errors.root ? (
                      <span className="text-center text-sm text-red-500">
                        {form.formState.errors.root.message}
                      </span>
                    ) : form.formState.isSubmitting ? (
                      "로그인 중..."
                    ) : (
                      "계정 아이디와 비밀번호를 입력해주세요."
                    )}
                  </p>
                </div>
                <div className="flex items-center gap-2">
                  <Label className="w-1/4">ID</Label>
                  <FormField
                    control={form.control}
                    name="id"
                    render={({ field }) => (
                      <div className="w-full">
                        <Input placeholder="아이디를 입력해주세요" {...field} />
                        <FormMessage />
                      </div>
                    )}
                  />
                </div>
                <div className="flex items-center gap-2">
                  <Label htmlFor="password" className="w-1/4">
                    Password
                  </Label>
                  <FormField
                    control={form.control}
                    name="password"
                    render={({ field }) => (
                      <div className="w-full">
                        <Input
                          type="password"
                          placeholder="비밀번호를 입력해주세요"
                          {...field}
                        />
                        <FormMessage />
                      </div>
                    )}
                  />
                </div>
                <div className="flex items-center gap-2">
                  <div className="flex items-center">
                    <Checkbox
                      id="remember"
                      checked={rememberMe}
                      onCheckedChange={(checked) => setRememberMe(!!checked)}
                    />
                    <Label htmlFor="remember" className="ml-2">
                      ID 기억하기
                    </Label>
                  </div>
                  {
                    // TODO
                    /* <a
                    href="#"
                    className="ml-auto text-sm underline underline-offset-4"
                  >
                    아이디/비밀번호를 잊으셨나요?
                  </a> */
                  }
                </div>
                <Button
                  type="submit"
                  className="w-full"
                  disabled={form.formState.isSubmitting}
                >
                  로그인
                </Button>
                <div className="mt-[20px] text-center text-sm">
                  계정이 없으신가요?{" "}
                  <Link
                    href="/auth/signup"
                    className="underline underline-offset-4"
                  >
                    회원가입
                  </Link>
                </div>
              </div>
            </form>
          </Form>
        </CardContent>
      </Card>
    </div>
  );
};
