"use client";

import { icons } from "@/assets/icons";
import { Button } from "@workspace/ui/components/button";
import { cn } from "@workspace/ui/lib/utils";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { ReactNode } from "react";

export default function Header({
  title,
  parent,
  rightSection,
  onBackPress,
  className,
}: {
  title: string;
  parent?: string;
  rightSection?: ReactNode;
  onBackPress?: () => void;
  className?: string;
}) {
  const router = useRouter();
  return (
    <>
      <header
        className={cn(
          "fixed left-0 right-0 top-0 z-20 flex h-[50px] items-center justify-between bg-[#ffffff] px-[16px] py-[10px]",
          className,
        )}
      >
        <div className="flex items-center">
          {(onBackPress || parent) && (
            <Button
              className="mr-2"
              variant="ghost"
              size="icon"
              onClick={onBackPress || (() => parent && router.push(parent))}
            >
              <Image
                src={icons.arrowBack}
                alt="뒤로가기"
                className="h-[30px] w-[30px]"
              />
            </Button>
          )}
          <h1 className="text-[20px] font-medium">{title}</h1>
        </div>
        {rightSection && (
          <div className="flex items-center space-x-4">{rightSection}</div>
        )}
      </header>
      <div className="h-[50px] flex-shrink-0"></div>
    </>
  );
}
