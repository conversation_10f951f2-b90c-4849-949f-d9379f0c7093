"use server";

import { platformSaasApi } from "../platform-api";

type PedometerDayData = {
  date: string; // yyyy-MM-dd
  stepCnt: number;
  rewardEligible: boolean;
  rewardGiven: boolean;
};

type PedometerMonthData = {
  monthDt: string; // yyyy-MM
  avgSteps: number;
  totalSteps: number;
};

export const fetchPedometerGoal = async () => {
  const { data } = await platformSaasApi.get<{
    userDid: string;
    goalCount: number;
  }>("/api/service/pedometer/goal");
  return data;
};

export const fetchPedometerRecordsRange = async ({
  startDt,
  endDt,
}: {
  startDt: string; // yyyy-MM-dd
  endDt: string; // yyyy-MM-dd
}) => {
  const { data } = await platformSaasApi.get<PedometerDayData[]>(
    "/api/service/pedometer/range",
    { params: { startDt, endDt } },
  );
  return data;
};

export const fetchPedometerRecordsMonthly = async ({
  startDt,
  endDt,
}: {
  startDt: string; // yyyy-MM-dd
  endDt: string; // yyyy-MM-dd
}) => {
  const { data } = await platformSaasApi.get<PedometerMonthData[]>(
    "/api/service/pedometer/monthStatistics",
    { params: { startDt, endDt } },
  );
  return data;
};
