"use client";

import { cn } from "@workspace/ui/lib/utils";
import { useBridgeStore } from "@workspace/webview-state";
import { AppInsetsType } from "@workspace/webview-state/types";
import { ReactNode, useEffect, useState } from "react";

export const MainContainer = ({
  className,
  containerClassName,
  paddingTop = false,
  paddingBottom = undefined,
  gradient = true,
  children,
}: {
  className?: string;
  containerClassName?: string;
  paddingTop?: boolean;
  paddingBottom?: boolean | undefined;
  gradient?: boolean;
  children: ReactNode;
}) => {
  const [paddings, setPaddings] = useState<{
    insets: AppInsetsType | null;
    paddingBottom: null | number;
  }>({
    insets: null,
    paddingBottom: null,
  });
  const [insets, bridgePaddingBottom] = useBridgeStore((state) => [
    state.insets,
    state.paddingBottom,
  ]);

  useEffect(() => {
    if (insets && bridgePaddingBottom)
      setPaddings({
        insets: insets,
        paddingBottom: bridgePaddingBottom,
      });
  }, [insets, bridgePaddingBottom, paddingBottom]);

  const paddingTopValue = 50 + (paddings.insets?.top ?? 0);
  const paddingBottomValue = paddingBottom
    ? (paddings.paddingBottom ?? 0)
    : paddingBottom === false
      ? 0
      : (paddings.insets?.bottom ?? 0);

  if (
    process.env.NODE_ENV !== "development" &&
    paddingTop &&
    !paddings.insets
  ) {
    return null;
  }

  return (
    <div
      className={cn(
        "flex min-h-full w-full justify-center",
        gradient ? "from-bg-from to-bg-to bg-gradient-to-t" : undefined,
        className,
      )}
      style={{
        paddingTop: paddingTop ? `${paddingTopValue.toFixed(0)}px` : undefined,
        paddingBottom: paddingBottomValue,
      }}
    >
      <div
        className={cn(
          "w-full min-w-[360px] max-w-[560px] px-[20px] pb-[20px]",
          "overflow-y-auto", // 스크롤바 추가
          containerClassName,
        )}
      >
        {children}
      </div>
    </div>
  );
};
