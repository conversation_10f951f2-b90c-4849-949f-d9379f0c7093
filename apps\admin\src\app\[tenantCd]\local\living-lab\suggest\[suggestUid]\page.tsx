import { AppContent } from "@/app/[tenantCd]/components/container";
import { TenantPageProps } from "@/app/[tenantCd]/layout";
import { getBaseURL } from "@/middleware-utils";
import {
  readSuggest,
  readSuggestComments,
} from "@workspace/db/crud/living-lab/suggest";
import { readSuggestCommentsAdmin } from "@workspace/db/crud/living-lab/suggest-admin";
import { Button } from "@workspace/ui/components/button";
import { Card, CardContent } from "@workspace/ui/components/card";
import dayjs from "dayjs";
import { headers } from "next/headers";
import { notFound, redirect, RedirectType } from "next/navigation";
import { SuggestComments } from "./components/suggest-comments";
import { SuggestContent } from "./components/suggest-content";

export default async function SuggestContentPage({
  params,
}: TenantPageProps & {
  params: Promise<{ suggestUid: string }>;
}) {
  const baseURL = await getBaseURL();

  const { tenantCd, suggestUid } = await params;
  const suggestUidNumber = Number.parseInt(suggestUid);
  if (Number.isNaN(suggestUidNumber)) {
    redirect("../", RedirectType.replace);
  }

  const suggest = await readSuggest(tenantCd, suggestUidNumber, false);
  if (!suggest) notFound();

  const suggestComments = await readSuggestCommentsAdmin(
    tenantCd,
    suggestUidNumber,
  );

  return (
    <AppContent title="정책제안 상세" hasBack>
      <div className="gap-4 space-y-4">
        <SuggestContent
          baseURL={baseURL}
          tenantId={tenantCd}
          suggest={suggest}
        />

        <SuggestComments tenantId={tenantCd} comments={suggestComments} />
      </div>
    </AppContent>
  );
}
