import { AppContent } from "@/app/[tenantCd]/components/container";
import { TenantPageProps } from "@/app/[tenantCd]/layout";
import { readSuggests } from "@workspace/db/crud/living-lab/suggest";
import {
  createSuggestFilters,
  readSuggestsAdmin,
  readSuggestsCountAdmin,
} from "@workspace/db/crud/living-lab/suggest-admin";
import { DataTableSkeleton } from "@workspace/ui/components/data-table";
import { Suspense } from "react";
import { SuggestList } from "./components/suggest-list";

const SuggestData = async ({
  tenantCd,
  filters,
  pageNumber,
  pageSize,
}: {
  tenantCd: string;
  filters: any;
  pageNumber: number;
  pageSize: number;
}) => {
  const suggestsCount = await readSuggestsCountAdmin(tenantCd, filters);
  const suggests = await readSuggestsAdmin(
    tenantCd,
    filters,
    pageNumber,
    pageSize,
  );

  return (
    <SuggestList
      data={suggests}
      count={suggestsCount}
      pageNumber={pageNumber}
      pageSize={pageSize}
    />
  );
};

export default async function SuggestPage({
  params,
  searchParams,
}: TenantPageProps) {
  const { tenantCd } = await params;
  const { page = "1", ...search } = await searchParams;

  const filters = createSuggestFilters(search);
  const pageNumber = parseInt(page as string, 10) ?? 1;
  const pageSize = 10;

  return (
    <AppContent title="주민참여 정책제안">
      <Suspense fallback={<DataTableSkeleton pageSize={pageSize} />}>
        <SuggestData
          tenantCd={tenantCd}
          filters={filters}
          pageNumber={pageNumber}
          pageSize={pageSize}
        />
      </Suspense>
    </AppContent>
  );
}
