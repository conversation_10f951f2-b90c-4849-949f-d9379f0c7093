import { useQueryPedometerRecordsRange } from "@/actions/pedometer/pedometer-query";
import { PrimaryButton } from "@/components/button";
import { commaizeNumber } from "@toss/utils";
import { Button } from "@workspace/ui/components/button";
import { useModalStore } from "@workspace/ui/providers/modal-provider";
import { dayNames } from "@workspace/utils/datetime";
import {
  addMonths,
  getDay,
  lastDayOfMonth,
  startOfMonth,
  subMonths,
} from "date-fns";
import { CheckCircle2, ChevronLeft, ChevronRight, XCircle } from "lucide-react";

const DateStatus = ({
  date,
  dateStatus,
  onClick,
}: {
  date: number | null;
  dateStatus: boolean | undefined;
  onClick: (date: number | null) => void;
}) => {
  return (
    <div
      className="relative flex h-[26px] flex-1 items-center justify-center"
      onClick={() => onClick(date)}
    >
      {date ? (
        <>
          <span className="absolute text-sm">{date}</span>
          {dateStatus === true ? (
            <CheckCircle2 className="text-primary h-[18px] w-[18px]" />
          ) : dateStatus === false ? (
            <XCircle className="h-[18px] w-[18px] text-red-500" />
          ) : null}
        </>
      ) : null}
    </div>
  );
};

export const PedometerCalendar = ({
  yearMonth,
  currentDate,
  onChangeYearMonth,
}: {
  yearMonth: Date;
  currentDate: Date;
  onChangeYearMonth: (yearMonth: Date) => void;
}) => {
  const [showModal, hideModal] = useModalStore((state) => [
    state.showModal,
    state.hideModal,
  ]);

  const startDate = startOfMonth(yearMonth);
  const lastDate = lastDayOfMonth(yearMonth);
  const firstDay = getDay(startOfMonth(yearMonth));

  const { data } = useQueryPedometerRecordsRange(startDate, lastDate);

  const handleDateTap = (date: number | null) => {
    if (!date || !data) return;
    const stepCount = data?.find(
      (record) => new Date(record.date).getDate() === date,
    )?.stepCnt;
    if (stepCount === undefined) return;
    const modalId = showModal({
      title: `${yearMonth.getFullYear()}년 ${yearMonth.getMonth() + 1}월 ${date}일`,
      children: (
        <div>
          <div className="flex flex-col items-center justify-center text-[16px] font-medium text-[#636F7A]">
            <div className="text-[#636F7A]">걸음수는</div>
            <div className="font-bold text-[#000000]">
              {commaizeNumber(stepCount ?? 0)} 보
            </div>
            <div>입니다.</div>
          </div>
          <div className="mt-[20px]">
            <PrimaryButton onClick={() => hideModal(modalId)}>
              확인
            </PrimaryButton>
          </div>
        </div>
      ),
    });
  };

  const dateStatus: Record<number, boolean | undefined> =
    data?.reduce(
      (acc, record) => {
        const date = new Date(record.date).getDate();
        acc[date] = record.rewardEligible;
        return acc;
      },
      {} as Record<number, boolean | undefined>,
    ) || {};

  const isCurrentMonth =
    yearMonth.getFullYear() === currentDate.getFullYear() &&
    yearMonth.getMonth() === currentDate.getMonth();

  const calendar: (number | null)[] = Array(firstDay)
    .fill(null)
    .concat(Array.from({ length: lastDate.getDate() }, (_, i) => i + 1));
  while (calendar.length % 7 !== 0 || calendar.length < 7 * 6)
    calendar.push(null);
  return (
    <div className="w-full rounded-lg bg-white p-[20px]">
      <div className="mb-[20px] flex items-center justify-between">
        <Button
          variant="ghost"
          onClick={() => onChangeYearMonth(subMonths(yearMonth, 1))}
        >
          <ChevronLeft className="h-5 w-5" />
        </Button>
        <div className="text-[14px] font-medium">{`${yearMonth.getFullYear()}년 ${yearMonth.getMonth() + 1}월`}</div>
        <Button
          variant="ghost"
          disabled={isCurrentMonth}
          onClick={() => onChangeYearMonth(addMonths(yearMonth, 1))}
        >
          <ChevronRight className="h-5 w-5" />
        </Button>
      </div>
      <div className="mb-2 flex justify-between">
        {dayNames.map((dayName, index) => (
          <div
            key={index}
            className="flex-1 text-center font-[medium] text-[12px]"
          >
            {dayName}
          </div>
        ))}
      </div>
      {Array.from({ length: calendar.length / 7 }).map((_, weekIdx) => (
        <div key={weekIdx} className="mb-1 flex justify-between">
          {calendar.slice(weekIdx * 7, weekIdx * 7 + 7).map((date, idx) => (
            <DateStatus
              key={idx}
              date={date}
              dateStatus={dateStatus[date ?? 0]}
              onClick={handleDateTap}
            />
          ))}
        </div>
      ))}
    </div>
  );
};
