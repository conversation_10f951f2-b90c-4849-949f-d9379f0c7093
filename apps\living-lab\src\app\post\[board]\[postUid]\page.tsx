import { BOARDS } from "@/app/utils/consts";
import Header from "@/components/common/header";
import { getSession } from "@workspace/utils/auth/session";
import { notFound, redirect, RedirectType } from "next/navigation";
import { Suspense } from "react";
import { BoardDetailContent } from "./components/content";

export default async function PostContentPage({
  params,
}: {
  params: Promise<{ board: string; postUid: string }>;
}) {
  const session = await getSession();
  if (!session) redirect("/", RedirectType.replace);
  const { board, postUid: uid } = await params;
  const boardInfo = BOARDS.find((b) => b.value === board);
  if (boardInfo === undefined) notFound();

  const postUid = Number.parseInt(uid);
  if (Number.isNaN(postUid)) {
    redirect("/post", RedirectType.replace);
  }

  return (
    <main className="flex max-h-screen min-h-screen flex-col bg-[#FFFFFF]">
      <Header title={`${boardInfo.title} 상세보기`} parent={`/post/${board}`} />
      <Suspense>
        <BoardDetailContent
          tenantId={session.tenantCd}
          board={boardInfo.value}
          postUid={postUid}
        />
      </Suspense>
    </main>
  );
}
