{"id": "a7007b93-be13-42df-a5d7-e7922ee196aa", "prevId": "00000000-0000-0000-0000-000000000000", "version": "7", "dialect": "postgresql", "tables": {"public.tb_store_delivery": {"name": "tb_store_delivery", "schema": "", "columns": {"id": {"name": "id", "type": "bigint", "primaryKey": true, "notNull": true, "identity": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "tb_store_delivery_id_seq", "schema": "public", "increment": "1", "startWith": "1", "minValue": "1", "maxValue": "9223372036854775807", "cache": "1", "cycle": false}}, "order_detail_id": {"name": "order_detail_id", "type": "bigint", "primaryKey": false, "notNull": true}, "time": {"name": "time", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "NOW()"}, "count": {"name": "count", "type": "integer", "primaryKey": false, "notNull": true, "default": 1}, "delivery_service": {"name": "delivery_service", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": false, "notNull": false}, "transaction_id": {"name": "transaction_id", "type": "<PERSON><PERSON><PERSON>(64)", "primaryKey": false, "notNull": false}, "tracking_code": {"name": "tracking_code", "type": "<PERSON><PERSON><PERSON>(64)", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "smallint", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.tb_store_order_detail": {"name": "tb_store_order_detail", "schema": "", "columns": {"id": {"name": "id", "type": "bigint", "primaryKey": true, "notNull": true, "identity": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "tb_store_order_detail_id_seq", "schema": "public", "increment": "1", "startWith": "1", "minValue": "1", "maxValue": "9223372036854775807", "cache": "1", "cycle": false}}, "order_id": {"name": "order_id", "type": "bigint", "primaryKey": false, "notNull": true}, "product_id": {"name": "product_id", "type": "bigint", "primaryKey": false, "notNull": true}, "price": {"name": "price", "type": "integer", "primaryKey": false, "notNull": true}, "count": {"name": "count", "type": "integer", "primaryKey": false, "notNull": true, "default": 1}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.tb_store_order": {"name": "tb_store_order", "schema": "", "columns": {"id": {"name": "id", "type": "bigint", "primaryKey": true, "notNull": true, "identity": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "tb_store_order_id_seq", "schema": "public", "increment": "1", "startWith": "1", "minValue": "1", "maxValue": "9223372036854775807", "cache": "1", "cycle": false}}, "user_did": {"name": "user_did", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "smallint", "primaryKey": false, "notNull": true}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>(64)", "primaryKey": false, "notNull": true, "default": "''"}, "price": {"name": "price", "type": "integer", "primaryKey": false, "notNull": true}, "time": {"name": "time", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "NOW()"}, "receiverName": {"name": "<PERSON><PERSON><PERSON>", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": false, "notNull": true}, "receiverPhone": {"name": "receiverPhone", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": false, "notNull": true}, "receiverAddress": {"name": "receiver<PERSON><PERSON><PERSON>", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.tb_store_payment": {"name": "tb_store_payment", "schema": "", "columns": {"id": {"name": "id", "type": "bigint", "primaryKey": true, "notNull": true, "identity": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "tb_store_payment_id_seq", "schema": "public", "increment": "1", "startWith": "1", "minValue": "1", "maxValue": "9223372036854775807", "cache": "1", "cycle": false}}, "user_did": {"name": "user_did", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "order_id": {"name": "order_id", "type": "bigint", "primaryKey": false, "notNull": true}, "amount": {"name": "amount", "type": "integer", "primaryKey": false, "notNull": true}, "time": {"name": "time", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "NOW()"}, "status": {"name": "status", "type": "smallint", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.tb_store_product_tag": {"name": "tb_store_product_tag", "schema": "", "columns": {"product_id": {"name": "product_id", "type": "bigint", "primaryKey": false, "notNull": true}, "tag_id": {"name": "tag_id", "type": "bigint", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.tb_store_product": {"name": "tb_store_product", "schema": "", "columns": {"id": {"name": "id", "type": "bigint", "primaryKey": true, "notNull": true, "identity": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "tb_store_product_id_seq", "schema": "public", "increment": "1", "startWith": "1", "minValue": "1", "maxValue": "9223372036854775807", "cache": "1", "cycle": false}}, "source_type": {"name": "source_type", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "source_id": {"name": "source_id", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "brand": {"name": "brand", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": false, "notNull": false}, "price": {"name": "price", "type": "integer", "primaryKey": false, "notNull": true}, "image_url": {"name": "image_url", "type": "<PERSON><PERSON><PERSON>(400)", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "smallint", "primaryKey": false, "notNull": true}, "created_time": {"name": "created_time", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "NOW()"}, "modified_time": {"name": "modified_time", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.tb_store_tag": {"name": "tb_store_tag", "schema": "", "columns": {"id": {"name": "id", "type": "bigint", "primaryKey": true, "notNull": true, "identity": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "tb_store_tag_id_seq", "schema": "public", "increment": "1", "startWith": "1", "minValue": "1", "maxValue": "9223372036854775807", "cache": "1", "cycle": false}}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"tb_store_tag_name_unique": {"name": "tb_store_tag_name_unique", "nullsNotDistinct": false, "columns": ["name"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}