import { ImageFiles } from "@/app/suggest/components/common";
import { PostContent, PostInfo } from "@/components/common/post-contents";
import { formatAsDateTime } from "@/lib/datetime";
import { getBaseURL } from "@/middleware";
import { convertNewlineToJSX } from "@toss/react";
import { readPoll, readPollVoted } from "@workspace/db/crud/living-lab/poll";
import { POLL_STATE } from "@workspace/db/schema/living-lab/poll";
import dayjs, { Dayjs } from "dayjs";
import { notFound } from "next/navigation";
import {
  getPollCategoryName,
  getPollStateName,
  isPollClosed,
} from "../../consts";
import { PollReferences } from "../vote/components/poll-reference";
import { VoteButton } from "./client";
import { PollResult } from "./poll-result";

const VoteStateButton = async ({
  tenantId,
  userDid,
  pollUid,
  isPollClosed,
}: {
  tenantId: string;
  userDid: string;
  pollUid: number;
  isPollClosed: boolean;
}) => {
  const isVoted = await readPollVoted(tenantId, userDid, pollUid);
  const disabled = isPollClosed || isVoted;

  return (
    <VoteButton pollUid={pollUid} disabled={disabled}>
      {isVoted ? "투표 완료" : isPollClosed ? "기한 만료" : "투표하기"}
    </VoteButton>
  );
};

const PollInfos = ({ period }: { period: Dayjs[] }) => {
  const startDate = formatAsDateTime(period[0]);
  const endDate = formatAsDateTime(period[1]);
  const periodText = `${startDate} ~ ${endDate}`;

  return (
    <>
      <PostInfo title="투표기간">{periodText}</PostInfo>
      <PostInfo title="투표방법">앱 투표</PostInfo>
    </>
  );
};

export const PollDetailContent = async ({
  tenantCd,
  userDid,
  pollUid,
}: {
  tenantCd: string;
  userDid: string;
  pollUid: number;
}) => {
  const content = await readPoll(tenantCd, pollUid);
  if (!content) notFound();

  const period = [dayjs(content.startDate), dayjs(content.endDate)];
  const isClosed = isPollClosed(content.state, period);

  const badges = [
    getPollCategoryName(content.category),
    getPollStateName(content.state, period),
  ];

  return (
    <div>
      <div className="flex flex-1 overflow-y-hidden">
        <div className="flex flex-1 flex-col overflow-y-auto">
          <PostContent
            badges={badges}
            name={content.name}
            depr={content.depr}
            title={content.title}
            headers={<PollInfos period={period} />}
          >
            <ImageFiles baseURL={await getBaseURL()} files={content.files} />
            {convertNewlineToJSX(content.content)}
          </PostContent>
        </div>
      </div>
      {content.state === POLL_STATE.CLOSED ? (
        <PollResult tenantId={tenantCd} pollUid={pollUid} />
      ) : (
        <VoteStateButton
          tenantId={tenantCd}
          userDid={userDid}
          pollUid={pollUid}
          isPollClosed={isClosed}
        />
      )}
      <PollReferences tenantId={tenantCd} pollUid={pollUid} />
    </div>
  );
};
