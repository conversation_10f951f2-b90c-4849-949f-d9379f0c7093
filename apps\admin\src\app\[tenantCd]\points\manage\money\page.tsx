import { isMainTenant } from "@/lib/tenant";
import { convertStringToDateRange } from "@/lib/utils";
import {
  getPointHistory,
  PointSearchFilter,
  readPointCount,
} from "@workspace/db/crud/common/points";
import { DataTableSkeleton } from "@workspace/ui/components/data-table";
import { Suspense } from "react";
import { AppContent } from "../../../components/container";
import { TenantPageProps } from "../../../layout";
import { PointList } from "./components/point-list";
import { PointSearchForm } from "./components/point-search-form";

const parseSearchParams = (search: {
  [x: string]: string | string[] | undefined;
}): PointSearchFilter => {
  if (!search) return {};
  const getString = (
    value: string | string[] | undefined,
  ): string | undefined => {
    if (Array.isArray(value)) return value[0];
    return value;
  };
  const getNumber = (
    value: string | string[] | undefined,
  ): number | undefined => {
    const str = getString(value);
    return str ? parseInt(str, 10) : undefined;
  };
  return {
    searchType: getString(search.searchType),
    keyword: getString(search.keyword),
    serviceName: getString(search.serviceName),
    pointType: getString(search.pointType),
    pointDetailType: getString(search.pointDetailType),
    serviceType: getString(search.serviceType),
    earnDt: convertStringToDateRange([
      getString(search.earnDtFrom),
      getString(search.earnDtTo),
    ]),
    expDt: convertStringToDateRange([
      getString(search.expDtFrom),
      getString(search.expDtTo),
    ]),
    pageSize: getNumber(search.pageSize),
  };
};

const PointManageContent = async ({
  tenantCd,
  searchParams,
  pageNumber,
  pageSize,
}: {
  tenantCd: string;
  searchParams: PointSearchFilter;
  pageNumber: number;
  pageSize: number;
}) => {
  const pointCount = await readPointCount(tenantCd, searchParams);
  const points = await getPointHistory(
    tenantCd,
    searchParams,
    pageNumber,
    pageSize,
  );

  return (
    <PointList
      data={points}
      count={pointCount}
      pageNumber={pageNumber}
      pageSize={pageSize}
    />
  );
};

export default async function PointManagePage({
  params,
  searchParams,
}: TenantPageProps) {
  const { tenantCd } = await params;
  const { page = "1", ...search } = await searchParams;
  const initialParams = parseSearchParams(search);
  const pageNumber = parseInt(page as string, 10) ?? 1;
  const pageSize = 10;

  return (
    <AppContent title="포인트 적립 관리(유상)">
      <PointSearchForm
        initialParams={initialParams}
        isMainTenant={isMainTenant(tenantCd)}
      />
      <Suspense fallback={<DataTableSkeleton pageSize={pageSize} />}>
        <PointManageContent
          tenantCd={tenantCd}
          searchParams={initialParams}
          pageNumber={pageNumber}
          pageSize={pageSize}
        />
      </Suspense>
    </AppContent>
  );
}
