import { BinaryLike, createHash } from "crypto";
import { existsSync } from "fs";
import { mkdir, writeFile } from "fs/promises";
import { join } from "path";

const hashContents = (buffer: Buffer): string => {
  const hash = createHash("sha256");
  hash.update(buffer as BinaryLike);
  return hash.digest("hex");
};

export const createHashFileName = (fileName: string, buffer: Buffer) => {
  const hash = hashContents(buffer);
  const ext = join(fileName).split(".").pop();
  const newFileName = `${hash}.${ext}`;
  return newFileName;
};

export const savePendingFiles = async (files: FormDataEntryValue[]) => {
  const savedFiles = [];
  for (const file of files) {
    if (file instanceof File) {
      const arrayBuffer = await file.arrayBuffer();
      const buffer = Buffer.from(arrayBuffer);
      const newFileName = createHashFileName(file.name, buffer);
      const filePath = join(process.env.ATTACHMENT_PATH!, newFileName);
      await mkdir(process.env.ATTACHMENT_PATH!, { recursive: true });
      if (!existsSync(filePath)) {
        await writeFile(filePath, new Uint8Array(buffer));
      }
      savedFiles.push({
        originalName: file.name,
        uri: newFileName,
      });
    }
  }
  return savedFiles;
};
