import "server-only";
import { ADMIN_ROLE } from "@workspace/db/schema/admin/admin";
import { jwtVerify, SignJWT } from "jose";
import { cookies } from "next/headers";

const secretKey = "ek8EVcnDbEgoh+oCHzTjEf92d4Y7tKtFkbpl83V0zOU="; // process.env.SESSION_SECRET
const sessionName = "session-admin"; // process.env.SESSION_NAME

const encodedKey = new TextEncoder().encode(secretKey);

export type TenantPayload = {
  tenantCd: string;
  tenantNm: string | null;
  role: ADMIN_ROLE;
};

export type SessionPayload = {
  uid: number;
  userNm: string;
  userDepr: string | null;
  lastLoginDt: Date;
  tenantCd: string;
  tenants: TenantPayload[];
};

export const encryptJWT = async (payload: any) => {
  return new SignJWT(payload)
    .setProtectedHeader({ alg: "HS256" })
    .setIssuedAt()
    .setExpirationTime("7d")
    .sign(encodedKey);
};

export const decryptJWT = async (session: string | undefined = "") => {
  try {
    const { payload } = await jwtVerify(session, encodedKey, {
      algorithms: ["HS256"],
    });
    return payload;
  } catch (error) {
    console.log("Failed to verify session");
  }
};

const setSessionCookie = async (payload: SessionPayload) => {
  const cookieStore = await cookies();
  const expiresAt = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000);
  const session = await encryptJWT({ ...payload, expiresAt });
  cookieStore.set(sessionName, session, {
    httpOnly: true,
    secure: process.env.NODE_ENV === "production",
    expires: expiresAt,
    sameSite: "lax",
    path: "/",
  });
};

export const createSession = async (payload: SessionPayload) => {
  await setSessionCookie(payload);
};

export const updateSession = async () => {
  const cookieStore = await cookies();
  const session = cookieStore.get(sessionName)?.value;
  const payload = (await decryptJWT(session)) as SessionPayload;
  if (!session || !payload) return null;

  await setSessionCookie(payload);
};

export const getSession = async (tenantId: string | undefined = undefined) => {
  const cookieStore = await cookies();
  const session = cookieStore.get(sessionName)?.value;
  if (!session) return undefined;
  const payload = (await decryptJWT(session)) as SessionPayload | undefined;
  if (tenantId && payload) {
    const tenant = payload.tenants.find((t) => t.tenantCd === tenantId);
    if (!tenant) return undefined;
  }
  return payload;
};

export const deleteSession = async () => {
  const cookieStore = await cookies();
  cookieStore.delete(sessionName);
};
