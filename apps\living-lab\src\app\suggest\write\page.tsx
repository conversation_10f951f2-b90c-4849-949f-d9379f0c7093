import Header from "@/components/common/header";
import { getSession } from "@workspace/utils/auth/session";
import { redirect, RedirectType } from "next/navigation";
import { GuidePanel } from "../components/common";
import { SuggestForm } from "./write-suggest";

export default async function SuggestWritePage() {
  const session = await getSession();
  if (!session) redirect("/", RedirectType.replace);

  return (
    <main className="flex max-h-screen min-h-screen flex-col bg-[#FFFFFF]">
      <Header title="정책제안" parent="/suggest" />
      <GuidePanel type="suggest" />
      <SuggestForm />
    </main>
  );
}
