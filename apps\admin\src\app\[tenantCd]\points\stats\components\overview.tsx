import { commaizeNumber } from "@toss/utils";
import {
  readEarnPointCount,
  readTransferPointCount,
  readUsePointCount,
} from "@workspace/db/crud/common/points";
import { Card } from "@workspace/ui/components/card";

const StatCard = ({
  title,
  subtitle,
  value,
  className = "",
}: {
  title: string;
  subtitle?: string;
  value: number | undefined;
  className?: string;
}) => (
  <Card className={`p-4 ${className}`}>
    <div className="flex items-center justify-between">
      <div className="text-sm text-gray-600">
        {title}
        {subtitle && (
          <div className="mt-1 text-xs text-gray-400">({subtitle})</div>
        )}
      </div>
      {/* <CircleHelp size={16} /> */}
    </div>
    <div className="mt-2 text-2xl font-bold">
      {value !== undefined ? commaizeNumber(value) : "-"}
    </div>
  </Card>
);

const PointStatsCount = async ({ tenantCd }: { tenantCd: string }) => {
  const result = await readEarnPointCount(tenantCd);
  return (
    <StatCard title="전체 누적 포인트 적립" subtitle="무상" value={result} />
  );
};

const PointUseCount = async ({ tenantCd }: { tenantCd: string }) => {
  const result = await readUsePointCount(tenantCd);
  return <StatCard title="포인트 사용" subtitle="무상" value={result} />;
};

const PointTransferStatsCount = async ({ tenantCd }: { tenantCd: string }) => {
  const result = await readTransferPointCount(tenantCd);
  return (
    <StatCard
      title="전체 누적 포인트 전환"
      subtitle="내보내기"
      value={result}
    />
  );
};

export const PointsStatsOverview = ({ tenantCd }: { tenantCd: string }) => {
  return (
    <div className="flex w-80 flex-col gap-4">
      {/* <UserStatsCount tenantCd={tenantCd} />
      <UserStatsCount tenantCd={tenantCd} /> */}
      <StatCard title="포인트 충전" subtitle="유상" value={undefined} />
      <PointStatsCount tenantCd={tenantCd} />
      <StatCard title="포인트 사용" subtitle="유상" value={undefined} />
      <PointUseCount tenantCd={tenantCd} />
      <StatCard title="포인트 전환" subtitle="가져오기" value={undefined} />
      <PointTransferStatsCount tenantCd={tenantCd} />
    </div>
  );
};
