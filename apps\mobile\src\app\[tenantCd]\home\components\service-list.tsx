"use client";

import { ButtonNone } from "@/components/button";
import { MenuItem, useGetServiceItems } from "@/consts/menu-items";
import assets from "@/services/assets";
import { convertNewlineToJSX } from "@toss/react";
import { Button } from "@workspace/ui/components/button";
import Image from "next/image";
import { ListTitle } from "./common";
import { GyeptServiceList } from "./service-gyept";

const ServiceItem = ({
  title,
  imageUrl,
  description,
  buttonText,
  onPress,
  onButtonPress,
}: MenuItem) => {
  return (
    <ButtonNone asChild onClick={onPress}>
      <div className="flex h-[152px] flex-col justify-between rounded-[10px] bg-white p-[10px]">
        <div className="mb-[8px] flex items-center justify-between">
          <span className="text-[16px] font-semibold">
            {convertNewlineToJSX(title)}
          </span>
          {imageUrl && (
            <Image
              src={imageUrl}
              width={40}
              height={40}
              alt={title}
              className="ml-[10px] h-[40px] w-[40px]"
            />
          )}
        </div>
        <div className="line-clamp-3 flex-1 place-items-start text-ellipsis text-[12px] text-[#636F7A]">
          {description}
        </div>
        {buttonText && (
          <div>
            <Button
              className="w-full items-center justify-center transition-transform active:scale-95"
              variant="secondary"
              onClick={(e) => {
                e.stopPropagation();
                onButtonPress ? onButtonPress() : onPress ? onPress() : null;
              }}
            >
              <span className="text-[#2DA5FF]">{buttonText}</span>
            </Button>
          </div>
        )}
      </div>
    </ButtonNone>
  );
};

export const ServiceList = () => {
  const { title, items } = useGetServiceItems();

  return (
    <div className="mt-[20px]">
      <ListTitle>{title}</ListTitle>
      <div className="grid grid-cols-2 gap-[12px]">
        {items.map((item, index) => (
          <ServiceItem key={index} {...item} />
        ))}
      </div>
    </div>
  );
};

export const ServiceListLocal = ({ tenantId }: { tenantId: string }) => {
  const data = assets(tenantId).home.localService;
  if (!data) return null;
  const { title, items } = data;

  const Component = tenantId === "gyept" ? GyeptServiceList : null;
  if (!Component) return null;

  return (
    <div className="mt-[20px]">
      <ListTitle>{title}</ListTitle>
      <Component items={items} />
    </div>
  );
};
