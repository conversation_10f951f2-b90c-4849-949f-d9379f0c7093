import { commaizeNumber } from "@toss/utils";
import { Routes, useBridgeStore, useNavigate } from "@workspace/webview-state";
import { MAIN_TENANT_CD } from "./consts";

export type MenuItem = {
  title: string;
  imageUrl: string;
  description?: string;
  buttonText?: string | null;

  onPress?: () => void;
  onButtonPress?: () => void;
};

export type Menu = {
  title?: string;
  items: MenuItem[];
};

export const useGetServiceItems = (): Menu => {
  const { navigate } = useNavigate();

  const petPass = undefined;
  const [todaySteps] = useBridgeStore((state) => [state.todaySteps]);

  const items: MenuItem[] = [
    {
      title: "반려동물\n신분증",
      imageUrl: "/assets/menu/pet-pass.png",
      description: "모바일로 발급되는 반려동물 신분증/출입증입니다.",
      buttonText: petPass ? "확인하기" : "발급하기",
      onPress: () => navigate(Routes.idCard({ category: "pet" })),
    },
    {
      title: "걸음수 측정",
      imageUrl: "/assets/menu/pedometer.png",
      description: "목표걸음 달성을 통해 혜택받기!",
      buttonText: todaySteps
        ? commaizeNumber(todaySteps) + " 걸음"
        : todaySteps === undefined
          ? "불러오는 중..."
          : "걸음수를 기록하세요",
      onPress: () => navigate(Routes.pedometer()),
    },
    // {
    //   title: "소식알림",
    //   imageUrl: "/assets/menu/inbox.png",
    //   description: "우리 지역 소식을 확인하고, 다양한 혜택을 받을 수 있어요!",
    //   buttonText: "확인하기",
    // },
  ];

  return { title: "CT 서비스", items };
};

export const useGetLocalLoungeItems = (tenantCd: string): Menu | null => {
  const { navigate } = useNavigate();

  const items: MenuItem[] = [
    ...(tenantCd !== MAIN_TENANT_CD
      ? [
          {
            title: "리빙랩",
            imageUrl: "/assets/menu/living-lab.png",
            description: "우리지역의 소통과 발전에 적극적으로 참여하기!",
            onPress: () => navigate(Routes.livingLab()),
          },
        ]
      : []),
    // {
    //   title: "개인이동",
    //   imageUrl: "/assets/menu/pm.png",
    //   description: "편리한 개인이동 수단 사용하기!",
    // },
  ];

  return items.length > 0 ? { title: "라운지", items } : null;
};

export const useGetCityLoungeItems = (tenantCd: string): Menu | null => {
  const { navigate } = useNavigate();

  const items: MenuItem[] = [
    ...(tenantCd === MAIN_TENANT_CD
      ? [
          {
            title: "모바일 상품권 교환소",
            imageUrl: "/assets/menu/coupon.png",
            description: "열심히 모은 포인트를 사용해서 다양한 쿠폰을 받아요!",
            onPress: () => navigate(Routes.store()),
          },
        ]
      : []),
    // {
    //   title: "개인이동",
    //   imageUrl: "/assets/menu/pm.png",
    //   description: "편리한 개인이동 수단 사용하기!",
    // },
  ];

  return items.length > 0 ? { title: "CT 라운지", items } : null;
};
