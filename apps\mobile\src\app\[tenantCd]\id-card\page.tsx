import { MainContainer } from "@/components/main-container";
import { Metadata } from "next";
import { IdCardContent } from "./content";

interface IdCardPageProps {
  params: Promise<{ tenantCd: string }>;
  searchParams: Promise<{ category: string | string[] | undefined }>;
}

export const generateMetadata = async ({
  searchParams,
}: IdCardPageProps): Promise<Metadata> => {
  const { category } = await searchParams;

  return { title: category === "pet" ? "반려동물신분증" : "모바일시민증" };
};

export default async function IdCardPage({
  params,
  searchParams,
}: Readonly<IdCardPageProps>) {
  const { tenantCd } = await params;
  const { category } = await searchParams;

  return (
    <MainContainer>
      <IdCardContent tenantId={tenantCd} initialCategory={category as string} />
    </MainContainer>
  );
}
