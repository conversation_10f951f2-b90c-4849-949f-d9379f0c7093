import {
  boolean,
  integer,
  pgTable,
  primaryKey,
  timestamp,
  varchar,
} from "drizzle-orm/pg-core";

export const termsGroup = pgTable("tb_terms_group", {
  termsCd: integer("terms_cd").notNull().primaryKey(),
  termsGroupTitle: varchar("terms_group_title", { length: 255 }),
  termsVer: varchar("terms_ver", { length: 10 }),
  valYn: boolean("val_yn").notNull(),
  regDt: timestamp("reg_dt").notNull(),
  chgDt: timestamp("chg_dt"),
});

export const termsData = pgTable(
  "tb_terms_data",
  {
    termsId: integer("terms_id").primaryKey().generatedAlwaysAsIdentity(),
    seq: integer("seq"),
    termsCd: integer("terms_cd").notNull(),
    termsTitle: varchar("terms_title", { length: 100 }),
    termsVer: varchar("terms_ver", { length: 10 }),
    termsSmry: varchar("terms_smry", { length: 255 }),
    termsConts: varchar("terms_conts", { length: 65535 }),
    milTypeId: varchar("mil_type_id", { length: 100 }),
    mustYn: boolean("must_yn").notNull(),
    valYn: boolean("val_yn").notNull(),
    privacyYn: boolean("privacy_yn").notNull(),
    regDt: timestamp("reg_dt").notNull(),
    chgDt: timestamp("chg_dt"),
  },
  (table) => [
    primaryKey({ columns: [table.termsId, table.termsCd, table.seq] }),
  ],
);

export const termsAgree = pgTable(
  "tb_terms_agree",
  {
    userDid: varchar("user_did", { length: 255 }).notNull(),
    termsCd: integer("terms_cd").notNull(),
    termsId: integer("terms_id").notNull(),
    agreVal: boolean("agre_val").notNull(),
    agreDt: timestamp("agre_dt").notNull(),
  },
  (table) => [
    primaryKey({ columns: [table.userDid, table.termsCd, table.termsId] }),
  ],
);
