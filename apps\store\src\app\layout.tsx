import type { Metadata, Viewport } from "next";
import "@workspace/ui/globals.css";
import "@workspace/ui/globals-mobile.css";
import QueryClientProvider from "@/services/providers/query-client-provider";
import { ModalProvider } from "@workspace/ui/providers/modal-provider";
import { GlobalBridgeProvider } from "@workspace/webview-state";
import { ReactNode } from "react";

export const metadata: Metadata = {
  title: "모바일 상품권 교환소",
};

export const viewport: Viewport = {
  themeColor: "#ffffff",
  width: "device-width",
  initialScale: 1,
  maximumScale: 1,
  userScalable: false,
  viewportFit: "cover",
};

export default function RootLayout({
  children,
}: Readonly<{ children: ReactNode }>) {
  return (
    <html lang="ko-KR">
      <body className="antialiased">
        <GlobalBridgeProvider appScheme="cw.store">
          <ModalProvider>
            <QueryClientProvider>{children}</QueryClientProvider>
          </ModalProvider>
        </GlobalBridgeProvider>
      </body>
    </html>
  );
}
