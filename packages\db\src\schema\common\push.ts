import {
  boolean,
  index,
  pgTable,
  timestamp,
  unique,
  varchar,
} from "drizzle-orm/pg-core";

export const pushTargetInfo = pgTable(
  "tb_push_target_info",
  {
    projectId: varchar("project_id", { length: 50 }).notNull(),
    trgtType: varchar("trgt_type", { length: 10 }),
    trgtToken: varchar("trgt_token", { length: 255 }).notNull().primaryKey(),
    mktYn: boolean("mkt_yn").default(false),
    notifYn: boolean("notif_yn").default(false),
    topicNm: varchar("topic_nm", { length: 255 }),
    userDid: varchar("user_did", { length: 255 }),
    clientId: varchar("client_id", { length: 255 }),
    regDt: timestamp("reg_dt").notNull(),
    updtDt: timestamp("updt_dt"),
    mktDt: timestamp("mkt_dt"),
    notifDt: timestamp("notif_dt"),
  },
  (table) => [
    unique("uq_user_project").on(table.userDid, table.projectId),
    index().on(table.projectId, table.userDid),
  ],
);
