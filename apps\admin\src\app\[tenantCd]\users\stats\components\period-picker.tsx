"use client";

import {
  applyDateRangeParams,
  DateRangePicker,
} from "@/components/date-range-picker";
import { DateRange } from "@/lib/utils";
import { format } from "date-fns";
import { useRouter } from "next/navigation";

export const PeriodPicker = ({ dateRange }: { dateRange: [Date, Date] }) => {
  const router = useRouter();

  const handleDateChange = (value: DateRange) => {
    const searchParams = new URLSearchParams();
    applyDateRangeParams(searchParams, "range", value);
    router.push(`?${searchParams.toString()}`);
  };
  return <DateRangePicker dateRange={dateRange} onChange={handleDateChange} />;
};
