import { cookies } from "next/headers";
import { cache } from "react";
import { decryptJWE } from "./jwt-utils";
import { decryptPlatformJWT } from "./platform-auth";
import { Session, SESSION_KEY } from "./types";

export const getPlatformTokenData = cache(async (token: string) => {
  try {
    const result = await decryptPlatformJWT(token);
    return result;
  } catch (error) {
    console.error("Error parsing platform JWT:", error);
    throw new Error("Invalid platform token");
  }
});

export const getSessionData = cache(async (token: string) => {
  try {
    const result = (await decryptJWE(token)) as Session;
    return result;
  } catch (error) {
    console.error("Error parsing session JWT:", error);
    throw new Error("Invalid session token");
  }
});

export const getSession = async () => {
  const cookiesStorage = await cookies();
  const sessionToken = cookiesStorage.get(SESSION_KEY)?.value;
  if (!sessionToken) return null;
  const session = await getSessionData(sessionToken);
  return session;
};

export const getTenant = async (session: Session, tenantCd: string | null) => {
  if (!tenantCd) return null;
  const tenant = session.tenants.find((tenant) => tenant.tenantCd === tenantCd);
  if (!tenant) throw new Error("Tenant not found");
  return tenant;
};
