import Header from "@/components/common/header";
import { readSuggest } from "@workspace/db/crud/living-lab/suggest";
import { Skeleton } from "@workspace/ui/components/skeleton";
import { getSession } from "@workspace/utils/auth/session";
import { notFound, redirect, RedirectType } from "next/navigation";
import { Suspense } from "react";
import { GuidePanel } from "../../components/common";
import { SuggestForm } from "../../write/write-suggest";

const EditForm = async ({
  tenantId,
  uid,
}: {
  tenantId: string;
  uid: number;
}) => {
  const result = await readSuggest(tenantId, uid);
  if (!result) notFound();
  return (
    <SuggestForm
      model={{
        suggestUid: result.uid,
        cateogry: result.category,
        title: result.title,
        content: result.content,
        files: result.files,
      }}
    />
  );
};

const EditFormSkeleton = () => {
  return (
    <div className="bg-[#FAFBFF] p-[20px]">
      <div className="mb-[10px]">
        <Skeleton className="h-[40px] w-full" />
      </div>
      <div className="mb-[10px]">
        <Skeleton className="h-[40px] w-full" />
      </div>
      <div className="mb-[10px]">
        <Skeleton className="h-[120px] w-full" />
      </div>
      <div className="mb-[20px]">
        <Skeleton className="h-[40px] w-full" />
      </div>
      <div className="my-[12px]">
        <Skeleton className="h-[52px] w-full rounded-[10px]" />
      </div>
    </div>
  );
};

export default async function SuggestEditPage({
  params,
}: {
  params: Promise<{ suggestUid: number }>;
}) {
  const session = await getSession();
  if (!session) redirect("/", RedirectType.replace);
  const { suggestUid } = await params;

  return (
    <main className="flex max-h-screen min-h-screen flex-col bg-[#FFFFFF]">
      <Header title="정책제안" parent={`/suggest/${suggestUid}`} />
      <GuidePanel type="suggest" />
      <Suspense fallback={<EditFormSkeleton />}>
        <EditForm tenantId={session.tenantCd} uid={suggestUid} />
      </Suspense>
    </main>
  );
}
