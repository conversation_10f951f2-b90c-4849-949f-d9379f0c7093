"use client";

import { useCallback } from "react";
import { use<PERSON>ridge<PERSON>oose } from "./bridge-provider";
import { NavigateRequest } from "./types";

export const useNavigate = () => {
  const { loose } = useBridgeLoose();

  const navigate = useCallback(
    async (navigateData: NavigateRequest) => {
      try {
        return await loose.navigate(navigateData);
      } catch (e) {
        console.warn(e, navigateData);
        throw e;
      }
    },
    [loose],
  );

  const dismissTo = useCallback(
    async (navigateData: NavigateRequest, reload?: boolean) => {
      try {
        return await loose.dismissTo(navigateData, reload);
      } catch (e) {
        console.warn(e, navigateData);
        throw e;
      }
    },
    [loose],
  );

  return { navigate, dismissTo };
};
