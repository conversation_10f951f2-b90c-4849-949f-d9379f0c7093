"use server";

import { ERROR_MESSAGES } from "@/lib/error-messages";
import {
  createSession,
  deleteSession,
  getSession,
  SessionPayload,
} from "@/lib/session";
import {
  addAdmin,
  AdminSignUpDto,
  checkAdminIdExists,
  getAdminInfo,
  getAdminTenants,
} from "@workspace/db/crud/admin/admin";
import {
  addAdminLog,
  addAdminLogTx,
} from "@workspace/db/crud/admin/admin-logs";
import { ADMIN_ACTION, ADMIN_ROLE } from "@workspace/db/schema/admin/admin";
import { getClientInfo, getClientIP } from "@workspace/utils/client-utils";
import { compare, genSalt, hash } from "bcrypt-ts";
import { redirect } from "next/navigation";

const isProduction = process.env.NODE_ENV !== "development";

const isAuthorizedRole = (role: string) => {
  return role !== ADMIN_ROLE.UNAUTHORIZED && role !== ADMIN_ROLE.BLOCKED;
};

const isAuthorizedAdmin = (admin: { isValid: string }) => {
  return admin.isValid === "Y";
};

export const login = async (id: string, password: string) => {
  const adminInfo = await getAdminInfo(id);
  if (!adminInfo || !(await compare(password, adminInfo.userPwd))) {
    return { error: ERROR_MESSAGES.AUTH.LOGIN_INVALID };
  }
  if (!isAuthorizedAdmin(adminInfo)) {
    return { error: ERROR_MESSAGES.AUTH.LOGIN_UNAUTHORIZED };
  }

  const tenants = await getAdminTenants(adminInfo.uid);
  const tenantList = tenants
    ?.map(({ adminRoles, tenants }) => ({
      tenantCd: tenants.tenantCd,
      tenantNm: tenants.tenantNm,
      role: adminRoles.userRole,
    }))
    .filter((tenant) => isAuthorizedRole(tenant.role));

  if (tenantList.length === 0) {
    return { error: ERROR_MESSAGES.AUTH.LOGIN_UNAUTHORIZED };
  }

  const payload: SessionPayload = {
    uid: adminInfo.uid,
    userNm: adminInfo.userNm,
    userDepr: adminInfo.userDepr,
    lastLoginDt: adminInfo.lastLoginDt,
    tenantCd: tenantList[0].tenantCd,
    tenants: tenantList,
  };
  await createSession(payload);
  await addAdminLog({
    tenantCd: payload.tenantCd,
    adminUid: adminInfo.uid,
    action: ADMIN_ACTION.AUTH_LOGIN,
    description: `로그인: ${id}`,
    clientInfo: await getClientInfo(),
  });
  redirect("/");
};

export const logout = async () => {
  const session = await getSession();
  if (!session) redirect("/auth/login");
  await addAdminLog({
    tenantCd: session.tenantCd,
    adminUid: session.uid,
    action: ADMIN_ACTION.AUTH_LOGOUT,
    description: `로그아웃: ${session.userNm}`,
    clientInfo: await getClientInfo(),
  });
  await deleteSession();
  redirect("/");
};

export const signUp = async (data: AdminSignUpDto) => {
  const clientInfo = await getClientInfo();
  const exists = await checkAdminIdExists(data.userId);
  if (exists) {
    return { error: ERROR_MESSAGES.AUTH.SIGNUP_DUPLICATE_ID };
  }

  const salt = await genSalt(10);
  const userPwd = await hash(data.userPwd, salt);
  try {
    const id = data.userId.trim().toLowerCase();
    if (id.length < (isProduction ? 4 : 1)) {
      return { error: ERROR_MESSAGES.AUTH.SIGNUP_ID_REQUIRED };
    }
    await addAdmin({ ...data, userPwd }, clientInfo);
    return { data: true };
  } catch (e) {
    return { error: ERROR_MESSAGES.AUTH.SIGNUP_FAILED };
  }
};

export const checkUserId = async (userId: string) => {
  try {
    const exists = await checkAdminIdExists(userId);
    return { data: exists };
  } catch (e) {
    return {
      error: ERROR_MESSAGES.AUTH.SIGNUP_ID_CHECK_FAILED,
    };
  }
};
