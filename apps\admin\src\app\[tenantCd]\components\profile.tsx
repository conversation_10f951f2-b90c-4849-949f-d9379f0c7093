"use client";

import { logout } from "@/actions/auth";
import { MESSAGES } from "@/lib/messages";
import { SessionPayload } from "@/lib/session";
import { createTenantPath, parseTenantPath } from "@/lib/tenant";
import { ADMIN_ROLE } from "@workspace/db/schema/admin/admin";
import { Avatar, AvatarFallback } from "@workspace/ui/components/avatar";
import { Button } from "@workspace/ui/components/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuRadioGroup,
  DropdownMenuRadioItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@workspace/ui/components/dropdown-menu";
import dayjs from "dayjs";
import { usePathname, useRouter } from "next/navigation";

export const Profile = ({
  session,
  role,
}: {
  session: SessionPayload;
  role: ADMIN_ROLE;
}) => {
  const router = useRouter();
  const pathname = usePathname();
  const [tenant, rest] = parseTenantPath(pathname);
  const handleChangeTenant = (newTenant: string) => {
    localStorage.setItem("tenantCd", newTenant);
    router.push(createTenantPath(newTenant, rest));
  };
  const roleName = MESSAGES.ROLE[role as keyof typeof MESSAGES.ROLE];
  return (
    <div>
      <DropdownMenu modal={false}>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" className="relative h-8 w-8 rounded-full">
            <Avatar className="h-8 w-8">
              <AvatarFallback>{session.userNm.substring(0, 2)}</AvatarFallback>
            </Avatar>
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" forceMount>
          <DropdownMenuLabel className="font-normal">
            <div className="flex flex-col space-y-1">
              <p className="text-sm font-medium leading-none">
                {session.userNm}
              </p>
              <p className="text-muted-foreground text-xs leading-none">
                {roleName}
              </p>
              <p className="text-muted-foreground pt-2 text-xs leading-none">
                최근접속일시 :
                {dayjs(session.lastLoginDt).format("YYYY.MM.DD. HH:mm:ss")}
              </p>
            </div>
          </DropdownMenuLabel>
          <DropdownMenuSeparator />
          <DropdownMenuRadioGroup
            value={tenant}
            onValueChange={handleChangeTenant}
          >
            {session.tenants.map((tenant) => (
              <DropdownMenuRadioItem
                key={tenant.tenantCd}
                value={tenant.tenantCd}
              >
                {tenant.tenantNm}
              </DropdownMenuRadioItem>
            ))}
          </DropdownMenuRadioGroup>
          <DropdownMenuSeparator />
          <DropdownMenuItem onClick={logout}>로그아웃</DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
};
