{"name": "@workspace/webview-state", "version": "1.0.0", "description": "", "dependencies": {"@webview-bridge/react": "^1.7.8", "@webview-bridge/web": "^1.7.8", "react": "catalog:react19", "react-dom": "catalog:react19", "zustand": "^5.0.1"}, "devDependencies": {"@types/react": "catalog:react19", "@types/react-dom": "catalog:react19", "@webview-bridge/types": "^1.7.7", "@workspace/eslint-config": "workspace:*", "@workspace/typescript-config": "workspace:*"}, "exports": {".": "./src/index.ts", "./types": "./src/types.ts"}}