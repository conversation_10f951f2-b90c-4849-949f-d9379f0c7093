"use client";

import { SUGGEST_CATEGORIES } from "@/app/utils/consts";
import { FilterSelect } from "@/components/filter-select";
import dayjs from "dayjs";
import { usePathname, useRouter } from "next/navigation";
import { useCallback } from "react";

export const SuggestFilter = ({
  category,
  year,
  selectableYears,
  count,
}: {
  category: string | undefined;
  year: number;
  selectableYears: number[];
  count: number;
}) => {
  const router = useRouter();
  const pathname = usePathname();

  const categories = SUGGEST_CATEGORIES.map((category) => ({
    name: category,
    value: category,
  }));
  const categoryName =
    category && SUGGEST_CATEGORIES.includes(category) ? category : undefined;
  const yearValue = selectableYears.includes(year) ? year : dayjs().year();

  const handleFilter = useCallback(
    (categoryValue: string | undefined, yearValue: number | undefined) => {
      const params = new URLSearchParams();
      if (categoryValue) {
        params.set("category", categoryValue);
      }
      if (yearValue) params.set("year", yearValue.toString());
      router.replace(`${pathname}?${params.toString()}`);
    },
    [pathname, router],
  );

  const handleCategoryChange = (newCategory: string | undefined) => {
    handleFilter(newCategory, year);
  };

  const handleYearChange = (newYear: number) => {
    handleFilter(category, newYear);
  };

  return (
    <FilterSelect
      category={categoryName}
      categories={categories}
      showYears={true}
      year={yearValue}
      selectableYears={selectableYears}
      count={count}
      handleCategoryChange={handleCategoryChange}
      handleYearChange={handleYearChange}
    />
  );
};
