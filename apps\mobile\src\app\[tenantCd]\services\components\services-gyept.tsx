import { useQueryPointBalance } from "@/actions/point/point-query";
import { commaizeNumber } from "@toss/utils";
import { Button } from "@workspace/ui/components/button";
import { Skeleton } from "@workspace/ui/components/skeleton";
import { cn } from "@workspace/ui/lib/utils";

export const GyeptGreenPoint = () => {
  const { data } = useQueryPointBalance();
  const items = data?.filter((item) => item.tenantCd === "gyept");

  const totalBalance = items?.reduce((acc, curr) => acc + curr.balance, 0);

  return (
    <Button variant="none" asChild>
      <div
        className={cn(
          "shadow-gray flex flex-col justify-between rounded-[10px] px-[16px] py-[12px] transition-transform active:scale-[0.97]",
          "bg-gradient-to-b from-[#FFFFFF] to-[#B3E3DF60]",
        )}
      >
        <div className="row flex items-center justify-between">
          <div>
            <p className="mb-[2px] text-[16px] font-semibold">
              그린플레이 포인트
            </p>
            <p className="text-[12px] text-[#636F7A]">
              이번달 적립된 그린플레이 포인트
            </p>
          </div>
          <div>
            <div className="flex flex-row items-center justify-end text-[18px] font-semibold">
              {totalBalance !== undefined ? (
                commaizeNumber(totalBalance)
              ) : (
                <Skeleton className="h-[16px] w-[60px]" />
              )}
              <span className="ml-[4px]">P</span>
            </div>
            <div className="h-[20px] rounded-[10px] bg-[#009DA5] px-[6px] py-[2px] text-[12px] text-[#ffffff]">
              확인하러가기
            </div>
          </div>
        </div>
      </div>
    </Button>
  );
};
