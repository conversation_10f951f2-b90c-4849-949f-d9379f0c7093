import {
  getPolicyList,
  readPolicyCount,
} from "@workspace/db/crud/common/points";
import { DataTableSkeleton } from "@workspace/ui/components/data-table";
import { Suspense } from "react";
import { AppContent } from "../../components/container";
import { TenantPageProps } from "../../layout";
import { PolicyPageClient } from "./components/policy-page-client";

const PolicyManageContent = async ({
  tenantCd,
  pageNumber,
  pageSize,
}: {
  tenantCd: string;
  pageNumber: number;
  pageSize: number;
}) => {
  const policyCount = await readPolicyCount(tenantCd);
  const policies = await getPolicyList(tenantCd, pageNumber, pageSize);

  return (
    <PolicyPageClient
      data={policies}
      count={policyCount}
      pageNumber={pageNumber}
      pageSize={pageSize}
    />
  );
};

export default async function PointPolicyPage({
  params,
  searchParams,
}: TenantPageProps) {
  const { tenantCd } = await params;
  const { page = "1" } = await searchParams;
  const pageNumber = parseInt(page as string, 10) ?? 1;
  const pageSize = 10;

  return (
    <AppContent title="포인트 전환 정책관리">
      <Suspense fallback={<DataTableSkeleton pageSize={pageSize} />}>
        <PolicyManageContent
          tenantCd={tenantCd}
          pageNumber={pageNumber}
          pageSize={pageSize}
        />
      </Suspense>
    </AppContent>
  );
}
