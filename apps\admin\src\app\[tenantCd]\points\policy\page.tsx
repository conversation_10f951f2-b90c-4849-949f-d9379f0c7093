"use client";

import {
  getP<PERSON>yList,
  PointPolicyItem,
  readPolicyCount,
} from "@workspace/db/crud/common/points";
import { Button } from "@workspace/ui/components/button";
import { DataTableSkeleton } from "@workspace/ui/components/data-table";
import { useEffect, useState } from "react";
import { AppContent } from "../../components/container";
import { TenantPageProps } from "../../layout";
import { PolicyFormDialog } from "./components/policy-form";
import { PolicyList } from "./components/policy-list";

const PolicyManageContent = ({
  tenantCd,
  pageNumber,
  pageSize,
}: {
  tenantCd: string;
  pageNumber: number;
  pageSize: number;
}) => {
  const [data, setData] = useState<PointPolicyItem[]>([]);
  const [count, setCount] = useState(0);
  const [loading, setLoading] = useState(true);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [dialogMode, setDialogMode] = useState<"create" | "edit">("create");
  const [selectedPolicy, setSelectedPolicy] = useState<PointPolicyItem | null>(
    null,
  );

  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      try {
        const [policyCount, policies] = await Promise.all([
          readPolicyCount(tenantCd),
          getPolicyList(tenantCd, pageNumber, pageSize),
        ]);
        setCount(policyCount);
        setData(policies);
      } catch (error) {
        console.error("Failed to fetch policy data:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [tenantCd, pageNumber, pageSize]);

  const handleCreateClick = () => {
    setDialogMode("create");
    setSelectedPolicy(null);
    setDialogOpen(true);
  };

  const handleEditClick = (policy: PointPolicyItem) => {
    setDialogMode("edit");
    setSelectedPolicy(policy);
    setDialogOpen(true);
  };

  if (loading) {
    return <DataTableSkeleton pageSize={pageSize} />;
  }

  return (
    <>
      <div className="mb-4 flex justify-end">
        <Button onClick={handleCreateClick}>등록</Button>
      </div>

      <PolicyList
        data={data}
        count={count}
        pageNumber={pageNumber}
        pageSize={pageSize}
        onEditClick={handleEditClick}
      />

      <PolicyFormDialog
        open={dialogOpen}
        onOpenChange={setDialogOpen}
        policy={selectedPolicy}
        mode={dialogMode}
      />
    </>
  );
};

export default function PointPolicyPage({
  params,
  searchParams,
}: TenantPageProps) {
  const [tenantCd, setTenantCd] = useState<string>("");
  const [pageNumber, setPageNumber] = useState(1);
  const pageSize = 10;

  useEffect(() => {
    const initializeParams = async () => {
      const resolvedParams = await params;
      const resolvedSearchParams = await searchParams;

      setTenantCd(resolvedParams.tenantCd);
      const page = resolvedSearchParams.page || "1";
      setPageNumber(parseInt(page as string, 10) ?? 1);
    };

    initializeParams();
  }, [params, searchParams]);

  if (!tenantCd) {
    return <DataTableSkeleton pageSize={pageSize} />;
  }

  return (
    <AppContent title="포인트 전환 정책관리">
      <PolicyManageContent
        tenantCd={tenantCd}
        pageNumber={pageNumber}
        pageSize={pageSize}
      />
    </AppContent>
  );
}
