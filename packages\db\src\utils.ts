import { add, format } from "date-fns";
import { AnyColumn, between, gte, lte, sql } from "drizzle-orm";
import { PgSelect } from "drizzle-orm/pg-core";

export const increment = (column: AnyColumn, value = 1) => {
  return sql`${column} + ${value}`;
};

export const betweenYear = (column: AnyColumn, year: number | undefined) => {
  return year
    ? between(column, new Date(`${year}-01-01`), new Date(`${year + 1}-01-01`))
    : undefined;
};

export const betweenDate = (
  column: AnyColumn,
  range: [Date?, Date?] | undefined,
) => {
  if (!range) return undefined;
  const endDate = range[1] ? add(range[1], { days: 1 }) : undefined;
  if (range[0] && endDate) {
    return between(column, range[0], endDate);
  } else if (range[0]) {
    return gte(column, range[0]);
  } else if (endDate) {
    return lte(column, endDate);
  }
  return undefined;
};

export const now = () => {
  return sql`now()`;
};

export const withPagination = async <T extends PgSelect>(
  qb: T,
  page = 1,
  pageSize = 10,
) => {
  return qb.limit(pageSize).offset((Math.max(1, page) - 1) * pageSize);
};

export const coalesce = <T extends AnyColumn, U>(column: T, value: U) =>
  sql`COALESCE(${column}, ${value})`.mapWith(column);
