import { SuggestItem } from "@/app/suggest/components/suggest-item";
import { readPollReferences } from "@workspace/db/crud/living-lab/poll";

export const PollReferences = async ({
  tenantId,
  pollUid,
}: {
  tenantId: string;
  pollUid: number;
}) => {
  const content = await readPollReferences(tenantId, pollUid);
  if (!content) return null;

  return (
    <div className="mx-[20px] my-[20px]">
      <div className="mb-[10px] text-[12px] text-[#808C98]">관련게시글</div>
      <div className="flex flex-col space-y-[40px]">
        {content.map((item) => (
          <SuggestItem key={item.uid} item={item} />
        ))}
      </div>
    </div>
  );
};
